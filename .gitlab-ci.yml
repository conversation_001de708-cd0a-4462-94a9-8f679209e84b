workflow:
  rules:
    - if: '$CI_COMMIT_TAG =~ /^v\d+\.\d+\.\d+$/'
      when: never
    - when: always

stages:
  - check
  - deploy

check:
  stage: check
  image: node:18-alpine
  script:
    - npm config set registry http://registry.npmmirror.com
    - npm install -g pnpm
    - pnpm install
    - pnpm check
  tags:
    - docker
  cache:
    paths:
      - node_modules/

# 测试环境部署
deploy_test:
  stage: deploy
  rules:
    - if: '$CI_COMMIT_REF_NAME == "dev"'
  script:
    - bash ./scripts/deploy.sh
  variables:
    IMAGE_NAME: slmc-front-mobile
    TAG: TEST-$CI_PIPELINE_ID
    DOCKER_REGISTRY: registry.cn-hangzhou.aliyuncs.com
    K8S_DEPLOY_URL: https://************:9443/k8s/clusters/c-fw94x/apis/apps/v1/namespaces/slmc/deployments/slmc-mobile
    K8S_TOKEN: token-phkng:2x6kgqpp44pl5pmchnjlhzlbr5rjjnn56h4kvmkll4kp45qrdtz7t6
    NUXT_PUBLIC_APP_ID: wxbc20021ec380cc27
    NUXT_PUBLIC_TAG: TEST-$CI_PIPELINE_ID
  needs:
    - check
  tags:
    - linux

    # 测试环境部署
deploy_prod:
  stage: deploy
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
  script:
    - bash ./scripts/deploy.sh
  variables:
    IMAGE_NAME: slmc-front-mobile-prod
    TAG: PROD-$CI_PIPELINE_ID
    DOCKER_REGISTRY: registry.cn-hangzhou.aliyuncs.com
    NUXT_PUBLIC_APP_ID: wx45b3f7454bd38a91
    NUXT_PUBLIC_TAG: PROD-$CI_PIPELINE_ID
  needs:
    - check
  tags:
    - linux
