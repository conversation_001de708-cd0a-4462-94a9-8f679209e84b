---
description: 
globs: *.vue
alwaysApply: false
---
# Figma设计稿自动还原规则
- 当用户发送格式为 https://www.figma.com/file/<FILE_KEY>?node-id=<NODE_ID> 的Figma链接时：

## 技术栈识别
- **框架**: Vue 3 + Nuxt.js + TypeScript  
- **样式**: UnoCSS + SCSS + Vant UI
- **移动端**: 设计稿基于375px宽度

## 代码生成要求

### 1. 文件结构
```vue
<script setup lang="ts">
// TypeScript逻辑
</script>

<template>
  <!-- Vue 3 Composition API 模板 -->
</template>

<style lang="scss" scoped>
/* SCSS样式，如需要 */
</style>
```

### 2. 样式编写规范

**UnoCSS优先**：
- 布局: `flex`, `grid`, `absolute`, `relative`, `fixed`
- 间距: `p-16px`, `m-8px`, `gap-12px` 
- 尺寸: `w-full`, `h-full`
- 文字: `text-14px`, `text-t-5`, `font-600`
- 颜色: `text-primary-6`, `bg-#F4F5F7`, `text-#1D2229`
- 圆角: `rd-10px`, `rd-full`
- 边框: `border-1px`, `border-solid`, `border-#E5E5E5`
- 不需要编写类似 tracking-4% leading-[1.8em] 的样式

**颜色系统**：
```scss
// 主色调
--primary-6: 0, 172, 151  // #00AC97
--primary-1: 230, 247, 245
--primary-2: 128, 214, 203
--primary-3: 51, 189, 172

// 文字颜色
--t-2: 201, 205, 212
--t-3: 134, 144, 156
--t-4: 78, 89, 105
--t-5: 29, 34, 41

// 背景色
--fill-1: 247, 248, 250
--fill-2: 242, 243, 245
```

### 3. 组件规范

**移动端适配**：
- 使用`safe-area-inset-bottom`适配底部安全区域
- 固定底部元素：`fixed left-0 right-0 bottom-0`
- 滚动容器：`overflow-y-auto`

**常用组件模式**：
```vue
<!-- 卡片布局 -->
<div class="bg-white rd-12px p-16px">
  <!-- 内容 -->
</div>

<!-- 底部固定按钮 -->
<base-fixed-bottom>
  <van-button class="w-343px h-48px" type="primary">
    确认
  </van-button>
</base-fixed-bottom>

<!-- 图标占位 -->
<div class="i-custom-icon-name w-24px h-24px"></div>
<!-- 或 -->
<img src="/placeholder-icon.svg" class="w-24px h-24px" alt="图标" />
```

### 4. 响应式处理
- 容器最大宽度：`max-w-375px mx-auto`
- 内容区域：`px-16px` (左右间距16px)
- 卡片间距：`gap-16px` 或 `mb-16px`

### 5. 字体处理
```scss
// DIN Pro字体
font-family: 'DINPro-Medium', sans-serif;
// 或使用UnoCSS
font-dinpro
```

### 6. 资源占位处理
- **SVG图标**: 使用 `<div>svg图标</div>`
- **图片**: 使用 `<div>img图片</div>`
- **背景图**: 使用 `background: url('/placeholder-bg.png') no-repeat center/cover;`

## 生成流程
1. **调用MCP**: 获取Figma设计稿信息
2. **分析布局**: 识别组件层级和布局模式  
3. **颜色匹配**: 映射到项目色彩系统
4. **样式转换**: 将设计稿尺寸转为UnoCSS类名
5. **代码生成**: 输出完整的Vue组件代码
6. **占位处理**: 为所有资源创建占位符

## 注意事项
- 严格遵循项目现有的命名约定和代码风格
- 优先使用UnoCSS，复杂样式使用SCSS
- 确保移动端适配和安全区域处理
- 所有图片和SVG使用占位符，不实际下载
- 保持代码简洁和可维护性