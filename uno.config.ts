import { readFileSync } from 'node:fs'

import { defineConfig, presetAttributify, presetIcons, presetUno } from 'unocss'
import transformerDirectives from '@unocss/transformer-directives'
import { presetScrollbarHide } from 'unocss-preset-scrollbar-hide'
import fg from 'fast-glob'

const customIcons: Record<string, string> = {

}

fg.sync('./app/assets/icons/**').forEach((file) => {
    const fileArr = file.split('/')
    fileArr.splice(0, 4)
    const name = fileArr.join('-').replace('.svg', '')
    customIcons[name] = readFileSync(file, 'utf-8')
})

export default defineConfig({
    presets: [
        presetAttributify(),
        presetUno(),
        presetIcons({
            scale: 1,
            collections: {
                custom: customIcons,
            },
        }),
        presetScrollbarHide(),
    ],
    shortcuts: {
        'base-background': 'bg-gray-bg h-full absolute inset-0 overflow-auto',
        'base-skeleton': 'bg-#fff h-full absolute inset-0 p-10px ',
        'divider': 'h-1px bg-fill-3',
    },
    rules: [
        [
            'shadow-button',
            {
                'box-shadow': '0px 2px 8px 0px rgba(0, 0, 0, 0.1)',
            },
        ],
        [
            'font-dinpro',
            {
                'font-family': '\'dinpro\', sans-serif',
            },
        ],
        [
            'font-ddinpro',
            {
                'font-family': '\'ddinpro\', sans-serif',
            },
        ],
        [
            'font-fangyuan',
            {
                'font-family': '\'fangyuan\', sans-serif',
            },
        ],
    ],
    theme: {
        colors: {
            'primary-1': 'rgb(var(--primary-1),<alpha-value>)',
            'primary-2': 'rgb(var(--primary-2),<alpha-value>)',
            'primary-3': 'rgb(var(--primary-3),<alpha-value>)',
            'primary-6': 'rgb(var(--primary-6),<alpha-value>)',
            'primary-7': 'rgb(var(--primary-7),<alpha-value>)',
            't-1': 'rgb(var(--t-1),<alpha-value>)',
            't-2': 'rgb(var(--t-2),<alpha-value>)',
            't-3': 'rgb(var(--t-3),<alpha-value>)',
            't-4': 'rgb(var(--t-4),<alpha-value>)',
            't-5': 'rgb(var(--t-5),<alpha-value>)',
            'success-1': 'rgb(var(--success-1),<alpha-value>)',
            'success-2': 'rgb(var(--success-2),<alpha-value>)',
            'success-3': 'rgb(var(--success-3),<alpha-value>)',
            'success-6': 'rgb(var(--success-6),<alpha-value>)',
            'success-7': 'rgb(var(--success-7),<alpha-value>)',
            'warning-1': 'rgb(var(--warning-1),<alpha-value>)',
            'warning-2': 'rgb(var(--warning-2),<alpha-value>)',
            'warning-3': 'rgb(var(--warning-3),<alpha-value>)',
            'warning-6': 'rgb(var(--warning-6),<alpha-value>)',
            'warning-7': 'rgb(var(--warning-7),<alpha-value>)',
            'danger-1': 'rgb(var(--danger-1),<alpha-value>)',
            'danger-2': 'rgb(var(--danger-2),<alpha-value>)',
            'danger-3': 'rgb(var(--danger-3),<alpha-value>)',
            'danger-6': 'rgb(var(--danger-6),<alpha-value>)',
            'danger-7': 'rgb(var(--danger-7),<alpha-value>)',
            'fill-1': 'rgb(var(--fill-1),<alpha-value>)',
            'fill-2': 'rgb(var(--fill-2),<alpha-value>)',
            'fill-3': 'rgb(var(--fill-3),<alpha-value>)',
            'fill-4': 'rgb(var(--fill-4),<alpha-value>)',
        },
    },
    transformers: [
        transformerDirectives({
            applyVariable: ['--uno'],
        }),
    ],
})
