export async function useAgeAndGender() {
    const ageAndGender = ref({
        age: 0,
        gender: 'male' as 'male' | 'female',
    })

    async function init() {
        const { userInfo } = useUserStore()

        if (userInfo?.idCard) {
            ageAndGender.value = parseIDCard(userInfo.idCard)
        }
        else if (userInfo?.gender && userInfo?.age) {
            ageAndGender.value = {
                age: userInfo.age,
                gender: userInfo.gender === '男' ? 'male' : 'female',
            }
        }
        else {
            const { results: questionListResults } = await useWrapFetch<BaseResponse<QuestionList[]>>('/user/question/list')
            const reportInterpretationId = questionListResults[0]!.reportInterpretationId
            const { results: questionDetail } = await useWrapFetch<BaseResponse<EvaluateQuestion>>(`/user/getSecondaryEvaluationByReportInterpretationId/${reportInterpretationId}`)

            if (questionDetail.preliminaryEvaluationQuestionResultId) {
                const { results: preliminary } = await useWrapFetch<BaseResponse<QuestionResult>>(`/question-result/${questionDetail.preliminaryEvaluationQuestionResultId}`)

                const parsed = JSON.parse(preliminary.questionResult)

                ageAndGender.value = {
                    age: parsed['2'],
                    gender: parsed['1'] === '1' ? 'male' : 'female',
                }
            }
        }
    }

    onMounted(async () => {
        init()
    })

    return {
        ageAndGender,
    }
}
