import { v4 as uuidv4 } from 'uuid'

export const useFoodScanStore = defineStore('foodScanStore', () => {
    const foodList = ref<FoodItem[]>([])
    const mealEvaluation = ref('')
    const isFoodVisible = ref(false)
    const isScanVisible = ref(false)
    const isFoodSheetShow = ref(false)

    const imgBlobUrl = ref('')
    const imgUrl = ref('')

    const keepFoodAlive = ref(false)

    // 添加全局锁防止多个组件实例重复调用上传接口
    const isUploadProcessing = ref(false)

    function resetFoodScan() {
        foodList.value = []
        mealEvaluation.value = ''
        isFoodVisible.value = false
        isScanVisible.value = false
        isFoodSheetShow.value = false
        imgBlobUrl.value = ''
        imgUrl.value = ''
        isUploadProcessing.value = false
    }

    // 统一处理 imgUrl 变化，避免多个组件实例重复调用
    const appConfig = useAppConfig()
    const isDev = appConfig.__IS_DEV__

    whenever(imgUrl, async (val) => {
        if (!val || val.trim() === '') {
            return
        }

        // 使用全局锁防止并发调用
        if (isUploadProcessing.value) {
            return
        }

        isUploadProcessing.value = true

        try {
            const filePath = isDev ? `https://test.slmc.top${val}` : `${window.location.origin}${val}`

            const { results } = await useWrapFetch<BaseResponse<{
                food: FoodItem[]
                mealEvaluation: string
            }>>('/checkInCustomerMeal/uploadImage', {
                method: 'post',
                params: {
                    filePath,
                },
            })

            mealEvaluation.value = results.mealEvaluation

            foodList.value = (results.food || []).map(item => ({
                ...item,
                uuid: uuidv4().split('-')[0]!,
                mealPicture: '',
                weight: extractNumber(item.weight) || 0,
                calories: extractNumber(item.calories) || 0,
                carbohydrates: extractNumber(item.carbohydrates) || 0,
                protein: extractNumber(item.protein) || 0,
                fat: extractNumber(item.fat) || 0,
                dietaryFiber: extractNumber(item.dietaryFiber) || 0,
                source: 'photo',
            }))
            isScanVisible.value = false
            isFoodSheetShow.value = true
        } catch (error) {
            console.error('上传图片处理失败:', error)
        } finally {
            // 延迟释放锁，防止极快的重复调用
            setTimeout(() => {
                isUploadProcessing.value = false
            }, 1000)
        }
    })

    return {
        foodList,
        mealEvaluation,
        isScanVisible,
        imgBlobUrl,
        imgUrl,
        isFoodVisible,
        keepFoodAlive,
        isFoodSheetShow,
        isUploadProcessing,
        resetFoodScan,
    }
})
