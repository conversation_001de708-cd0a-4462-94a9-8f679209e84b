export interface DiaryItem {
    id: number
    customerId: string
    weight: number
    recordTime: string
    content: string
    pictures: string
    is_deleted: number
    createTime: string
    updateTime: string
}

export function useDiary() {
    const dayjs = useDayjs()
    const monthDiaryList = ref<DiaryItem[]>([])
    const isEmpty = computed(() => monthDiaryList.value.length === 0)

    function parseDiaryContent(content: string): DiaryContent {
        try {
            return JSON.parse(content)
        } catch {
            return {
                content: content || '',
            }
        }
    }

    function handleImagePreview(pictures: string, startPosition: number = 0) {
        try {
            const imageList = JSON.parse(pictures)
            if (Array.isArray(imageList)) {
                showImagePreview({
                    images: imageList.map((item: { url: string }) => item.url),
                    startPosition,
                })
            }
        } catch (error) {
            console.error('解析图片数据失败:', error)
        }
    }

    async function fetchDiaryList(startTime: string, endTime: string) {
        try {
            const { results } = await useWrapFetch<BaseResponse<DiaryItem[]>>('/diary/list', {
                params: {
                    startTime,
                    endTime,
                },
            })
            if (results) {
                monthDiaryList.value = results
            }
        } catch (error) {
            console.error('获取日记列表失败:', error)
        }
    }

    return {
        dayjs,
        monthDiaryList,
        isEmpty,
        fetchDiaryList,
        handleImagePreview,
        parseDiaryContent,
    }
}
