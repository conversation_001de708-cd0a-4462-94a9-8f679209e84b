import lowGiIcon from '~/assets/images/checkin/diet-pattern/low-gi.png'
import lowFatIcon from '~/assets/images/checkin/diet-pattern/low-fat.png'
import lowPurineIcon from '~/assets/images/checkin/diet-pattern/low-purine.png'
import jiangnanIcon from '~/assets/images/checkin/diet-pattern/jiangnan.png'
import dashIcon from '~/assets/images/checkin/diet-pattern/dash.png'
import mediterraneanIcon from '~/assets/images/checkin/diet-pattern/mediterranean.png'
import highProteinIcon from '~/assets/images/checkin/diet-pattern/high-protein.png'
import lowGiDetail1Icon from '~/assets/images/checkin/diet-pattern/low-gi-detail-1.svg'
import lowGiDetail2Icon from '~/assets/images/checkin/diet-pattern/low-gi-detail-2.svg'
import lowGiDetail3Icon from '~/assets/images/checkin/diet-pattern/low-gi-detail-3.svg'
import lowFatDetail1Icon from '~/assets/images/checkin/diet-pattern/low-fat-detail-1.svg'
import lowFatDetail2Icon from '~/assets/images/checkin/diet-pattern/low-fat-detail-2.svg'
import lowFatDetail3Icon from '~/assets/images/checkin/diet-pattern/low-fat-detail-3.svg'
import lowFatDetail4Icon from '~/assets/images/checkin/diet-pattern/low-fat-detail-4.svg'
import lowPurineDetail1Icon from '~/assets/images/checkin/diet-pattern/low-purine-detail-1.svg'
import lowPurineDetail2Icon from '~/assets/images/checkin/diet-pattern/low-purine-detail-2.svg'
import lowPurineDetail3Icon from '~/assets/images/checkin/diet-pattern/low-purine-detail-3.svg'
import lowPurineDetail4Icon from '~/assets/images/checkin/diet-pattern/low-purine-detail-4.svg'
import jiangnanDetail1Icon from '~/assets/images/checkin/diet-pattern/jiangnan-detail-1.svg'
import jiangnanDetail2Icon from '~/assets/images/checkin/diet-pattern/jiangnan-detail-2.svg'
import jiangnanDetail3Icon from '~/assets/images/checkin/diet-pattern/jiangnan-detail-3.svg'
import jiangnanDetail4Icon from '~/assets/images/checkin/diet-pattern/jiangnan-detail-4.svg'
import dashDetail1Icon from '~/assets/images/checkin/diet-pattern/dash-detail-1.svg'
import dashDetail2Icon from '~/assets/images/checkin/diet-pattern/dash-detail-2.svg'
import dashDetail3Icon from '~/assets/images/checkin/diet-pattern/dash-detail-3.svg'
import dashDetail4Icon from '~/assets/images/checkin/diet-pattern/dash-detail-4.svg'
import dashDetail5Icon from '~/assets/images/checkin/diet-pattern/dash-detail-5.svg'
import mediterraneanDetail1Icon from '~/assets/images/checkin/diet-pattern/mediterranean-detail-1.svg'
import mediterraneanDetail2Icon from '~/assets/images/checkin/diet-pattern/mediterranean-detail-2.svg'
import mediterraneanDetail3Icon from '~/assets/images/checkin/diet-pattern/mediterranean-detail-3.svg'
import mediterraneanDetail4Icon from '~/assets/images/checkin/diet-pattern/mediterranean-detail-4.svg'
import highProteinDetail1Icon from '~/assets/images/checkin/diet-pattern/high-protein-detail-1.svg'
import highProteinDetail2Icon from '~/assets/images/checkin/diet-pattern/high-protein-detail-2.svg'
import highProteinDetail3Icon from '~/assets/images/checkin/diet-pattern/high-protein-detail-3.svg'

export type PatternId =
    | 'low-gi'
    | 'low-fat'
    | 'low-purine'
    | 'jiangnan'
    | 'dash'
    | 'mediterranean'
    | 'high-protein'

export const patternIdMap = {
    低GI饮食: 'low-gi',
    低脂饮食: 'low-fat',
    低碳水化合物膳食饮食: 'low-purine',
    江南饮食: 'jiangnan',
    DASH饮食: 'dash',
    地中海饮食: 'mediterranean',
    高蛋白饮食: 'high-protein',
}

export interface DietPattern {
    id: PatternId
    title: string
    tag: string
    decorationColor: string
    icon: string
    cardBackgroundColor: string
    isActive?: boolean
    definition: string
    keyPoints: string[]
    clinicalValidation: string[]
    suitablePopulation: string[]
    detailIcons: string[]
}

export function useDietPatterns() {
    function createPatterns(activeId?: PatternId): DietPattern[] {
        return [
            {
                id: 'low-gi',
                title: '低GI饮食',
                tag: '稳糖缓释、控糖首选',
                decorationColor: '#4CA465',
                icon: lowGiIcon,
                cardBackgroundColor: '#f0fcf5',
                isActive: activeId === 'low-gi',
                definition:
                    '低GI饮食模式是一种根据食物的升糖指数（GI）来选择食物的饮食方式，旨在通过选择低GI食物来控制血糖水平，帮助减肥和改善健康。',
                keyPoints: [
                    '平稳餐后血糖：低 GI 食物消化吸收慢，血糖峰值低且下降平缓。',
                    '提高脂肪氧化：相对减少碳水化合物依赖度，提高脂肪消耗率。',
                    '延缓饥饿：富含膳食纤维，更"抗饿"。',
                    '防止减肥反弹：保持基础代谢率，减少体重回升。',
                ],
                clinicalValidation: [
                    '研究表明，对于糖尿病患者，低GI食物在一天内引起的血糖波动更小，可有效降低糖化血红蛋白、空腹血糖、餐后2h血糖，增加胰岛素敏感性',
                    '坚持低GI膳食益于控制糖尿病患者的并发症',
                ],
                detailIcons: [
                    lowGiDetail1Icon,
                    lowGiDetail2Icon,
                    lowGiDetail3Icon,
                ],
                suitablePopulation: [
                    '适合各类需要减重的人群，尤其是存在糖尿病或胰岛素抵抗的超重/肥胖人群。',
                ],
            },
            {
                id: 'low-fat',
                title: '低脂饮食',
                tag: '控脂减负、轻养代谢',
                decorationColor: '#4C96CA',
                icon: lowFatIcon,
                cardBackgroundColor: '#f0f8fc',
                isActive: activeId === 'low-fat',
                definition:
                    '低脂饮食是指膳食脂肪占膳食总热量的30%以下，或全天脂肪摄入量小于50克的饮食方式。这种饮食方式主要用于治疗或改善因脂肪水解、吸收、运转及代谢不正常所引发的症状。',
                keyPoints: [
                    '减少饱和脂肪和反式脂肪的摄入：例如避免肥肉、油炸食品、全脂乳制品等。',
                    '增加富含不饱和脂肪的摄入：比如鱼类、坚果、橄榄油等。',
                    '多食用低脂食物：如蔬菜、水果、全谷物和瘦肉。',
                ],
                detailIcons: [
                    lowFatDetail1Icon,
                    lowFatDetail2Icon,
                    lowFatDetail3Icon,
                    lowFatDetail4Icon,
                ],
                clinicalValidation: [
                    '降低血脂水平',
                    '有助于体重控制',
                    '改善心血管健康',
                    '调节代谢指标',
                ],
                suitablePopulation: [
                    '适用于肥胖、高血脂、冠心病、肝脏疾病、胆囊炎等人群',
                    '注意：长期低脂饮食可能导致脂溶性维生素（如维生素A、D、E、K）缺乏，需及时补充。',
                ],
            },
            {
                id: 'low-purine',
                title: '低碳水化合物膳食饮食',
                tag: '限碳燃脂、速效减重',
                decorationColor: '#4C96CA',
                icon: lowPurineIcon,
                cardBackgroundColor: '#f0f8fc',
                isActive: activeId === 'low-purine',
                definition:
                    '低碳水化合物膳食饮食是限制全天膳食中嘌呤的摄入量在150~250mg的饮食方案。低嘌呤主要针对嘌呤代谢异常，尤其是痛风和高尿酸血症患者，强调限制高嘌呤食物入，如动物内脏、海鲜、肉类等，同时增加低嘌呤食物如蔬菜、水果、全谷物例，以维持体内嘌呤代谢平衡，减少尿酸生成。',
                keyPoints: [
                    '避免高嘌呤食物——动物内脏如肝、肾、心等，嘌呤含量普遍高于普通肉类，应尽量避免选择。鸡蛋蛋白、牛奶等嘌呤含量较低，可安心食用。',
                    '适量食用植物性嘌呤 ——虽然大豆嘌呤含量略高于瘦肉和鱼类，但植物性食物中的嘌呤人体利用率低，豆腐、豆干等豆制品在加工后嘌呤含量有所降低，可适量食用。',
                    '减少血糖波动 ——宜选择低血糖指数的碳水化合物类食物，每天全谷物食物不低于主食量的30%，膳食纤维摄入量达到25~30g。',
                    '考虑个体差异——不同个体对食物的反应程度不同，有痛风发作病史的人群在遵循上述原则基础上，要尽量避免既往诱发痛风发作的食物。',
                ],
                clinicalValidation: [
                    '降低尿酸水平',
                    '减少痛风发作频率',
                    '联合药物治疗的效果提升',
                    '改善代谢指标',
                ],
                detailIcons: [
                    lowPurineDetail1Icon,
                    lowPurineDetail2Icon,
                    lowPurineDetail3Icon,
                    lowPurineDetail4Icon,
                ],
                suitablePopulation: [
                    '痛风',
                    '高尿酸血症',
                    '糖尿病',
                    '肾功能不全等人群',
                ],
            },
            {
                id: 'jiangnan',
                title: '江南饮食',
                tag: '清淡营养、中式健康',
                decorationColor: '#6CCA4C',
                icon: jiangnanIcon,
                cardBackgroundColor: '#f7fcf0',
                isActive: activeId === 'jiangnan',
                definition:
                    '江南饮食指以我国长江中下游浙江、上海、江苏等地为代表的一种膳食模式，也称为"包邮区膳食"。江南饮食崇尚自然，顺应时序，不时不食，以稻米为主食，增加粗杂粮；多食新鲜蔬菜水果；以植物油为主；提倡蒸、煮、炖、煨等低温烹饪方式；白肉多，减少红肉，多食豆制品。',
                keyPoints: [
                    '多样主食：粗杂粮（小米、黑米、燕麦、红薯、杂豆等）与精米轮换。',
                    '低温烹饪：菜籽油、花生油等植物油为主，少炝锅。',
                    '水产丰富，少红肉：多吃鱼虾，适量猪肉；减少牛羊等红肉。',
                    '蔬果充足：建议每天 300–500 g 蔬菜、200–350 g 水果。',
                    '豆制品、坚果与奶类：优质蛋白的重要来源。',
                    '少糖：江南口味偏甜，需控制糖用量。',
                ],
                clinicalValidation: [
                    '流行病学数据显示可降低超重、肥胖、2 型糖尿病及代谢性疾病风险。',
                    '与地中海饮食理念相似，更易于国人接受。',
                ],
                detailIcons: [
                    jiangnanDetail1Icon,
                    jiangnanDetail2Icon,
                    jiangnanDetail3Icon,
                    jiangnanDetail4Icon,
                ],
                suitablePopulation: [
                    '普通健康人群，尤其注重清淡、均衡和食材多样性',
                    '需控制体重或血糖者',
                    '注意：需留意对甜味偏好造成的精制糖摄入过量问题',
                ],
            },
            {
                id: 'dash',
                title: 'DASH饮食',
                tag: '稳压控盐、护心利肾',
                decorationColor: '#E4BD50',
                icon: dashIcon,
                cardBackgroundColor: '#fcf9f1',
                isActive: activeId === 'dash',
                definition:
                    'DASH饮食（得舒饮食）是一种专门设计用于降低高血压和改善心脏代谢健康的饮食模式。它强调高钾、高镁、高钙、低钠的均衡饮食，并富含膳食纤维和优质蛋白。',
                keyPoints: [
                    '高钾、高镁、高钙 —— 促进血管舒张，帮助降低血压。',
                    '低钠 —— 限制每日钠摄入，有助于减少水钠潴留，降低血压。',
                    '富含膳食纤维 —— 促进血糖稳定，减少动脉粥样硬化风险。',
                    '富含优质蛋白 —— 提供饱腹感，促进代谢健康。',
                    '限制饱和脂肪和精制糖 —— 降低心血管疾病风险。',
                ],
                clinicalValidation: [
                    '降血压效果显著',
                    '改善血脂',
                    '稳定血糖，预防糖尿病',
                    '有助于减重',
                    '降低慢性疾病风险',
                ],
                detailIcons: [
                    dashDetail1Icon,
                    dashDetail2Icon,
                    dashDetail3Icon,
                    dashDetail4Icon,
                    dashDetail5Icon,
                ],
                suitablePopulation: [
                    '高血压患者',
                    '高血脂人群',
                    '肥胖/超重人群',
                    '糖尿病患者',
                    '普通健康人群（降低心血管疾病风险）',
                ],
            },
            {
                id: 'mediterranean',
                title: '地中海饮食',
                tag: '抗炎护心、健康常备',
                decorationColor: '#E4BD50',
                icon: mediterraneanIcon,
                cardBackgroundColor: '#fcf9f1',
                isActive: activeId === 'mediterranean',
                definition:
                    '以植物性食物为主，包括全谷类 、豆类 、蔬菜 、水果 、坚果等；鱼 、家禽 、蛋 、乳制品适量， 红肉及其产品少量；食用油主要是橄榄油；适量饮红葡萄酒。',
                keyPoints: [
                    '富含不饱和脂肪酸：橄榄油、鱼类富含 omega-3，有助于降低血脂、抗炎。',
                    '丰富抗氧化剂：蔬果、坚果含多种维生素、酚类物质，减轻氧化应激。',
                    '低 GI 食物：全谷物与蔬菜帮助稳定血糖、控制胰岛素水平。',
                    '适量红酒：含多酚，适度饮用可提升心血管健康，但不宜过量。',
                ],
                clinicalValidation: [
                    '有助于体重控制和减肥',
                    '明显改善心血管健康',
                    '有助于调节血糖和预防2型糖尿病',
                    '改善代谢综合征，降低相关慢性病风险',
                ],
                detailIcons: [
                    mediterraneanDetail1Icon,
                    mediterraneanDetail2Icon,
                    mediterraneanDetail3Icon,
                    mediterraneanDetail4Icon,
                ],
                suitablePopulation: [
                    '心血管疾病高风险人群',
                    '代谢综合症和糖尿病患者',
                    '追求长寿与整体健康的人群',
                    '注重生活质量和健康饮食习惯的人群',
                    '注意：️不适合消化道功能欠缺者 如胃溃疡、短肠综合症等人群，难以吸收太多植物性食物，长期食用会导致营养不良',
                ],
            },
            {
                id: 'high-protein',
                title: '高蛋白饮食',
                tag: '增肌控重、短期减重',
                decorationColor: '#AD3C19',
                icon: highProteinIcon,
                cardBackgroundColor: '#fcf4f0',
                isActive: activeId === 'high-protein',
                definition:
                    '高蛋白膳食是在国际上非常流行的快速减重方式，这种膳食模式对单纯性肥胖患者来说更有利于减轻体重，并有利于控制减重后体重的反弹。具体指每日蛋白量超过每日总能量的20%，但一般不高于35%的膳食模式。',
                keyPoints: [
                    '适度提高蛋白质：动物蛋白与植物蛋白并重，保证氨基酸谱全面。',
                    '营养均衡：仍需搭配适量碳水化合物和健康脂肪。',
                    '关注肾脏负担：肾功能异常者谨慎使用；定期监测肾功能。',
                    '个体化调整：不同人群蛋白质需求量差异大，需在专业人士指导下进行调整。',
                ],
                clinicalValidation: [
                    '有助于体重管理与饱腹感提升',
                    '有助于肌肉合成与维持',
                    '有助于改善新陈代谢与血糖调控',
                ],
                detailIcons: [
                    highProteinDetail1Icon,
                    highProteinDetail2Icon,
                    highProteinDetail3Icon,
                ],
                suitablePopulation: [
                    '需要短期体重管理者',
                    '注意：肾功能异常、痛风患者慎选',
                ],
            },
        ]
    }

    function getPatternById(id: PatternId): DietPattern {
        const found = createPatterns().find((pattern) => {
            return pattern.id === id
        })
        if (!found) {
            throw new Error(`未找到ID为"${id}"的饮食模式`)
        }
        return found
    }

    function getPatternIdFromTitle(title: string | undefined): PatternId {
        if (!title) return 'mediterranean'

        const pattern = createPatterns().find(p => p.title === title)
        return pattern?.id || 'mediterranean'
    }

    return {
        createPatterns,
        getPatternById,
        getPatternIdFromTitle,
    }
}
