export function useBmiTips(rootBmi?: string) {
    const { preliminaryResult } = useSurveyContent()

    const temp1 = '根据您的问卷结果显示，可能存在脂肪肝风险。'
    const temp2 = '请保持良好的生活习惯，定期体检。'
    const bmi = ref<number | null>(null)

    const bmiTips = ref('')

    watch(preliminaryResult, () => {
        const parsed = JSON.parse(preliminaryResult.value)

        if (rootBmi) {
            bmi.value = Number.parseFloat(rootBmi)
        } else {
            bmi.value = Number.parseFloat(parsed['5'])
        }

        const isQuestionAbnormal
            = parsed['6'] === '1'
                || parsed['7'] === '1'
                || parsed['8'] === '1'
                || parsed['9'] === '1'
                || parsed['12'] === '1'

        if (bmi.value && bmi.value < 24) {
            if (isQuestionAbnormal) {
                bmiTips.value = temp1
            } else {
                bmiTips.value = temp2
            }
        } else if (bmi.value && bmi.value >= 24) {
            bmiTips.value = temp1
        }
    })

    return {
        bmiTips,
        bmi,
    }
}
