export const useUserStateStore = defineStore('userStateStore', () => {
    const userState = ref<string>()

    const hospitalId = ref<'slmc' | 'nanjing' | null>(null)
    const noPay = ref(false)

    async function getUserState() {
        try {
            if (!userState.value) {
                const { results } = await useWrapFetch<BaseResponse<string>>('/user/getState')

                userState.value = results
            }

            const parsed = JSON.parse(window.atob(userState.value))

            if (parsed.inviteId === '5200') {
                hospitalId.value = 'nanjing'
            } else {
                hospitalId.value = 'slmc'
            }

            if (parsed.noPay) {
                noPay.value = true
            }
        } catch (error) {
            hospitalId.value = 'slmc'
            console.error(error)
        } finally {
            if (!hospitalId.value) {
                hospitalId.value = 'slmc'
            }
        }
    }

    function reset() {
        userState.value = undefined
        hospitalId.value = null
        noPay.value = false
    }

    return {
        userState,
        getUserState,
        hospitalId,
        noPay,
        reset,
    }
}, {
    persist: {
        storage: piniaPluginPersistedstate.localStorage(),
    },
})
