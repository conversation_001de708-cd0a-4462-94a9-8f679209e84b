export function useTimer() {
    const dayjs = useDayjs()
    const progress = ref(0)
    const isReachEndTime = ref(false)

    const passedTime = ref({
        hour: '00',
        minute: '00',
        second: '00',
    })

    const remainingTime = ref({
        hour: '00',
        minute: '00',
        second: '00',
    })

    let startTime = ''
    let endTime = ''

    function setTime(start: string, end: string) {
        startTime = start
        endTime = end
        startLoop()
    }

    function update() {
        const diffSeconds = dayjs().diff(dayjs(startTime), 'second')
        if (!diffSeconds)
            return
        const hours = Math.floor(diffSeconds / 3600)
        const minutes = Math.floor((diffSeconds % 3600) / 60)
        const seconds = diffSeconds % 60

        passedTime.value = {
            hour: String(hours).padStart(2, '0'),
            minute: String(minutes).padStart(2, '0'),
            second: String(seconds).padStart(2, '0'),
        }

        if (dayjs().isBefore(dayjs(endTime))) {
            isReachEndTime.value = false
            // 计算剩余时间
            const remainingSeconds = dayjs(endTime).diff(dayjs(), 'second')
            const remainingHours = Math.floor(remainingSeconds / 3600)
            const remainingMinutes = Math.floor((remainingSeconds % 3600) / 60)
            const remainingSecond = remainingSeconds % 60

            remainingTime.value = {
                hour: String(remainingHours).padStart(2, '0'),
                minute: String(remainingMinutes).padStart(2, '0'),
                second: String(remainingSecond).padStart(2, '0'),
            }

            // 计算进度百分比
            const totalDuration = dayjs(endTime).diff(dayjs(startTime), 'second')
            const elapsedDuration = dayjs().diff(dayjs(startTime), 'second')
            progress.value = Math.min(Math.round((elapsedDuration / totalDuration) * 100), 100)
        } else {
            isReachEndTime.value = true
            progress.value = 100
            // 计算超时时间
            const overtimeSeconds = dayjs().diff(dayjs(endTime), 'second')
            const overtimeHours = Math.floor(overtimeSeconds / 3600)
            const overtimeMinutes = Math.floor((overtimeSeconds % 3600) / 60)
            const overtimeSecond = overtimeSeconds % 60

            remainingTime.value = {
                hour: String(overtimeHours).padStart(2, '0'),
                minute: String(overtimeMinutes).padStart(2, '0'),
                second: String(overtimeSecond).padStart(2, '0'),
            }
        }
    }

    let animationFrameId: number | null = null
    let lastUpdate = 0 // 记录上次更新时间

    function updateLoop(timestamp: number) {
        // 检查是否经过了1秒
        if (timestamp - lastUpdate >= 1000) {
            update()
            lastUpdate = timestamp
        }
        animationFrameId = requestAnimationFrame(updateLoop)
    }

    function startLoop() {
        lastUpdate = performance.now()
        animationFrameId = requestAnimationFrame(updateLoop)
    }

    onUnmounted(() => {
        if (animationFrameId) {
            cancelAnimationFrame(animationFrameId)
            animationFrameId = null
        }
    })

    return {
        progress,
        setTime,
        passedTime,
        remainingTime,
        isReachEndTime,
    }
}
