import ID from 'idcard'

export function useProfileFormValidate(profileForm: HostProfileForm) {
    if (!profileForm.name) {
        showToast('请输入姓名')
        return false
    }

    // if (!profileForm.phone) {
    //     showToast('请输入手机号')
    //     return false
    // }

    // if (!profileForm.idCard) {
    //     showToast('请输入身份证号')
    //     return false
    // }

    if (profileForm.idCard && !ID.verify(profileForm.idCard.toString())) {
        showToast('请输入正确的身份证号')
        return false
    }

    return true
}
