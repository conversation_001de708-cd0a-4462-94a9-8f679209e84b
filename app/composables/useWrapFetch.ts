import { showFailToast } from 'vant'

export const useWrapFetch = $fetch.create({
    baseURL: '/api',
    onRequest({ options }) {
        const userInfo = localStorage.getItem('userStore')
        const token = userInfo ? JSON.parse(userInfo).token : ''
        const sessionToken = sessionStorage.getItem('token')

        options.headers = {
            // eslint-disable-next-line ts/ban-ts-comment
            // @ts-expect-error
            Authorization: sessionToken || token,
        }
    },
    onResponse({ response, request }) {
        if (response.status === 200 && !['/api/checkInCustomerMeal/recommend', '/api/gene-test/program/submit-order', '/api/light-fasting-record/latestLightFastingRecord'].includes(request as string)) {
            if (response._data.state !== 200 && response._data.state !== 302) {
                // 40163 微信 code 重复使用
                if (![40163].includes(response._data.state))
                    showFailToast(`${response._data.msg}`)

                throw new Error(response._data.msg)
            }
        }
    },
    onResponseError({ response, request }) {
        if (!['/api/patient-assessment/getUnsentAssessmentReports'].includes(request as string)) {
            if (response.status === 401) {
                showFailToast(`登录过期${request}`)
                const { reset } = useUserStore()
                reset()
                navigateTo('/logout')
            } else {
                // showFailToast('请求失败')
            }
        }
    },
})
