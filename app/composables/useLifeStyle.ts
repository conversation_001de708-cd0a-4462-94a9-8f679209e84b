// @unocss-include
export function useLifeStyle() {
    const lifeStyles = ref<{
        name: string
        icon: string
        show: boolean
    }[]>([
        {
            name: '饮食规律',
            icon: 'i-custom:user-life-style-diet-rule',
            show: false,
        },
        {
            name: '控制进餐速度',
            icon: 'i-custom:user-life-style-eat-speed',
            show: false,
        },
        {
            name: '避免暴饮暴食',
            icon: 'i-custom:user-life-style-avoid-eat',
            show: false,
        },
        {
            name: '减少外卖',
            icon: 'i-custom:user-life-style-avoid-eatout',
            show: false,
        },
        // {
        //     name: '减少高糖、高盐、高脂肪食物',
        //     icon: 'i-custom:user-life-style-reduce-sugar-salt-fat',
        //     show: false,
        // },
        {
            name: '充足饮水',
            icon: 'i-custom:user-life-style-water',
            show: false,
        },
        {
            name: '充足睡眠',
            icon: 'i-custom:user-life-style-sleep',
            show: false,
        },
        {
            name: '作息规律',
            icon: 'i-custom:user-life-style-regular-schedule',
            show: false,
        },
        {
            name: '控酒或戒酒',
            icon: 'i-custom:user-life-style-wine',
            show: false,
        },
        {
            name: '控烟或戒烟',
            icon: 'i-custom:user-life-style-smoke',
            show: false,
        },
    ])

    const filteredLifeStyles = computed(() => {
        return lifeStyles.value.filter(item => item.show)
    })

    function parseQuestionData(rawQuestionData: string) {
        const questionData = JSON.parse(rawQuestionData)
        if (questionData.diet_behaviors) {
            if (questionData.diet_behaviors.includes('三餐不规律')) {
                lifeStyles.value.find(item => item.name === '饮食规律')!.show = true
            }
            if (questionData.diet_behaviors.includes('进食速度过快')) {
                lifeStyles.value.find(item => item.name === '控制进餐速度')!.show = true
            }
            if (questionData.diet_behaviors.includes('暴饮暴食')) {
                lifeStyles.value.find(item => item.name === '避免暴饮暴食')!.show = true
            }
            if (questionData.diet_behaviors.includes('经常点外卖或在外就餐')) {
                lifeStyles.value.find(item => item.name === '减少外卖')!.show = true
            }
            // if (questionData.diet_behaviors.includes('喜饮奶茶/含糖饮料')) {
            //     lifeStyles.value.find(item => item.name === '减少高糖、高盐、高脂肪食物')!.show = true
            // }
        }

        if (questionData.water) {
            if (['<1000mL', '1000~1500mL'].includes(questionData.water)) {
                lifeStyles.value.find(item => item.name === '充足饮水')!.show = true
            }
        }

        if (questionData.sleep_time) {
            if (['6~7小时', '5~6小时', '<5小时'].includes(questionData.sleep_time)) {
                lifeStyles.value.find(item => item.name === '充足睡眠')!.show = true
            }
        }

        if (questionData.late_nights) {
            if (['1~2次', '3~7次'].includes(questionData.late_nights)) {
                lifeStyles.value.find(item => item.name === '作息规律')!.show = true
            }
        }

        if (questionData.alcohol) {
            if (questionData.alcohol === '是') {
                lifeStyles.value.find(item => item.name === '控酒或戒酒')!.show = true
            }
        }

        if (questionData.smoking) {
            if (questionData.smoking === '是') {
                lifeStyles.value.find(item => item.name === '控烟或戒烟')!.show = true
            }
        }
    }

    return {
        filteredLifeStyles,
        parseQuestionData,
    }
}
