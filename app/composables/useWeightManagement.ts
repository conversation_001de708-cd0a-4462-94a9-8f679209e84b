import dayjs from 'dayjs'

export function useWeightManagement() {
    async function updateDietaryPatterns(dietaryPatternsName: string) {
        try {
            const response = await useWrapFetch<BaseResponse<boolean>>('/api/dietary-patterns', {
                method: 'PUT',
                query: {
                    dietaryPatternsName,
                },
            })
            if (response.state === 200 && response.results) {
                console.log('更新饮食模式成功')
            }
            return response
        } catch (error) {
            console.error('更新饮食模式失败:', error)
            throw error
        }
    }

    async function saveHealthProgramIndex(params: HealthProgramIndex) {
        try {
            const response = await useWrapFetch<BaseResponse<number>>('/api/healthProgram/saveHealthProgramIndex', {
                method: 'POST',
                body: params,
            })
            if (response.state === 200 && response.results) {
                console.log('保存健康计划首页数据成功')
            }
            return response
        } catch (error) {
            console.error('保存健康计划首页数据失败:', error)
            throw error
        }
    }

    // 设置目标体重
    async function saveTargetWeight(weight: number) {
        try {
            const response = await useWrapFetch<BaseResponse<boolean>>('/checkInCustomerIndex/save', {
                method: 'post',
                body: {
                    weightIndex: weight,
                },
            })
            if (response.state === 200 && response.results) {
                console.log('保存目标体重成功')
            }
            return response
        } catch (error) {
            console.error('保存目标体重失败:', error)
            throw error
        }
    }

    async function saveWeightCheckIn(weight: number) {
        try {
            await useWrapFetch('/checkInCustomerWeight/save', {
                method: 'post',
                body: {
                    weight,
                    checkInDate: dayjs().format('YYYY-MM-DD'),
                },
            })
        } catch (error) {
            console.error('保存体重打卡失败:', error)
            throw error
        }
    }

    async function saveWeightManagementData({ healthProgramData, dietMode, targetWeight, weightCheckIn }: {
        healthProgramData: HealthProgramIndex
        dietMode: string
        targetWeight: number
        weightCheckIn: number
    }) {
        if (healthProgramData) {
            const promises = [] as Promise<any>[]
            promises.push(saveHealthProgramIndex(healthProgramData))
            if (dietMode) {
                promises.push(updateDietaryPatterns(dietMode))
            }
            promises.push(saveTargetWeight(targetWeight))
            promises.push(saveWeightCheckIn(weightCheckIn))

            const results = await Promise.allSettled(promises)

            results.forEach((result, index) => {
                if (result.status === 'rejected') {
                    console.error(`Promise ${index} 失败:`, result.reason)
                }
            })

            return results
        }
        return null
    }

    return {
        updateDietaryPatterns,
        saveHealthProgramIndex,
        saveTargetWeight,
        saveWeightManagementData,
    }
}
