import wx from 'weixin-js-sdk'

export async function useWxBridge(params: {
    jsApiList?: wx.jsApiList
    openTagList?: wx.openTagList
}) {
    try {
        const { jsApiList, openTagList } = params

        const { results } = await useWrapFetch<BaseResponse<{
            signature: string
            appId: string
            timestamp: number
            nonceStr: string
        }>>('/open-api/v1/wx/createJsapiSignature', {
            params: {
                url: window.location.href,
            },
        })

        wx.config({
            debug: false,
            appId: useRuntimeConfig().public.appId,
            timestamp: results.timestamp,
            nonceStr: results.nonceStr,
            signature: results.signature,
            jsApiList,
            openTagList,
        })

        return wx
    } catch (error) {
        showFailToast('获取微信签名失败')
        return null
    }
}
