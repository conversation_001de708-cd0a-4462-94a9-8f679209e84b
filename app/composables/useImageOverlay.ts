import { readonly, ref } from 'vue'

import { mergeQRCodeWithLogo } from '@/utils/image-overlay'

export interface ImageOverlayOptions {
    customLogoPath: string
    cacheKey?: string
}

export function useImageOverlay() {
    const isProcessing = ref(false)
    const error = ref<string | null>(null)
    const processedCache = ref<Map<string, string>>(new Map())

    function generateCacheKey(qrCodeData: string, logoPath: string): string {
        const dataHash = btoa(qrCodeData.slice(0, 100)).slice(0, 20)
        const logoHash = btoa(logoPath).slice(0, 20)
        return `${dataHash}_${logoHash}`
    }

    async function processQRCodeWithLogo(
        qrCodeBase64: string,
        options: ImageOverlayOptions,
    ): Promise<string> {
        if (!qrCodeBase64) return ''

        const cacheKey = options.cacheKey || generateCacheKey(qrCodeBase64, options.customLogoPath)

        if (processedCache.value.has(cacheKey)) {
            return processedCache.value.get(cacheKey)!
        }

        isProcessing.value = true
        error.value = null

        try {
            const processedImage = await mergeQRCodeWithLogo(qrCodeBase64, options.customLogoPath)
            processedCache.value.set(cacheKey, processedImage)
            return processedImage
        } catch (err) {
            const errorMessage = err instanceof Error ? err.message : '图像处理失败'
            error.value = errorMessage

            return qrCodeBase64.startsWith('data:image/')
                ? qrCodeBase64
                : `data:image/png;base64,${qrCodeBase64}`
        } finally {
            isProcessing.value = false
        }
    }

    function clearCache() {
        processedCache.value.clear()
    }

    return {
        isProcessing: readonly(isProcessing),
        error: readonly(error),
        processQRCodeWithLogo,
        clearCache,
    }
}
