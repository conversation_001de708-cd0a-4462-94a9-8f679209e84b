export function useFasting(manual = false) {
    const dayjs = useDayjs()

    const fastingState = ref<{
        isChallenge: boolean | null
        startAt: string
        endAt: string
        // 0: 进食期 1: 断食期
        type: 0 | 1
    }>({
        isChallenge: null,
        startAt: '',
        endAt: '',
        // 0: 进食期 1: 断食期
        type: 0,
    })
    const fastingStatus = ref(false)

    const fastingText = computed(() => {
        return fastingState.value.type === 0 ? '进食' : '断食'
    })

    const inverseFastingText = computed(() => {
        return fastingState.value.type === 0 ? '断食' : '进食'
    })

    async function getLatestLightFastingRecord() {
        try {
            const { results } = await useWrapFetch<BaseResponse<any>>('/api/light-fasting-record/latestLightFastingRecord')

            if (results && !results.endTime) {
                fastingState.value.isChallenge = true
                fastingState.value.startAt = dayjs(results.startTime).format('YYYY-MM-DD HH:mm:ss')
                fastingState.value.endAt = dayjs(results.preEndTime).format('YYYY-MM-DD HH:mm:ss')
                fastingState.value.type = results.type
            } else {
                fastingState.value.isChallenge = false
                fastingState.value.startAt = ''
                fastingState.value.endAt = ''
                fastingState.value.type = 0
            }
            fastingStatus.value = true
        } catch (error) {
            fastingStatus.value = true
            console.error(error)
        }
    }

    async function handleChangeFastingStatus() {
        try {
            const isReachEndTime = dayjs().isAfter(dayjs(fastingState.value.endAt))

            if (!isReachEndTime) {
                await showConfirmDialog({
                    title: `提前开始${inverseFastingText.value}`,
                    message: `按计划您应该在${dayjs(fastingState.value.endAt).format('HH:mm')}开始${inverseFastingText.value}，确定要提前开始吗？`,
                    confirmButtonText: '确定开始',
                })
            }

            const { results, state } = await useWrapFetch<BaseResponse<any>>('/light-fasting-record', {
                method: 'POST',
                body: {
                    type: fastingState.value.type === 0 ? 1 : 0,
                    endTime: dayjs().format(`YYYY-MM-DD HH:mm:ss`),
                },
            })

            if (state === 302) {
                showFailToast('结束时间不能早于开始时间')
            }

            if (results) {
                await getLatestLightFastingRecord()
                showToast('设置成功')
            }
        } catch (error) {
            console.error(error)
        }
    }

    onMounted(() => {
        if (!manual) {
            getLatestLightFastingRecord()
        }
    })

    return {
        fastingState,
        fastingText,
        inverseFastingText,
        getLatestLightFastingRecord,
        handleChangeFastingStatus,
        fastingStatus,
    }
}
