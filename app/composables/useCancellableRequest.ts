interface CancellableXHRRequest<T> {
    promise: Promise<T>
    abort: () => void
}

interface RequestOptions {
    method?: 'GET' | 'POST' | 'PUT' | 'DELETE'
    body?: any
    headers?: Record<string, string>
    baseURL?: string
}

/**
 * 创建基于 XMLHttpRequest 的可取消请求
 * 专为微信小程序 web-view 环境设计，支持真正的请求取消
 * 功能完全对标 useWrapFetch
 *
 * @param url 请求地址
 * @param options 请求选项
 * @returns 包含 promise 和 abort 方法的对象
 */
export function useCancellableRequest<T = any>(
    url: string,
    options: RequestOptions = {},
): CancellableXHRRequest<T> {
    const xhr = new XMLHttpRequest()

    const promise = new Promise<T>((resolve, reject) => {
        xhr.onreadystatechange = () => {
            if (xhr.readyState === XMLHttpRequest.DONE) {
                if (xhr.status === 200) {
                    try {
                        const response = JSON.parse(xhr.responseText)

                        // 构建完整请求路径用于特殊处理判断
                        const baseURL = options.baseURL || '/api'
                        const fullUrl = url.startsWith('/api') ? url : `${baseURL}${url.startsWith('/') ? url : `/${url}`}`

                        // 复制 useWrapFetch 的 onResponse 逻辑
                        if (xhr.status === 200 && fullUrl !== '/api/checkInCustomerMeal/recommend') {
                            if (response.state !== 200 && response.state !== 302) {
                                // 40163 微信 code 重复使用
                                if (![40163].includes(response.state)) {
                                    showFailToast(`${response.msg}`)
                                }
                                reject(new Error(response.msg))
                                return
                            }
                        }

                        resolve(response)
                    } catch (error) {
                        reject(new Error('响应解析失败'))
                    }
                } else if (xhr.status === 401) {
                    // 复制 useWrapFetch 的 onResponseError 逻辑
                    const baseURL = options.baseURL || '/api'
                    const fullUrl = url.startsWith('/api') ? url : `${baseURL}${url.startsWith('/') ? url : `/${url}`}`

                    if (!['/api/patient-assessment/getUnsentAssessmentReports'].includes(fullUrl)) {
                        showFailToast(`登录过期${fullUrl}`)
                        const { reset } = useUserStore()
                        reset()
                        navigateTo('/logout')
                    }
                    reject(new Error('登录过期'))
                } else if (xhr.status !== 0) { // 0 表示被 abort
                    reject(new Error(`请求失败: ${xhr.status}`))
                }
            }
        }

        xhr.onerror = () => {
            reject(new Error('网络错误'))
        }

        xhr.onabort = () => {
            reject(new Error('请求已取消'))
        }
    })

    // 构建完整URL
    const baseURL = options.baseURL || '/api'
    const fullUrl = url.startsWith('/api') ? url : `${baseURL}${url.startsWith('/') ? url : `/${url}`}`

    // 设置请求
    const method = options.method || 'GET'
    xhr.open(method, fullUrl, true)

    // 设置通用请求头
    xhr.setRequestHeader('Content-Type', 'application/json')

    // 复制 useWrapFetch 的 onRequest 逻辑
    const userInfo = localStorage.getItem('userStore')
    const token = userInfo ? JSON.parse(userInfo).token : ''
    const sessionToken = sessionStorage.getItem('token')

    if (token || sessionToken) {
        xhr.setRequestHeader('Authorization', token || sessionToken)
    }

    // 设置自定义请求头
    if (options.headers) {
        Object.entries(options.headers).forEach(([key, value]) => {
            xhr.setRequestHeader(key, value)
        })
    }

    // 发送请求
    const body = options.body ? JSON.stringify(options.body) : null
    xhr.send(body)

    return {
        promise,
        abort: () => {
            xhr.abort()
        },
    }
}

/**
 * 创建与 useWrapFetch 完全兼容的可取消请求函数
 * 用于直接替换 useWrapFetch 调用
 */
export function useCancellableWrapFetch<T = any>(
    url: string,
    options: RequestOptions = {},
): Promise<T> {
    const request = useCancellableRequest<T>(url, options)

    // 在当前组件上下文中管理请求
    if (getCurrentInstance()) {
        onBeforeUnmount(() => {
            request.abort()
        })
    }

    return request.promise
}

/**
 * 创建可取消的请求管理器
 * 用于管理多个可取消请求的生命周期
 */
export function useCancellableRequestManager() {
    const requests = ref<Map<string, CancellableXHRRequest<any>>>(new Map())

    /**
     * 创建一个带标识的可取消请求
     * @param key 请求标识
     * @param url 请求地址
     * @param options 请求选项
     */
    function createRequest<T = any>(
        key: string,
        url: string,
        options: RequestOptions = {},
    ): Promise<T> {
        // 如果已存在同名请求，先取消
        if (requests.value.has(key)) {
            requests.value.get(key)?.abort()
        }

        const request = useCancellableRequest<T>(url, options)
        requests.value.set(key, request)

        // 请求完成后清理
        const cleanup = () => {
            requests.value.delete(key)
        }

        request.promise.finally(cleanup)

        return request.promise
    }

    /**
     * 取消指定的请求
     * @param key 请求标识
     */
    function cancelRequest(key: string): boolean {
        const request = requests.value.get(key)
        if (request) {
            request.abort()
            requests.value.delete(key)
            return true
        }
        return false
    }

    /**
     * 取消所有正在进行的请求
     */
    function cancelAllRequests(): void {
        requests.value.forEach((request) => {
            request.abort()
        })
        requests.value.clear()
    }

    /**
     * 获取当前进行中的请求数量
     */
    const activeRequestCount = computed(() => requests.value.size)

    // 组件卸载时自动清理
    onBeforeUnmount(() => {
        cancelAllRequests()
    })

    return {
        createRequest,
        cancelRequest,
        cancelAllRequests,
        activeRequestCount,
    }
}

/**
 * 专门用于轮询的可取消请求
 */
export function useCancellablePolling(options: {
    interval: number
    maxAttempts?: number
    onError?: (error: Error, attempt: number) => void
}) {
    const isPolling = ref(false)
    let currentRequest: CancellableXHRRequest<any> | null = null
    let pollingTimer: ReturnType<typeof setTimeout> | null = null

    async function startPolling<T>(
        url: string,
        requestOptions: RequestOptions = {},
        condition: (data: T) => boolean = () => false,
    ): Promise<T | null> {
        if (isPolling.value) {
            console.warn('轮询已在进行中')
            return null
        }

        isPolling.value = true
        let attempt = 0
        const maxAttempts = options.maxAttempts || Infinity

        while (isPolling.value && attempt < maxAttempts) {
            try {
                attempt++

                currentRequest = useCancellableRequest<T>(url, requestOptions)
                const data = await currentRequest.promise

                currentRequest = null

                if (condition(data)) {
                    stopPolling()
                    return data
                }
            } catch (error) {
                currentRequest = null

                if (error instanceof Error && error.message === '请求已取消') {
                    console.log('轮询请求已被取消')
                    break
                }

                options.onError?.(error as Error, attempt)

                if (attempt >= maxAttempts) {
                    stopPolling()
                    break
                }
            }

            if (!isPolling.value) break

            // 等待下次轮询
            await new Promise<void>((resolve) => {
                pollingTimer = setTimeout(resolve, options.interval)
            })
        }

        stopPolling()
        return null
    }

    function stopPolling(): void {
        isPolling.value = false

        if (currentRequest) {
            currentRequest.abort()
            currentRequest = null
        }

        if (pollingTimer) {
            clearTimeout(pollingTimer)
            pollingTimer = null
        }
    }

    onBeforeUnmount(() => {
        stopPolling()
    })

    return {
        isPolling,
        startPolling,
        stopPolling,
    }
}
