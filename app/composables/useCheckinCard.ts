export function useCheckinCard() {
    const cardList = ref<CheckInCard[]>([])
    const originCardList = ref<CheckInCard[]>([])

    async function fetchData(showLoading = true) {
        let closeLoading = () => {}
        if (showLoading) {
            const { closeLoading: closeLoadingFn } = useLoading()
            closeLoading = closeLoadingFn
        }
        try {
            const { results } = await useWrapFetch<BaseResponse<CheckInCard[]>>('/checkInCard/listCustomer')
            originCardList.value = results.map(item => ({
                ...item,
                value: 0,
            }))
            const filterStatus = results.filter((item: any) => item.status === 1)
            if (filterStatus.length > 0) {
                cardList.value = filterStatus.map(item => ({
                    ...item,
                    value: 0,
                }))
            } else {
                const { results: defaultList } = await useWrapFetch<BaseResponse<CheckInCard[]>>('/checkInCard/listDefault')
                cardList.value = defaultList.filter(item => item.cardName !== 'waistCircumference').map(item => ({
                    ...item,
                    value: 0,
                }))
                await useWrapFetch('/checkInCard/sort', {
                    method: 'POST',
                    body: defaultList.filter(item => item.cardName !== 'waistCircumference').map(item => item.id),
                })
            }
        } catch (error) {
            console.error(error)
        } finally {
            closeLoading()
        }
    }

    onMounted(() => {
        fetchData()
    })

    return {
        fetchData,
        cardList,
        originCardList,
    }
}
