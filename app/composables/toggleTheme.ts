export function toggleTheme(theme?: 'user' | 'manager') {
    theme = theme || 'manager'

    if (theme === 'user') {
        document.documentElement.style.setProperty('--primary-1', '230, 247, 245')
        document.documentElement.style.setProperty('--primary-2', '128, 214, 203')
        document.documentElement.style.setProperty('--primary-3', '106, 217, 203')
        document.documentElement.style.setProperty('--primary-6', '0, 172, 151')
        document.documentElement.style.setProperty('--primary-7', '0, 155, 136')

        // document.documentElement.style.setProperty('--warning-1', '255, 247, 232')
        // document.documentElement.style.setProperty('--warning-2', '255, 228, 186')
        // document.documentElement.style.setProperty('--warning-3', '255, 207, 139')
        // document.documentElement.style.setProperty('--warning-6', '255, 125, 0')
        // document.documentElement.style.setProperty('--warning-7', '210, 95, 0')
    } else {
        document.documentElement.style.setProperty('--primary-1', '228, 244, 250')
        document.documentElement.style.setProperty('--primary-2', '169, 232, 232')
        document.documentElement.style.setProperty('--primary-3', '106, 217, 203')
        document.documentElement.style.setProperty('--primary-6', '0, 172, 151')
        document.documentElement.style.setProperty('--primary-7', '3, 128, 113')

        // document.documentElement.style.setProperty('--warning-1', '255, 247, 232')
        // document.documentElement.style.setProperty('--warning-2', '255, 228, 186')
        // document.documentElement.style.setProperty('--warning-3', '255, 207, 139')
        // document.documentElement.style.setProperty('--warning-6', '255, 125, 0')
        // document.documentElement.style.setProperty('--warning-7', '210, 95, 0')
    }
}
