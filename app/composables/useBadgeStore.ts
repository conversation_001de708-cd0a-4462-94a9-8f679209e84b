export const useBadgeStore = defineStore('badge', {
    state: () => ({
        badges: {
            '/user/checkin': { show: false },
            '/user/tool': { show: false },
            '/user/service': { show: false },
            // '/user/knowledge': { show: false },
            // '/user/handbook': { show: false },
            '/user/archives': { show: false },
        } as Record<string, { show?: boolean, num?: number }>,
        latestHealthProgramPlan: null as any,
        healthProgramPlans: [] as any[],
    }),

    getters: {
        unreadPlansCount: (state) => {
            return state.healthProgramPlans.filter(item => item.readStatus === 0).length
        },
        hasUnreadPlans: (state) => {
            return state.healthProgramPlans.some(item => item.readStatus === 0)
        },
    },

    actions: {
        updateBadge(path: string, data: { show?: boolean, num?: number }) {
            if (this.badges[path]) {
                this.badges[path] = { ...this.badges[path], ...data }
            } else {
                this.badges[path] = { show: data.show || false, num: data.num || 0 }
            }
        },

        async fetchArchivesStatus() {
            const { results } = await useWrapFetch<BaseResponse<any[]>>('/healthProgram/listHealthProgramPlan')
            this.healthProgramPlans = results || []
            this.latestHealthProgramPlan = results?.[0] || null
            this.updateBadge('/user/archives', {
                show: this.hasUnreadPlans,
                num: this.unreadPlansCount,
            })
        },
    },
})
