import test1 from '@/assets/images/home-testing/1.png'
import test2 from '@/assets/images/home-testing/2.png'

export function useHomeTesting() {
    const testItems = ref([
        {
            id: 1,
            name: '16S肠道微生物基因检测',
            desc: '122项健康分析项目',
            advantage: ['六大优势', '深度解读'],
            img: test1,
        },
        {
            id: 2,
            name: '个人健康体检基因检测',
            desc: '78项分析报告',
            advantage: ['7大方向'],
            img: test2,
        },
    ])

    const searchValue = ref('')

    const filteredTestItems = computed(() => {
        return testItems.value.filter(item => item.name.includes(searchValue.value))
    })

    return {
        testItems,
        searchValue,
        filteredTestItems,
    }
}
