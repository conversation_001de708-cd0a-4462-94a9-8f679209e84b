import { closeUnifiedToast, showUnifiedLoadingToast } from '@/utils/unified-toast'

import type { AsyncDataRequestStatus } from '#app'

export function useStatusLoading(status: globalThis.Ref<AsyncDataRequestStatus, AsyncDataRequestStatus>) {
    watch(status, (value) => {
        if (value === 'pending') {
            showUnifiedLoadingToast({
                message: '加载中...',
                forbidClick: true,
                size: 'medium',
                overlay: true,
            })
        } else {
            closeUnifiedToast(true)
        }
    })
}
