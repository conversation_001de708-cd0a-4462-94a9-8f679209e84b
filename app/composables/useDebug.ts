import buildTime from '~build/time'
import { commitMessage } from '~build/git'

import type { MaybeElementRef } from '@vueuse/core'

export function useDebug(debugRef: MaybeElementRef) {
    const config = useRuntimeConfig()
    let configContext = ''

    Object.entries(config.public).forEach(([key, value]) => {
        if (!['motion', 'piniaPluginPersistedstate'].includes(key))
            configContext += `${key}: ${value}\n`
    })

    onLongPress(debugRef, () => {
        const dayjs = useDayjs()
        showDialog({
            message: `
${dayjs(buildTime).format('YYYY-MM-DD HH:mm:ss')}
${commitMessage}
${configContext}
`,

        })
    }, {
        delay: 2000,
    })
}
