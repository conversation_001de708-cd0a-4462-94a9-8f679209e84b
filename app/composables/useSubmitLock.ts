export function useSubmitLock() {
    const isSubmitting = ref(false)

    async function withSubmitLock<T>(task: () => Promise<T>): Promise<T | undefined> {
        if (isSubmitting.value) return
        isSubmitting.value = true
        try {
            return await task()
        } finally {
            // 注意：由调用方决定是否在成功后跳转（不再需要解锁）
            // 若需要允许继续提交（如失败），可在 catch 中手动重置
        }
    }

    function resetSubmitLock() {
        isSubmitting.value = false
    }

    return {
        isSubmitting,
        withSubmitLock,
        resetSubmitLock,
    }
}
