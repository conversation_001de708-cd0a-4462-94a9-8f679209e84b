import type { UploaderFileListItem } from 'vant/es/uploader/types'

interface UseImageUploaderOptions {
    maxCount?: number
    maxFileSize?: number // MB
    compressThreshold?: number // MB
    onUploadSuccess?: (fileId: string) => void
    onUploadComplete?: (fileIds: string[]) => void
}

export function useImageUploader(options: UseImageUploaderOptions = {}) {
    const {
        maxCount = 1,
        maxFileSize = 10,
        compressThreshold = 3.8,
        onUploadSuccess,
        onUploadComplete,
    } = options

    const pictures = ref<UploaderFileListItem[]>([])
    const uploadedFileIds = ref<string[]>([])
    const isImageLoading = ref(false)

    const hasUploadedPictures = computed(() => pictures.value.length > 0)

    async function uploadFile(file: File): Promise<string> {
        const { closeLoading } = useLoading({
            message: '上传中...',
        })

        try {
            const formData = new FormData()
            formData.append('file', file)

            const { results } = await useWrapFetch<BaseResponse<string>>('/v1/file/upload', {
                body: formData,
                method: 'post',
                headers: {
                    Accept: 'application/json',
                },
            })

            if (results) {
                uploadedFileIds.value.push(results)
                onUploadSuccess?.(results)
            }

            return formatResource(results)
        } catch (error) {
            return ''
        } finally {
            closeLoading()
        }
    }

    async function handleBeforeRead(file: File | File[]): Promise<File | File[] | undefined> {
        try {
            if (!file || (Array.isArray(file) && file.length === 0)) {
                showToast('无效的文件')
                return undefined
            }

            const files = Array.isArray(file) ? file : [file]

            if (pictures.value.length + files.length > maxCount) {
                showToast(`最多只能添加${maxCount}张图片`)
                return undefined
            }

            for (const f of files) {
                if (!f || !(f instanceof File)) {
                    showToast('无效的文件')
                    continue
                }

                const fileMbSize = f.size / 1024 / 1024

                if (fileMbSize > maxFileSize) {
                    showToast(`图片大小不能超过${maxFileSize}MB`)
                    return undefined
                }

                try {
                    let _file = f
                    if (fileMbSize >= compressThreshold) {
                        _file = await compressImage(f)
                    }

                    const url = await uploadFile(_file)
                    if (url) {
                        pictures.value.push({ url })
                    }
                } catch (error) {
                    showToast(`处理文件 ${f.name} 失败`)
                    return undefined
                }
            }

            if (uploadedFileIds.value.length > 0) {
                onUploadComplete?.(uploadedFileIds.value)
            }

            return file
        } catch (error) {
            showToast('上传图片失败')
            return undefined
        }
    }

    function handlePreview(file: UploaderFileListItem) {
        showImagePreview([file.url!])
    }

    function handleDelete(file: UploaderFileListItem) {
        const index = pictures.value.findIndex(item => item.url === file.url)
        if (index > -1) {
            uploadedFileIds.value.splice(index, 1)
        }
        pictures.value = pictures.value.filter(item => item.url !== file.url)
    }

    function clearPictures() {
        pictures.value = []
        uploadedFileIds.value = []
    }

    function getUploadedFileIds(): string[] {
        return [...uploadedFileIds.value]
    }

    return {
        pictures,
        uploadedFileIds: readonly(uploadedFileIds),
        isImageLoading: readonly(isImageLoading),
        hasUploadedPictures,

        handleBeforeRead,
        handlePreview,
        handleDelete,
        clearPictures,
        getUploadedFileIds,

        setImageLoading: (loading: boolean) => {
            isImageLoading.value = loading
        },
    }
}
