import SparkMD5 from 'spark-md5'

export function calcFileHash(file: File) {
    return new Promise<string>((resolve, reject) => {
        const reader = new FileReader()

        reader.readAsArrayBuffer(file)

        reader.onload = (e) => {
            const buffer = e.target?.result as ArrayBuffer
            const spark = new SparkMD5.ArrayBuffer()

            spark.append(buffer)

            resolve(spark.end())
        }

        reader.onerror = reject

        reader.onabort = reject
    })
}

export function getFileType(file: File) {
    return file.type.split('/')[1]
}

export function useFileUpload(file: File) {
    const hash = calcFileHash(file)
    const fileType = getFileType(file)

    const formData = new FormData()
    formData.append('file', file)

    return formData
}
