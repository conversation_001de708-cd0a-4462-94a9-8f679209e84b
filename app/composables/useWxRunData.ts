interface WxRunData {
    step: string | undefined
    // 上次获取步数的时候
    lastStepUpdateTime: string | undefined
    // 步数对应的日期
    stepTime: string | undefined
    isReject: boolean
}

export const useWxRunData = defineStore('wxRunData', () => {
    const wxRunData = ref<WxRunData>({
        step: undefined,
        lastStepUpdateTime: undefined,
        stepTime: undefined,
        isReject: false,
    })

    function updateWxRunData(data: WxRunData) {
        if (!data.step)
            return

        if (Number(data.lastStepUpdateTime) < Number(wxRunData.value?.lastStepUpdateTime)) {
            return
        }

        wxRunData.value = data
    }

    return {
        wxRunData,
        updateWxRunData,
    }
}, {
    persist: {
        storage: piniaPluginPersistedstate.localStorage(),
    },
})
