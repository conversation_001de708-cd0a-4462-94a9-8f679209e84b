export const useSportCheckStore = defineStore('sportCheckStore', () => {
    const sportCheckStatus = ref<boolean>(false)

    function setSportCheckStatus(status: boolean) {
        sportCheckStatus.value = status
    }

    function resetSportCheckStatus() {
        sportCheckStatus.value = false
    }

    function getSportCheckStatus() {
        return sportCheckStatus.value
    }

    return {
        sportCheckStatus,
        setSportCheckStatus,
        resetSportCheckStatus,
        getSportCheckStatus,
    }
}, {
    persist: {
        storage: piniaPluginPersistedstate.localStorage(),
        key: 'sport-check-status',
    },
})
