import { driver } from 'driver.js'
import 'driver.js/dist/driver.css'

import guideSlideImg from '@/assets/images/checkin/diet/guide-slide.png'

export function useGuide() {
    let isGuideActive = false
    let driverObj: any = null

    const stepStyles = [
        { offset: '13%' },
        { offset: '20%' },
        { offset: '86%' },
        { offset: '25%' },
        { offset: '75%' },
        { offset: '50%' },
    ]

    function createDriver() {
        return driver({
            showProgress: false,
            allowClose: false,
            showButtons: ['close'],
            steps: [
                {
                    element: '#step1',
                    popover: {
                        description: '打开"个性化营养推荐"查看健康食谱',
                        side: 'bottom',
                        align: 'center',
                    },
                },
                {
                    element: '#step2',
                    popover: {
                        description: '点击卡片：记录每日饮食摄入、运动消耗',
                        side: 'bottom',
                        align: 'center',
                    },
                },
                {
                    element: '#step3',
                    popover: {
                        description: '控制热量收支，轻松减脂减重',
                        side: 'bottom',
                        align: 'center',
                    },
                    onDeselected: () => {
                        const style = document.createElement('style')
                        style.id = 'step4-zindex-style'
                        style.textContent = `
                            #step4 {
                                z-index: 2025 !important;
                            }
                        `
                        document.head.appendChild(style)
                    },
                },
                {
                    element: '#step4',
                    popover: {
                        description: '尽量保证每日均衡营养摄入哦',
                        side: 'top',
                        align: 'center',
                    },
                    onDeselected: () => {
                        const styleElement = document.getElementById('step4-zindex-style')
                        if (styleElement) {
                            styleElement.remove()
                        }
                    },
                },
                {
                    element: '#step5',
                    popover: {
                        description: '尝试轻断食，突破减重瓶颈',
                        side: 'top',
                        align: 'center',
                    },
                    onDeselected: () => {
                        const style = document.createElement('style')
                        style.id = 'step6-overflow-style'
                        style.textContent = `
                            #step6 {
                                z-index: 2025 !important;
                            }
                            .van-uploader__input-wrapper:has(#step6) {
                                overflow: visible !important;
                            }
                            :not(body):has(#step6.driver-active-element) {
                                overflow: visible !important;
                            }
                        `
                        document.head.appendChild(style)
                    },
                },
                {
                    element: '#step6',
                    popover: {
                        description: '餐食拍照，自动计算热量',
                        side: 'top',
                        align: 'start',
                    },
                    onDeselected: () => {
                        const styleElement = document.getElementById('step6-overflow-style')
                        if (styleElement) {
                            styleElement.remove()
                        }
                    },
                },
                { popover: { description: '左右滑动屏幕，发现更多实用工具' } },
            ],
            popoverClass: 'custom-guide-popover',
            smoothScroll: true,
            stagePadding: 4,

            onPopoverRender: (popover, { state }) => {
                const currentIndex = state.activeIndex || 0
                applyStepStyles(currentIndex)
            },

            onHighlighted: () => {
                if (isGuideActive) {
                    const existingContainer = document.querySelector('.guide-buttons-container')
                    if (existingContainer) {
                        existingContainer.remove()
                    }

                    const buttonsContainer = document.createElement('div')

                    if (!driverObj.isLastStep()) {
                        buttonsContainer.className = 'guide-buttons-container'

                        const skipButton = document.createElement('div')
                        skipButton.className = 'guide-skip-btn'
                        skipButton.textContent = '跳过引导'
                        skipButton.style.pointerEvents = 'auto'

                        const nextButton = document.createElement('div')
                        nextButton.className = 'guide-next-btn'
                        nextButton.textContent = '下一步'
                        nextButton.style.pointerEvents = 'auto'

                        skipButton.addEventListener('click', handleGuideEnd, { passive: false })
                        skipButton.addEventListener('touchend', handleGuideEnd, { passive: false })

                        function handleNext(e: MouseEvent | TouchEvent) {
                            e.preventDefault()
                            e.stopPropagation()
                            driverObj.moveNext()
                        }
                        nextButton.addEventListener('click', handleNext, { passive: false })
                        nextButton.addEventListener('touchend', handleNext, { passive: false })

                        buttonsContainer.appendChild(skipButton)
                        buttonsContainer.appendChild(nextButton)
                    } else {
                        buttonsContainer.className = 'guide-buttons-container guide-space-center'

                        const finishButton = document.createElement('div')
                        finishButton.className = 'guide-finish-btn'
                        finishButton.textContent = '完成'
                        finishButton.style.pointerEvents = 'auto'

                        finishButton.addEventListener('click', handleGuideEnd, { passive: false })
                        finishButton.addEventListener('touchend', handleGuideEnd, { passive: false })

                        buttonsContainer.appendChild(finishButton)
                    }

                    document.body.appendChild(buttonsContainer)
                }
            },

            onDestroyed: () => {
                isGuideActive = false
                removeGuideButtons()
                // window.dispatchEvent(new CustomEvent('guide:hideStep6'))
            },
        })
    }

    function handleGuideEnd(e: MouseEvent | TouchEvent) {
        e.preventDefault()
        e.stopPropagation()

        isGuideActive = false

        if (driverObj) {
            driverObj.destroy()
            driverObj = null
        }

        localStorage.setItem('hasSeenGuide', 'true')
    }

    function applyStepStyles(stepIndex: number) {
        const popoverWrapper = document.querySelector('.custom-guide-popover') as HTMLElement
        if (!popoverWrapper) return

        const style = stepStyles[stepIndex] || stepStyles[0]

        const flexStyle = document.createElement('style')
        flexStyle.id = 'guide-flex-style'
        const oldFlexStyle = document.getElementById('guide-flex-style')
        if (oldFlexStyle) {
            oldFlexStyle.remove()
        }
        if (stepIndex === 4) {
            flexStyle.textContent = `
                .custom-guide-popover {
                    justify-content: flex-end !important;
                }
            `
        } else {
            flexStyle.textContent = `
                .custom-guide-popover {
                    justify-content: center !important;
                }
            `
        }
        document.head.appendChild(flexStyle)

        const oldStyle = document.getElementById('guide-step-style')
        if (oldStyle) {
            oldStyle.remove()
        }

        const description = popoverWrapper.querySelector('.driver-popover-description') as HTMLElement
        if (description) {
            const afterStyle = document.createElement('style')
            afterStyle.id = 'guide-step-style'

            if (stepIndex >= 0 && stepIndex <= 2) {
                afterStyle.textContent = `
                    .custom-guide-popover .driver-popover-description::after {
                        right: ${style?.offset} !important;
                        rotate: 0deg;
                        top: -48px;
                        bottom: unset;
                    }   
                `
            } else if (stepIndex >= 3 && stepIndex <= 5) {
                afterStyle.textContent = `
                    .custom-guide-popover .driver-popover-description::after {
                        left: ${style?.offset} !important;
                        rotate: 180deg;
                        bottom: -48px;
                        top: unset;
                    }
                `
            } else if (stepIndex === 6) {
                afterStyle.textContent = `
                    .custom-guide-popover .driver-popover-description::after {
                        display: none !important;
                    }
                `

                const oldImg = description.querySelector('.guide-slide-img')
                if (oldImg) {
                    oldImg.remove()
                }

                const img = document.createElement('img')
                img.src = guideSlideImg
                img.className = 'guide-slide-img'
                img.style.position = 'absolute'
                img.style.width = '64px'
                img.style.height = '64px'
                img.style.top = '-80px'
                img.style.right = '50%'
                img.style.transform = 'translateX(50%)'

                description.appendChild(img)
            }

            document.head.appendChild(afterStyle)
        }
    }

    function removeGuideButtons() {
        const buttonsContainer = document.querySelector('.guide-buttons-container')
        if (buttonsContainer) {
            buttonsContainer.remove()
        }
    }

    function startGuide() {
        isGuideActive = true
        driverObj = createDriver()
        driverObj.drive()
    }

    function destroyGuide() {
        isGuideActive = false
        if (driverObj) {
            driverObj.destroy()
            driverObj = null
        }
        removeGuideButtons()
        // window.dispatchEvent(new CustomEvent('guide:hideStep6'))

        const oldStyle = document.getElementById('guide-step-style')
        if (oldStyle) {
            oldStyle.remove()
        }

        const flexStyle = document.getElementById('guide-flex-style')
        if (flexStyle) {
            flexStyle.remove()
        }
    }

    return {
        startGuide,
        destroyGuide,
        get isActive() {
            return isGuideActive
        },
    }
}
