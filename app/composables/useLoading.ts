import { getCurrentInstance, onUnmounted } from 'vue'

import { allowMultipleUnifiedToast, showUnifiedLoadingToast } from '@/utils/unified-toast'

export function useLoading(options?: {
    message?: string
    allowMultiple?: boolean
} | undefined) {
    let message = '加载中'
    let allowMultiple = true

    if (options) {
        message = options.message || '加载中'
        allowMultiple = options.allowMultiple || true
    }

    // eslint-disable-next-line ts/no-unused-expressions
    allowMultiple && allowMultipleUnifiedToast()

    const loadingToast = showUnifiedLoadingToast({
        message,
        forbidClick: true,
        duration: 0,
        size: 'medium',
        overlay: true,
        allowMultiple,
    })

    function closeLoading() {
        loadingToast.close()
    }

    const instance = getCurrentInstance()
    if (instance) {
        onUnmounted(() => {
            closeLoading()
        })
    }

    return {
        closeLoading,
    }
}
