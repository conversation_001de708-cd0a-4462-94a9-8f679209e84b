import planIntermittentFasting from '~/assets/images/checkin/plan/plan-intermittent-fasting.svg'
import planLightFat from '~/assets/images/checkin/plan/plan-light-fat.svg'
import planLowFat from '~/assets/images/checkin/plan/plan-low-fat.svg'
import planSugarFat from '~/assets/images/checkin/plan/plan-sugar-fat.svg'
import detailIntermittentFasting from '~/assets/images/checkin/plan/detail-intermittent-fasting.svg'
import detailLightFat from '~/assets/images/checkin/plan/detail-light-fat.svg'
import detailLowFat from '~/assets/images/checkin/plan/detail-low-fat.svg'
import detailSugarFat from '~/assets/images/checkin/plan/detail-sugar-fat.svg'

export interface Plan {
    id: PlanId | string
    title: string
    description: string
    tags: string[]
    level: string
    isInUse: boolean
    showStar: boolean
    cover: string
    size: string
    gradient: string
    detail: {
        subTitle: string
        titleColor: string
        cardBackground: string
        coverImage: string
        images: string[]
    }
}

export function usePlans() {
    const plans = ref<Plan[]>([
        {
            id: 'fasting',
            title: 'Li05 生活方式管理计划',
            description: '基于16+8主流轻断食模式',
            tags: ['减肥小白', '懒人减肥法'],
            level: '免费',
            isInUse: false,
            showStar: false,
            cover: planIntermittentFasting,
            size: 'w-69px h-67px',
            gradient: 'linear-gradient(180deg, #F1FFFB 1.88%, #F7FFFD 100%)',
            detail: {
                subTitle: '和油腻说拜拜，拥抱清新与美丽',
                titleColor: '#00AC97',
                cardBackground:
                    'linear-gradient(180deg, #DCFFE6 0%, #F4F5F7 100%)',
                coverImage: detailIntermittentFasting,
                images: [
                    '1.intro.svg',
                    '2.plan-content.svg',
                    '3.fasting.svg',
                    '4.weight.svg',
                    '5.suitable-people.svg',
                    '6.qa.svg',
                ],
            },
        },
        {
            id: '22',
            title: 'Li05 轻脂瘦身管理计划',
            description: '多维度数据，制定精准化饮食方案',
            tags: ['清肠道', '降脂减重'],
            level: '基础',
            isInUse: false,
            showStar: true,
            cover: planLightFat,
            size: 'w-73px h-77px',
            gradient: 'linear-gradient(180deg, #F1FBFF 1.88%, #F5FCFF 100%)',
            detail: {
                subTitle: '每天一点点，脂肪Say Byebye！',
                titleColor: '#0989E3',
                cardBackground:
                    'linear-gradient(180deg, #DCF0FF 0%, #F4F5F7 100%)',
                coverImage: detailLightFat,
                images: [
                    '1.intro.svg',
                    '2.plan-content.svg',
                    '3.probiotic.svg',
                    '4.nutritionist.svg',
                    '5.weight.svg',
                    '6.suitable-people.svg',
                    '7.qa.svg',
                ],
            },
        },
        {
            id: 'lipid-reduction',
            title: 'Li05 降脂减重管理计划',
            description: '调节肠道菌群、促进脂肪代谢',
            tags: ['清肠道', '降脂减重'],
            level: '进阶',
            isInUse: false,
            showStar: true,
            cover: planLowFat,
            size: 'w-82px h-82px',
            gradient: 'linear-gradient(180deg, #FFFCF1 1.88%, #FFFEF7 100%)',
            detail: {
                subTitle: '每天一点点，脂肪Say Byebye！',
                titleColor: '#E7B100',
                cardBackground:
                    'linear-gradient(180deg, #FFF9E6 0%, #F4F5F7 100%)',
                coverImage: detailLowFat,
                images: [
                    '1.intro.svg',
                    '2.plan-content.svg',
                    '3.probiotic.svg',
                    '4.prebiotics.svg',
                    '5.nutritionist.svg',
                    '6.weight.svg',
                    '7.suitable-people.svg',
                    '8.qa.svg',
                ],
            },
        },
        {
            id: 'metabolic-improvement',
            title: 'Li05 糖脂代谢综合管理计划',
            description: '调节肠道菌群、促进脂肪代谢',
            tags: ['清肠道', '降脂减重'],
            level: '定制',
            isInUse: false,
            showStar: true,
            cover: planSugarFat,
            size: 'w-82px h-82px',
            gradient: 'linear-gradient(180deg, #F1F3FF 1.88%, #F8F9FF 100%)',
            detail: {
                subTitle: '每天一点点，脂肪Say Byebye！',
                titleColor: '#5E6AF2',
                cardBackground:
                    'linear-gradient(180deg, #EEEDFF 0%, #F4F5F7 100%)',
                coverImage: detailSugarFat,
                images: [
                    '1.intro.svg',
                    '2.plan-content.svg',
                    '3.sugar.svg',
                    '4.probiotic.svg',
                    '5.prebiotics.svg',
                    '6.nutritionist.svg',
                    '7.weight.svg',
                    '8.suitable-people.svg',
                    '9.qa.svg',
                ],
            },
        },
    ])

    const setInUsePlan = (planId: PlanId) => {
        plans.value.forEach((plan) => {
            plan.isInUse = plan.id === planId
        })
    }

    const getPlanById = function (id: PlanId | string) {
        let _id = id

        if (id === '21') {
            _id = 'fasting'
        } else if (id === '23') {
            _id = 'lipid-reduction'
        } else if (id === '24') {
            _id = 'metabolic-improvement'
        }

        return plans.value.find(plan => plan.id === _id) as Plan
    }

    return {
        plans,
        setInUsePlan,
        getPlanById,
    }
}
