export function useGetCheckInByDate() {
    const checkinData = ref<CheckInCustomerData>()

    async function getData(checkInDate: string) {
        const { results } = await useWrapFetch<BaseResponse<CheckInCustomerData>>('/checkInCustomerData/getByDate', {
            method: 'POST',
            body: {
                checkInDate,
            },
        })

        checkinData.value = results
    }

    return {
        getData,
        checkinData,
    }
}
