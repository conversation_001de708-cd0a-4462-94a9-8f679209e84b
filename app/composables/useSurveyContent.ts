export function useSurveyContent() {
    const preliminaryResult = ref('{}')
    const secondaryResult = ref('{}')

    async function init() {
        const { results: questionListResults } = await useWrapFetch<BaseResponse<QuestionList[]>>('/user/question/list')

        const reportInterpretationId = questionListResults[0]!.reportInterpretationId

        const { results: questionDetail } = await useWrapFetch<BaseResponse<EvaluateQuestion>>(`/user/getSecondaryEvaluationByReportInterpretationId/${reportInterpretationId}`)

        if (questionDetail.preliminaryEvaluationQuestionResultId) {
            const { results: preliminary } = await useWrapFetch<BaseResponse<QuestionResult>>(`/question-result/${questionDetail.preliminaryEvaluationQuestionResultId}`)
            preliminaryResult.value = preliminary.questionResult
        }

        if (questionDetail.secondaryEvaluationQuestionResultId) {
            const { results: secondary } = await useWrapFetch<BaseResponse<QuestionResult>>(`/question-result/${questionDetail.secondaryEvaluationQuestionResultId}`)
            secondaryResult.value = secondary.questionResult
        }
    }

    onMounted(init)

    return {
        preliminaryResult,
        secondaryResult,
    }
}
