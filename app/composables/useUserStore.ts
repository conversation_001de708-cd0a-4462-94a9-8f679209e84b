export const useUserStore = defineStore('userStore', () => {
    const userInfo = ref<HostProfileForm>()
    const token = ref('')
    const role = ref<'user' | 'manager'>()

    const { getUserState } = useUserStateStore()

    watch(token, () => {
        if (token.value && role.value === 'user') {
            getUserState()
        }
    }, {
        immediate: true,
    })

    function reset() {
        const { wxRunData } = storeToRefs(useWxRunData())
        const { reset: resetUserState } = useUserStateStore()

        resetUserState()

        wxRunData.value = {
            step: undefined,
            lastStepUpdateTime: undefined,
            stepTime: undefined,
        }

        userInfo.value = undefined
        token.value = ''
        role.value = undefined
    }

    watchEffect(() => toggleTheme(role.value))

    const ageAndGender = computed(async () => {
        if (role.value === 'manager') {
            return {
                age: 1,
                gender: 'male',
            }
        }

        if (userInfo.value?.idCard) {
            return parseIDCard(userInfo.value?.idCard)
        }
        else if (userInfo.value?.gender && userInfo.value?.age) {
            return {
                age: userInfo.value?.age,
                gender: userInfo.value?.gender === '男' ? 'male' : 'female',
            }
        }
        else {
            const { results: questionListResults } = await useWrapFetch<BaseResponse<QuestionList[]>>('/user/question/list')
            const reportInterpretationId = questionListResults[0]!.reportInterpretationId
            const { results: questionDetail } = await useWrapFetch<BaseResponse<EvaluateQuestion>>(`/user/getSecondaryEvaluationByReportInterpretationId/${reportInterpretationId}`)

            if (questionDetail.preliminaryEvaluationQuestionResultId) {
                const { results: preliminary } = await useWrapFetch<BaseResponse<QuestionResult>>(`/question-result/${questionDetail.preliminaryEvaluationQuestionResultId}`)

                const parsed = JSON.parse(preliminary.questionResult)

                return {
                    age: parsed['2'],
                    gender: parsed['1'] === '1' ? 'male' : 'female',
                }
            }
        }

        return {
            age: 1,
            gender: 'male',
        }
    })

    return {
        userInfo,
        token,
        reset,
        role,
        ageAndGender: computed(() => ageAndGender.value),
    }
}, {
    persist: {
        storage: piniaPluginPersistedstate.localStorage(),
    },
})
