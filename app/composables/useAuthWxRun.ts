export async function useAuthWxRun() {
    if (isOnWechatMP()) {
        const { wxRunData } = useWxRunData()
        if (wxRunData?.isReject) {
            return
        }

        const dayjs = useDayjs()
        if (wxRunData?.step === undefined) {
            const wx = await useWxBridge({})

            wx?.miniProgram.redirectTo({
                url: `/pages/run/index`,
            })
        } else if (!dayjs.unix(Number(wxRunData?.lastStepUpdateTime)).isSame(dayjs(), 'day')) {
            const wx = await useWxBridge({})

            wx?.miniProgram.redirectTo({
                url: `/pages/run/index`,
            })
        }
    }
}
