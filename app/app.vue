<script setup lang="ts">
import VConsole from 'vconsole'

import 'amfe-flexible'
import './build-info'
import { loadingFadeOut } from './closeAppLoading'

loadingFadeOut()

const { hospitalId } = storeToRefs(useUserStateStore())

whenever(hospitalId, (val) => {
    if (val === 'nanjing') {
        useHead({
            title: '体重管理专家',
        })
    } else {
        useHead({
            title: 'SLMC体重管家',
        })
    }
}, {
    immediate: true,
})

onMounted(() => {
    if (useRuntimeConfig().public.vconsole) {
        new VConsole()
    }
})

const themeVars = ref({
    primaryColor: 'rgb(var(--primary-6))',
})

const metaLayout = ref<MetaLayout>({
    customBg: undefined,
    noPadding: false,
})

const route = useRoute()
watch(() => route.meta, (val: any) => {
    if (val.meta?.layout) {
        metaLayout.value = val.meta?.layout
    } else {
        metaLayout.value = {
            customBg: undefined,
            noPadding: false,
        }
    }
}
, {
    deep: true,
    immediate: true,
},
)

const isPlanOverlayShow = ref(false)

// const stopWatch = watch(() => route.path, async (val) => {
//     if (val.includes('/user') && !['/user/archives/nutrition-plan', '/user/address', '/user/address/new', '/user/confirm-order'].includes(val) && !sessionStorage.getItem('planOverlayShow')) {
//         const { results } = await useWrapFetch<BaseResponse<number>>('/patient-assessment/getUnsentAssessmentReports')
//         if (results > 0) {
//             isPlanOverlayShow.value = true
//             sessionStorage.setItem('planOverlayShow', 'true')
//             stopWatch()
//         }
//     }
// }, {
//     immediate: true,
// })

const { include, exclude } = storeToRefs(useKeepaliveStore())
</script>

<template>
    <van-config-provider :theme-vars="themeVars" theme-vars-scope="global">
        <nuxt-loading-indicator />
        <nuxt-layout :meta-layout="metaLayout">
            <nuxt-page
                :keepalive="{
                    include,
                    exclude,
                }"
            />
        </nuxt-layout>

        <user-plan-overlay v-model="isPlanOverlayShow" />
    </van-config-provider>
</template>
