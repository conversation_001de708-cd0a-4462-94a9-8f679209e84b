<script setup lang="ts">
import { getSurveyMeta } from '@/utils/survey/getMeta'

const props = defineProps<{
    data: QuestionList[]
}>()

const activeTab = ref('health')

async function handleNavigate(planId: string) {
    const wx = await useWxBridge({})
    wx?.miniProgram.navigateTo({
        url: `/pages/order/new-pay?planId=${planId}&showToggle=1`,
    })
}
</script>

<template>
    <div class="relative z-0 flex flex-col gap-16px p-16px pb-0 h-full">
        <img
            src="@/assets/images/service/banner-camp.png" width="100%" height="139px" alt="" srcset=""
            @click="navigateTo('/user/camp')"
        />

        <div class="flex-1 flex flex-col">
            <div
                class="flex flex-col px-16px pb-16px h-399px"
                :class="activeTab === 'health' ? 'health-bg-l' : 'health-bg-r'"
            >
                <div class="flex h-56px">
                    <div
                        class="flex flex-1 items-center justify-center"
                        :class="activeTab === 'health' ? 'text-#1D2229 text-16px font-600' : 'text-#868F9C text-15px font-500'"
                        @click="activeTab = 'health'"
                    >
                        健康服务
                    </div>
                    <div
                        class="flex flex-1 items-center justify-center"
                        :class="activeTab === 'survey' ? 'text-#1D2229 text-16px font-600' : 'text-#868F9C text-15px font-500'"
                        @click="activeTab = 'survey'"
                    >
                        问卷中心
                    </div>
                </div>
                <div class="mt-16px">
                    <div
                        v-if="activeTab === 'health'"
                        class="w-full flex flex-col gap-16px"
                    >
                        <div class="flex flex-col">
                            <img src="@/assets/images/service/health-1.png" width="100%" height="100%" @click="handleNavigate('lipid-reduction')" />
                            <div class="bg-#F5F6FA pl-16px h-41px w-full rd-lb-10px rd-rb-10px flex items-center text-14px text-#1D2229 font-600">
                                SLMC 降脂减重套餐
                            </div>
                        </div>
                        <!-- < img src="@/assets/images/service/health-2.png" width="100%" height="100%" @click="handleNavigate('metabolic-improvement')" /> -->
                    </div>
                    <div
                        v-if="activeTab === 'survey'"
                        class="w-full flex flex-col gap-16px"
                    >
                        <div
                            v-for="survey in data || []" :key="survey.questionId"
                            class="flex flex-col rd-10px overflow-hidden h-151px" :class="[
                                Number(survey.questionId) === 7 ? 'survey-bg-1'
                                : Number(survey.questionId) === 9 ? 'survey-bg-2' : 'survey-bg-default',
                            ]"
                        >
                            <div class="flex-1 pl-24px flex flex-col justify-center">
                                <div class="text-20px text-#FFFFFF font-600">
                                    {{ getSurveyMeta(survey.content).title }}
                                </div>
                            </div>
                            <div class="flex gap-4px items-center justify-end h-48px px-16px">
                                <template v-if="survey.reportInterpretationId">
                                    <nuxt-link v-if="survey.reportInterpretationId" :to="`/user/assessment/${survey.reportInterpretationId}/records`">
                                        <van-button :disabled="!survey.questionId" round color="#E4FAF9" class="w-90px! h-30px! text-#00AC97!">
                                            填写记录
                                        </van-button>
                                    </nuxt-link>

                                    <nuxt-link v-if="Number(survey.questionId) !== EXTERNAL_PROCESS_ID" :to="`/user/assessment/${survey.reportInterpretationId}/evaluate`">
                                        <van-button :disabled="!survey.questionId" round color="#E4FAF9" class="w-90px! h-30px! text-#00AC97!">
                                            评估结果
                                        </van-button>
                                    </nuxt-link>

                                    <nuxt-link :to="`/user/survey/consent?surveyId=${survey.questionId}&interpretationId=${survey.reportInterpretationId}&resultId=${survey.questionResultId}&type=PRELIMINARY_EVALUATION`">
                                        <van-button :disabled="!survey.questionId" round color="#E4FAF9" class="w-90px! h-30px! text-#00AC97!">
                                            重新评估
                                        </van-button>
                                    </nuxt-link>
                                </template>

                                <div v-else>
                                    <nuxt-link :to="`/user/survey/consent?surveyId=${survey.questionId}&interpretationId=${survey.reportInterpretationId}&resultId=${survey.questionResultId}&type=PRELIMINARY_EVALUATION`">
                                        <van-button round type="primary" class="w-90px! h-30px!">
                                            开始评估
                                        </van-button>
                                    </nuxt-link>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="bg-white flex-1">
                <div class="text-#868F9C text-14px w-full text-center mt-0px">更多{{ activeTab === 'health' ? '健康服务' : '评估内容' }}上线中</div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.survey-bg-1 {
    background-image: url('@/assets/images/service/survey-1.png');
    background-size: cover;
    background-position: center;
}
.survey-bg-2 {
    background-image: url('@/assets/images/service/survey-2.png');
    background-size: cover;
    background-position: center;
}

.health-bg-l {
    background-image: url('@/assets/images/service/health-l.png');
    background-size: cover;
    background-position: center;
}
.health-bg-r {
    background-image: url('@/assets/images/service/health-r.png');
    background-size: cover;
    background-position: center;
}
</style>
