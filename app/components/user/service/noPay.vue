<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/css'
import 'swiper/css/pagination'
import { Autoplay, Pagination } from 'swiper/modules'

import DietPatternIndex from '~/pages/user/checkin/diet-pattern/index.vue'

const props = defineProps<{
    data: QuestionList[]
    status: string
}>()

const paginationStyle = {
    '--swiper-pagination-color': '#00AC97',
}
</script>

<template>
    <div class="relative z-0">
        <template v-if="status === 'pending'">
            <div class="h-200px flex items-center justify-center">
                <shared-unified-loading size="medium" :rainbow="false" text="加载中..." vertical />
            </div>
        </template>

        <template v-else>
            <swiper
                v-if="data?.length"
                :modules="[Autoplay, Pagination]"
                :pagination="{ clickable: true }"
                :style="paginationStyle"
                autoplay
            >
                <swiper-slide v-for="survey in data || []" :key="survey.questionId">
                    <user-swipers1 :survey="survey" />
                </swiper-slide>
            </swiper>
            <van-empty v-else description="暂无问卷" />
        </template>

        <div class="flex flex-col mx-16px pt-16px bg-white rd-10px overflow-hidden">
            <div class="w-full text-#1D2229 pl-24px text-15px font-600 relative before:content-[''] before:absolute before:rd-1px before:left-16px before:top-1/2 before:-translate-y-1/2 before:w-3px before:h-13px before:bg-primary-6">
                推荐食谱
            </div>
            <diet-pattern-index :is-replace="false" />
        </div>

        <shared-safe-buttom />
    </div>
</template>
