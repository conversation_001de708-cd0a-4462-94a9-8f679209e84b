<script setup lang="ts">
import ID from 'idcard'

const isSignOverlayShow = ref(false)
const route = useRoute()

const dayjs = useDayjs()

const isLoading = ref(false)

const { userInfo } = storeToRefs(useUserStore())

const signData = ref({
    name: userInfo.value?.name,
    systolicPressure: '',
    diastolicPressure: '',
    cirrhosis: '',
    waistCircumference: '',
    cap: '',
    idCard: userInfo.value?.idCard,
    doctorId: '',
    signInRecordId: '',
})

async function handleSignIn() {
    try {
        isLoading.value = true
        const doctorId = sessionStorage.getItem('doctorId')
        signData.value.doctorId = doctorId || '0'
        if (!signData.value.idCard) {
            showToast('请输入身份证号')
            return
        }

        if (!ID.verify(signData.value.idCard.toString())) {
            showToast('请输入正确的身份证号')
            return
        }

        const { results } = await useWrapFetch<BaseResponse<string>>(`/signin/addByCustomer`, {
            method: 'POST',
            body: {
                ...signData.value,
            },
        })
        if (results) {
            isSignOverlayShow.value = false
            signData.value.signInRecordId = results
            userInfo.value!.idCard = signData.value.idCard

            await useWrapFetch<BaseResponse<any>>(`/signin/updateIndex`, {
                method: 'POST',
                body: {
                    ...signData.value,
                },
            })

            showSuccessToast('签到成功')
            sessionStorage.removeItem('doctorId')
        } else {
            showFailToast('今日已签到')
            sessionStorage.removeItem('doctorId')
            isSignOverlayShow.value = false
        }
    } catch (error) {
        console.log(error)
    } finally {
        isLoading.value = false
    }
}

const isSign = ref(false)

// const opacity = computed(() => {
//     return isSign.value ? '0.5' : '1'
// })

async function checkIsSign() {
    const today = dayjs().format('YYYY-MM-DD')
    const { results } = await useWrapFetch<BaseResponse<any>>(`/signin/listIndexByCustomer/${today}`)

    if (results) {
        isSign.value = true
    }
}

onMounted(async () => {
    if (sessionStorage.getItem('sign-overlay-opened') === 'true') {
        return false
    } else {
        if (route.query.doctorId) {
            sessionStorage.setItem('doctorId', route.query.doctorId as string)
            isSignOverlayShow.value = true
            checkIsSign()
            sessionStorage.setItem('sign-overlay-opened', 'true')
            return
        }

        if (sessionStorage.getItem('doctorId')) {
            isSignOverlayShow.value = true
            checkIsSign()
            sessionStorage.setItem('sign-overlay-opened', 'true')
        }
    }
})

function handleClose() {
    isSignOverlayShow.value = false
    sessionStorage.removeItem('doctorId')
}

async function handleUpdateRecord() {
    try {
        const { results } = await useWrapFetch<BaseResponse<any>>(`/signin/updateIndex`, {
            method: 'POST',
            body: {
                ...signData.value,
            },
        })

        if (results) {
            showSuccessToast('数据更新成功')
            sessionStorage.removeItem('doctorId')
            isSignOverlayShow.value = false
        }
    } catch (error) {
        console.log(error)
    }
}
</script>

<template>
    <van-overlay :show="isSignOverlayShow">
        <div flex="~ col" w-full h-full gap-16px items-center justify-center>
            <div flex="~ col" items-center relative>
                <div class="background-survey-invite"></div>
                <div
                    class="w-303px rd-16px -translate-y-40px flex flex-col gap-12px pt-48px px-20px"
                    style="background: linear-gradient(180deg, #D8EEFF 0%, #FFFFFF 70.14%);"
                >
                    <!-- <div w-full px-21px>
                        <input v-model="idCard" class="custom-input" placeholder="请输入身份证号" />
                    </div> -->

                    <div>
                        <div class="text-t-4 text-14px">
                            身份证号
                        </div>

                        <input v-model="signData.idCard" class="custom-input" placeholder="请输入身份证号" />
                    </div>

                    <!-- <div class="flex justify-between">
                        <div>
                            <div class="text-t-4 text-14px">
                                收缩压 <span class="text-12px text-t-3">(mmHg)</span>
                            </div>

                            <input v-model="signData.systolicPressure" class="custom-input !w-126px" placeholder="请输入收缩压" />
                        </div>

                        <div>
                            <div class="text-t-4 text-14px">
                                舒张压 <span class="text-12px text-t-3">(mmHg)</span>
                            </div>

                            <input v-model="signData.diastolicPressure" class="custom-input !w-126px" placeholder="请输入收缩压" />
                        </div>
                    </div>

                    <div>
                        <div class="text-t-4 text-14px">
                            腰围 <span class="text-12px text-t-3">(cm)</span>
                        </div>

                        <input v-model="signData.waistCircumference" class="custom-input" placeholder="请输入腰围" />
                    </div>

                    <div>
                        <div class="text-t-4 text-14px">
                            肝硬度 <span class="text-12px text-t-3">(KPa)</span>
                        </div>

                        <input v-model="signData.cirrhosis" class="custom-input" placeholder="请输入肝硬度" />
                    </div>

                    <div>
                        <div class="text-t-4 text-14px">
                            CAP <span class="text-12px text-t-3">(db/m)</span>
                        </div>

                        <input v-model="signData.cap" class="custom-input" placeholder="请输入CAP指标" />
                    </div> -->

                    <div class="flex justify-center items-center mb-16px mt-24px! gap-16px">
                        <van-button :disabled="isLoading || isSign" type="primary" class="h-50px! w-145px!" round size="large" @click="handleSignIn">
                            {{ isSign ? '今日已签到' : '立即签到' }}
                        </van-button>

                        <!-- <van-button v-else type="primary" round class="h-50px! w-145px!" @click="handleUpdateRecord">
                            更新记录
                        </van-button> -->
                    </div>
                </div>
                <div class="i-custom:close absolute h-32px w-32px z-1000 -bottom-10px left-136px" @click="handleClose"></div>
            </div>
        </div>
    </van-overlay>
</template>

<style lang="scss" scoped>
.background-survey-invite {
    background: url('@/assets/images/background/sign.png') no-repeat center/cover;
    --uno: w-111px h-92px relative z-1000;
}

.custom-input {
  border: 1px solid rgba(78, 89, 105, 1);
  --uno: bg-transparent text-16px rd-56px h-40px w-full indent-16px mt-10px;
//   opacity: v-bind(opacity);
}

.custom-input::placeholder {
  --uno: text-t-3;
  font-size: 16px;
}
</style>
