<script setup lang="ts">
import ID from 'idcard'

const props = defineProps<{
    initData: any
    formatedBmi: any
    interpretationId: string
}>()

const { userInfo } = storeToRefs(useUserStore())
const idCard = ref()

const isIdcardOverlayShow = ref(false)

const navLink = computed(() => {
    return `/user/survey/submit?surveyId=${props.initData?.evaluateQuestion?.results.secondaryEvaluationQuestionId}&resultId=${props.initData?.evaluateQuestion?.results.secondaryEvaluationQuestionResultId}&type=SECONDARY_EVALUATION&interpretationId=${props.interpretationId}`
})

function handleEvaluate() {
    if (!userInfo.value?.idCard) {
        isIdcardOverlayShow.value = true
    } else {
        navigateTo(navLink.value)
    }
}

const isLoading = ref(false)

async function handleConfirm() {
    try {
        isLoading.value = true
        if (!idCard.value) {
            showToast('请输入身份证号')
            return
        }

        if (!ID.verify(idCard.value.toString())) {
            showToast('请输入正确的身份证号')
            return
        }

        const { results } = await useWrapFetch<BaseResponse<string>>('/user/rewIdCard', {
            method: 'PUT',
            params: {
                idCard: idCard.value,
            },
        })
        if (results) {
            showSuccessToast('更新成功')
            isIdcardOverlayShow.value = false
            userInfo.value!.idCard = idCard.value
            navigateTo(navLink.value)
        } else {
            showFailToast('更新身份证失败')
        }
    } catch (error) {
        console.log(error)
    } finally {
        isLoading.value = false
    }
}

const standardWeight = computed(() => {
    const height = Number(props.initData?.height)
    const gender = props.initData?.gender
    if (gender === '男') {
        return ((height - 80) * 0.7).toFixed(0)
    }
    return ((height - 70) * 0.6).toFixed(0)
})
</script>

<template>
    <div class="mx-16px h-[calc(100%-200px)] -translate-y-70px bg-white rd-10px p-16px text-t-5 flex flex-col justify-between">
        <div>
            <div flex justify-between>
                <div text="primary-6 15px" font-600>
                    {{ formatedBmi.fat }}
                </div>

                <div>
                    <span text="t-4 12px">
                        BMI指数
                    </span>
                    <span text="primary-6 16px" mx-4px>
                        {{ formatedBmi.value }}
                    </span>
                </div>
            </div>

            <div flex gap-16px my-16px>
                <div class="h-45px w-full bg-#FFF5E0 rd-24px flex items-center pl-16px">
                    <span text="#333">当前体重</span>
                    <span text="warning-6 16px" font="600" ml-8px>
                        {{ props.initData?.weight }}kg
                    </span>
                </div>

                <div class="h-45px w-full bg-#C6F1EC rd-24px flex items-center pl-16px">
                    <span text="#333">理想体重</span>
                    <span text="primary-6 16px" font="600" ml-8px>
                        {{ standardWeight }}kg
                    </span>
                </div>
            </div>

            <user-weight-graph :bmi="Number(formatedBmi.value)" />
        </div>

        <div>
            <div class="flex justify-center" mb-16px>
                <div w-209px text="t-5 center">
                    为了您的健康建议进一步检查及评估获得个性化健康指导
                </div>
            </div>
            <div class="grid grid-cols-2 gap-16px">
                <van-button type="primary" round class="!text-15px" @click="handleEvaluate">
                    继续深度评估
                </van-button>

                <user-nav-to-home />
            </div>
        </div>
    </div>

    <user-idcard-overlay
        v-model="isIdcardOverlayShow"
        v-model:id-card="idCard"
        @confirm="handleConfirm"
    />

    <user-sign-overlay />
</template>

<style lang="scss" scoped>
.dashed-border {
    width: 100%;
    height: 1px;
    background-image: linear-gradient(to right, #a9e8e8 0%, #a9e8e8 50%, transparent 50%);
    background-size: 10px 10px;
    background-repeat: repeat-x;
    margin: 23px 0 47px 0;
}
</style>
