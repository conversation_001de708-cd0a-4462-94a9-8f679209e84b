<script setup lang="ts">
const { initData } = defineProps<{
    initData: any
    interpretationId: string
}>()

const suggestions = computed(() => {
    return initData.suggestions
})
</script>

<template>
    <div class="mx-16px -translate-y-20px" bg-white rd-10px p-16px text-t-5>
        <div v-if="suggestions?.length" text="t-5 14px" leading-30px>
            <!-- <p>
                根据您的评估结果：
            </p>

            <span>
                存在<span class="custom-underline">待改善的生活习惯</span>
            </span> -->

            <p>
                脂肪肝与饮食、运动等生活方式密切相关,
            </p>

            <p>
                <span class="custom-underline">合理饮食结构</span>有助于改善脂肪肝,
            </p>

            <p>
                <span class="custom-underline">多样食物选择</span>可满足机体营养需求,
            </p>

            <p>
                <span class="custom-underline">适量运动</span>有助于保障良好的生理机能运转，促进新陈代谢并提升身体活力。
            </p>

            <!-- <p>
                <span class="custom-underline">丰富食物种类</span>
                <span>，最大化满足营养所需。</span>
            </p>

            <p>
                同时每周进行<span class="custom-underline">适量运动</span>，从而保障良好的生理机能运转，促进新陈代谢并提升身体活力。
            </p> -->
        </div>

        <div v-else text="t-5 14px" leading-30px>
            <p>
                根据您的评估结果：
            </p>
            <p>
                您的生活习惯良好
            </p>

            <p>
                希望您继续保持<span class="custom-underline">均衡饮食，适量运动</span>
            </p>

            <p>
                从而保障良好的生理机能运转，促进新陈代谢并提升身体活力。
            </p>
        </div>

        <template v-if="suggestions?.length">
            <div pt-16px flex items-center mb-8px>
                <div class="i-custom-dot-orange w-22px h-14px"></div>

                <span text-t-5 text-16px font-600>健康建议</span>
            </div>

            <ul pr-24px>
                <li v-for="item in suggestions" :key="item">
                    {{ item.comment }}
                </li>
            </ul>
        </template>

        <div pt-16px flex items-center mb-8px>
            <div class="i-custom-dot-green w-22px h-14px"></div>

            <span text-t-5 text-16px font-600>生活方式建议</span>
        </div>

        <template v-if="suggestions?.length">
            <user-suggest-block v-for="(item, index) in suggestions" :key="index" :content="item.suggestion" :title="item.label" mt-8px />
        </template>

        <template v-else>
            <div class="border-gradient" mt-28px>
                <div p-16px relative>
                    <div text-t-4 text-12px>
                        规律作息、均衡饮食、适度运动，这些良好的生活习惯已融入你的日常，请继续保持！日常饮食宜多样均衡，主食粗细搭配，多吃蔬果，远离加工、高糖和油炸食品，推荐每日盐摄入少于 6 克，油脂摄入 25 - 30 克以内。运动遵循适度原则，每周 3 - 5 次超 30 分钟的有氧，如快走、骑车，配合 2 - 3 次俯卧撑、深蹲等力量训练，运动前后做好热身拉伸，循序渐进，避免受伤。
                    </div>
                </div>
            </div>
        </template>

        <user-nav-to-home class="w-full" />
    </div>
</template>

<style>
ul {
    @apply list-disc text-warning-6 relative left-25px;

}
.border-gradient {
  position: relative;
  transform: translateY(-15px);
  backdrop-filter: blur(4px);
  background-color:rgba(237, 255, 253, 0.27);
  /* padding: 16px; */

  &::before {
    display: block;
    content: '';
    border-radius: 10px;
    border: 1px solid transparent;
    background: linear-gradient(270deg, #DFF2F0 0%, #E1FDFA 100%) border-box;
    -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
    -webkit-mask-composite: xor;
    mask-composite: exclude;
    position: absolute;
    width: 100%;
    height: 100%;
  }
}

.tag {
  padding: 2px 4px;
  border-radius: 10px;
  background-color: rgb(var(--danger-1));
  color: rgb(var(--danger-6));
  font-size: 12px;
}
</style>
