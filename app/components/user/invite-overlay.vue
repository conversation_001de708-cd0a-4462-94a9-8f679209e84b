<script setup lang="ts">
import type { AsyncDataRequestStatus } from '#app'
import type WX from 'weixin-js-sdk'

const isOverlayShow = defineModel('modelValue', { type: Boolean, default: true })

let wx: typeof WX | null = null

const state = sessionStorage.getItem('state')

interface StateInfo {
    managerName: string
    questionTitle: string
}

const stateInfo = ref<StateInfo>()

const status = ref<AsyncDataRequestStatus>('pending')

async function init() {
    const _wx = await useWxBridge({
        jsApiList: ['closeWindow'],
    })

    if (_wx)
        wx = _wx

    const { results } = await useWrapFetch<BaseResponse<StateInfo>>(`/open-api/v1/wx/inviteInfo?state=${state}`)

    stateInfo.value = results
    isOverlayShow.value = true

    status.value = 'success'
}

init()

function close() {
    wx?.closeWindow()
}
</script>

<template>
    <base-suspense :status="status">
        <van-overlay :show="isOverlayShow">
            <div flex="~ col" w-full h-full gap-16px items-center justify-center>
                <div class="background-survey-invite relative">
                    <div flex flex-col items-center justify-start>
                        <p mt-54px text="t-5 16px" font-500>
                            《{{ stateInfo?.questionTitle }}》
                        </p>

                        <p px-33px mt-8px text-t-5>
                            尊敬的用户，您有一份{{ stateInfo?.questionTitle }}邀约！
                        </p>
                    </div>

                    <div flex="~ col" items-center justify-center absolute gap-13px bottom-30px w-full>
                        <van-button type="primary" class="!w-120px !h-42px" round mt-24px @click="isOverlayShow = false">立即前往</van-button>
                        <p text-t-4 text-11px>
                            来自您的专属客户经理：{{ stateInfo?.managerName }}
                        </p>
                    </div>
                </div>
                <div class="i-custom-cross w-20px h-20px" @click="close"></div>
            </div>
        </van-overlay>
    </base-suspense>
</template>

<style lang="scss" scoped>
.background-survey-invite {
    background: url('@/assets/images/background/survey-invite.svg') no-repeat center/cover;
    --uno: w-273px h-306px;
}
</style>
