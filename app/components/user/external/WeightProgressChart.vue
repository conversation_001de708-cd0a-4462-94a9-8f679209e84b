<script setup lang="ts">
import Chart from 'chart.js/auto'
import { computed, onMounted, ref, watch } from 'vue'

interface WeightData {
    currentWeight: number | null
    targetWeight: number | null
}

const props = withDefaults(defineProps<{
    currentWeight?: number | string | null
    targetWeight?: number | string | null
}>(), {
    currentWeight: null,
    targetWeight: null,
})

const chartRef = ref<HTMLCanvasElement>()
const chartInstance = ref<Chart | null>(null)

// 验证体重数据是否有效
function isValidWeight(weight: number | string | null): weight is number {
    if (weight === null) return false
    const num = typeof weight === 'string' ? Number(weight) : weight
    return !Number.isNaN(num) && num > 0
}

// 获取有效的体重数据
const validWeights = computed<WeightData>(() => ({
    currentWeight: isValidWeight(props.currentWeight) ? Number(props.currentWeight) : null,
    targetWeight: isValidWeight(props.targetWeight) ? Number(props.targetWeight) : null,
}))

// 判断是否有足够的数据显示图表 (只需要当前和目标体重)
const hasEnoughData = computed(() => {
    const weights = validWeights.value
    return isValidWeight(weights.currentWeight) && isValidWeight(weights.targetWeight)
})

// 定义虚线网格插件
const dashedGridPlugin = {
    id: 'dashedGrid',
    beforeDraw(chart: any) {
        const { ctx: gridCtx, chartArea } = chart
        if (!chartArea) return // chartArea 可能未定义
        const { top, bottom, left, right } = chartArea
        // const yScale = scales.y // 移除未使用的 yScale

        // 绘制横向虚线网格
        gridCtx.save()
        gridCtx.beginPath()
        gridCtx.lineWidth = 1
        gridCtx.strokeStyle = '#F3F4F6' // 使用更浅的灰色
        gridCtx.setLineDash([4, 4]) // 调整虚线样式

        // 绘制 4 条横向虚线，均匀分布
        const numLines = 4
        const step = (bottom - top) / (numLines + 1)

        for (let i = 1; i <= numLines; i++) {
            const yPixel = top + step * i
            // 确保线条在图表区域内
            if (yPixel >= top && yPixel <= bottom) {
                gridCtx.moveTo(left, yPixel)
                gridCtx.lineTo(right, yPixel)
            }
        }

        gridCtx.stroke()
        gridCtx.restore()
    },
}

// 定义自定义标签插件 (替代 weightLabelsPlugin)
const customLabelsPlugin = {
    id: 'customLabels',
    afterDraw: (chart: any) => {
        const chartCtx = chart.ctx
        const meta = chart.getDatasetMeta(0) // 获取第一个数据集的元数据
        const { currentWeight, targetWeight } = validWeights.value

        // 检查元数据和数据点数量是否足够（至少需要第一个和最后一个）
        if (!meta || !meta.data || meta.data.length < 2) {
            return // 确保元数据和数据点存在
        }

        const currentPoint = meta.data[0] // 第一个点是当前体重
        const targetPoint = meta.data[meta.data.length - 1] // 最后一个点是目标体重

        const labelOffset = 50 // *** 标签距离数据点的垂直偏移 (增加，以拉开与数值的距离) ***
        const valueOffset = 15 // *** 数值距离数据点的垂直偏移 (保持不变) ***
        const kgSpacing = 2 // kg 单位与数字的间距
        const kgFont = '400 12px "PingFang SC", "Helvetica Neue", Arial, sans-serif' // kg 单位字体
        const kgText = 'kg'
        chartCtx.font = kgFont // 需要先设置字体才能测量宽度
        const kgWidth = chartCtx.measureText(kgText).width

        const currentWeightHorizontalOffset = 20 // *** 为目前体重组添加向右偏移量 ***
        const targetWeightHorizontalOffset = -20 // *** 为目标体重组添加向左偏移量 ***

        // 绘制当前体重标签和数值
        if (isValidWeight(currentWeight) && currentPoint) {
            chartCtx.save()

            // -- 修改为居中对齐 --
            // 准备数字和单位
            const numberText = `${currentWeight}`
            const numberFont = 'bold 28px "PingFang SC", "Helvetica Neue", Arial, sans-serif'
            chartCtx.font = numberFont
            const numberWidth = chartCtx.measureText(numberText).width
            const totalValueWidth = numberWidth + kgSpacing + kgWidth

            // 计算绘制位置使 [数字 kg] 整体居中于 currentPoint.x + offset
            const valueCenterX = currentPoint.x + currentWeightHorizontalOffset // *** 应用偏移量 ***
            const valueStartX = valueCenterX - totalValueWidth / 2
            const numberX = valueStartX + numberWidth // 数字右边界 X
            const kgX = numberX + kgSpacing // kg 左边界 X

            // 绘制 "目前体重" (居中于 currentPoint.x + offset)
            chartCtx.font = '400 12px "PingFang SC", "Helvetica Neue", Arial, sans-serif'
            chartCtx.fillStyle = '#666'
            chartCtx.textAlign = 'center' // 居中对齐
            chartCtx.fillText('目前体重', valueCenterX, currentPoint.y - labelOffset) // *** 使用偏移后的中心点 ***

            // 绘制 当前体重数值
            chartCtx.font = numberFont
            chartCtx.fillStyle = '#333'
            chartCtx.textAlign = 'right' // 数字右对齐
            chartCtx.fillText(numberText, numberX, currentPoint.y - valueOffset)

            // 绘制 "kg" 单位
            chartCtx.font = kgFont
            chartCtx.fillStyle = '#333'
            chartCtx.textAlign = 'left' // kg 左对齐
            chartCtx.fillText(kgText, kgX, currentPoint.y - valueOffset)
            // -- 结束修改 --

            chartCtx.restore()
        }

        // 绘制目标体重标签和数值
        if (isValidWeight(targetWeight) && targetPoint) {
            chartCtx.save()

            // -- 修改为居中对齐 --
            // 准备数字和单位
            const targetNumberText = `${targetWeight}`
            const targetNumberFont = 'bold 28px "PingFang SC", "Helvetica Neue", Arial, sans-serif'
            chartCtx.font = targetNumberFont
            const targetNumberWidth = chartCtx.measureText(targetNumberText).width
            // kg 宽度和字体已在上面获取，可重用 (kgWidth 在外部定义了)
            const targetTotalValueWidth = targetNumberWidth + kgSpacing + kgWidth

            // 计算绘制位置使 [数字 kg] 整体居中于 targetPoint.x + offset
            const targetValueCenterX = targetPoint.x + targetWeightHorizontalOffset // *** 应用偏移量 ***
            const targetValueStartX = targetValueCenterX - targetTotalValueWidth / 2
            const targetNumberX = targetValueStartX + targetNumberWidth // 数字右边界 X
            const targetKgX = targetNumberX + kgSpacing // kg 左边界 X

            // 绘制 "目标体重" (居中于 targetPoint.x + offset)
            chartCtx.font = '400 12px "PingFang SC", "Helvetica Neue", Arial, sans-serif'
            chartCtx.fillStyle = '#666'
            chartCtx.textAlign = 'center' // 居中对齐
            chartCtx.fillText('目标体重', targetValueCenterX, targetPoint.y - labelOffset) // *** 使用偏移后的中心点 ***

            // 绘制 目标体重数值
            chartCtx.font = targetNumberFont
            chartCtx.fillStyle = '#00BFA0'
            chartCtx.textAlign = 'right' // 数字右对齐
            chartCtx.fillText(targetNumberText, targetNumberX, targetPoint.y - valueOffset)

            // 绘制 "kg" 单位
            chartCtx.font = kgFont
            chartCtx.fillStyle = '#00BFA0'
            chartCtx.textAlign = 'left' // kg 左对齐
            chartCtx.fillText(kgText, targetKgX, targetPoint.y - valueOffset)
            // -- 结束修改 --

            chartCtx.restore()
        }
    },
}

function render() {
    if (!chartRef.value || !hasEnoughData.value) return

    if (chartInstance.value) {
        chartInstance.value.destroy()
    }

    const canvas = chartRef.value
    const ctx = canvas.getContext('2d')!
    const { currentWeight, targetWeight } = validWeights.value

    // 确保体重数据有效才能插值
    if (!isValidWeight(currentWeight) || !isValidWeight(targetWeight)) {
        console.warn('当前或目标体重数据无效，无法渲染图表')
        return
    }

    // --- 开始：插入中间点 ---
    const numIntermediatePoints = 8 // 定义中间点的数量 (从 4 增加到 8)

    const labels = ['当前']
    const data = [currentWeight]
    const pointRadii = [6] // 当前点的半径
    const pointHoverRadii = [8] // 当前点的悬停半径
    const pointBorderWidths = [2] // 当前点的边框宽度

    for (let i = 1; i <= numIntermediatePoints; i++) {
        labels.push('') // 中间点使用空标签
        // const intermediateWeight = currentWeight + (targetWeight - currentWeight) * (i / (numIntermediatePoints + 1)) // 旧的线性插值
        // --- 开始：计算波动值 ---
        const progress = i / (numIntermediatePoints + 1)
        const linearWeight = currentWeight + (targetWeight - currentWeight) * progress
        const totalRange = Math.abs(targetWeight - currentWeight)
        // 波动幅度，可以调整这个百分比来控制波动大小，例如 5% 的范围
        const fluctuationMagnitude = totalRange * 0.05
        // 使用正弦函数制造波动，确保起点和终点不受影响
        // 增加频率（乘以 3）以获得更蜿蜒的效果
        const fluctuation = fluctuationMagnitude * Math.sin(progress * Math.PI * 3)
        const intermediateWeight = linearWeight + fluctuation
        // --- 结束：计算波动值 ---
        data.push(intermediateWeight)
        pointRadii.push(0) // 隐藏中间点
        pointHoverRadii.push(0) // 无悬停效果
        pointBorderWidths.push(0) // 无边框
    }

    labels.push('目标') // 目标点的标签
    data.push(targetWeight)
    pointRadii.push(6) // 目标点的半径
    pointHoverRadii.push(8) // 目标点的悬停半径
    pointBorderWidths.push(2) // 目标点的边框宽度
    // --- 结束：插入中间点 ---

    // 计算有效数据的范围 (只基于当前和目标) - 保持原有逻辑计算padding
    const validOriginalData = [currentWeight, targetWeight].filter(isValidWeight)
    // 添加更多 padding 以确保标签显示完整
    const range = Math.abs(targetWeight - currentWeight)
    const padding = range * 0.3 > 5 ? range * 0.3 : 5 // 至少 5kg 的 padding
    const minWeight = Math.min(...validOriginalData) - padding
    const maxWeight = Math.max(...validOriginalData) + padding

    // 准备图表数据 (只包含当前和目标)
    // const labels = ['当前', '目标'] // 标签用于内部标识，不会显示 - 旧代码移除
    // const data = [currentWeight, targetWeight] // 旧代码移除

    chartInstance.value = new Chart(ctx, {
        type: 'line',
        data: {
            labels, // 使用包含中间点的新标签数组
            datasets: [
                {
                    label: '体重', // 单个数据集
                    data, // 使用包含中间点的新数据数组
                    fill: { // 启用填充
                        target: 'start',
                        above: 'rgba(0, 191, 160, 0.1)', // 填充颜色，使用RGBA实现透明度
                    },
                    borderColor: '#00BFA0', // 线条颜色
                    borderWidth: 3, // 线条宽度
                    tension: 0.4, // 曲线张力 (恢复到 0.4)
                    pointBackgroundColor: '#00BFA0', // 数据点填充颜色
                    pointBorderColor: 'white', // 数据点边框颜色
                    // 使用数组为每个点指定样式
                    pointBorderWidth: pointBorderWidths,
                    pointRadius: pointRadii,
                    pointHoverRadius: pointHoverRadii,
                },
                // 移除第二个 "连线" 数据集
            ],
        },
        options: {
            plugins: {
                legend: {
                    display: false, // 隐藏图例
                },
                tooltip: {
                    enabled: false, // 禁用默认提示框
                },
            },
            scales: {
                x: {
                    display: false, // 隐藏 X 轴
                    grid: {
                        display: false, // 隐藏 X 轴网格线
                    },
                    ticks: {
                        display: false, // 隐藏 X 轴刻度
                    },
                },
                y: {
                    display: false, // 隐藏 Y 轴
                    min: minWeight,
                    max: maxWeight,
                    grid: {
                        display: false, // Y轴网格线由插件绘制
                    },
                    ticks: {
                        display: false, // 隐藏 Y 轴刻度
                    },
                },
            },
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: { // 增加顶部和底部 padding 给标签留出空间
                    top: 50, // 恢复顶部 padding
                    right: 30, // 恢复右侧 padding
                    bottom: 8, // 保留底部 padding
                    left: 30, // 恢复左侧 padding
                },
            },
        },
        plugins: [dashedGridPlugin, customLabelsPlugin], // 使用更新后的插件
    })

    return chartInstance.value
}

// 更新 watch 依赖
watch(() => [props.currentWeight, props.targetWeight], () => {
    render()
})

onMounted(() => {
    render()
})
</script>

<template>
    <canvas ref="chartRef" class="w-100% h-100%"></canvas>
</template>
