<script setup lang="ts">
import type { UploaderFileListItem } from 'vant/es/uploader/types'

interface Props {
    modelValue: UploaderFileListItem[]
    maxCount?: number
    previewSize?: string | number
    uploadButtonHeight?: string | number
    showDescription?: boolean
    description?: string
    loadingText?: string
    isImageLoading?: boolean
}

interface Emits {
    (e: 'update:modelValue', value: UploaderFileListItem[]): void
    (e: 'preview', file: UploaderFileListItem): void
    (e: 'delete', file: UploaderFileListItem): void
    (e: 'beforeRead', file: File | File[]): Promise<File | File[] | undefined>
}

const props = withDefaults(defineProps<Props>(), {
    maxCount: 1,
    previewSize: '68',
    uploadButtonHeight: '68px',
    showDescription: false,
    description: '上述指标，您可以直接拍照上传',
    loadingText: '解析中...',
    isImageLoading: false,
})

const emit = defineEmits<Emits>()

const hasUploadedPictures = computed(() => props.modelValue.length > 0)

const uploadButtonStyle = computed(() => ({
    width: hasUploadedPictures.value ? '68px' : '100%',
    height: hasUploadedPictures.value ? '68px' : props.uploadButtonHeight,
}))

function handlePreview(file: UploaderFileListItem) {
    emit('preview', file)
}

function handleDelete(file: UploaderFileListItem) {
    emit('delete', file)
}

function handleBeforeRead(file: File | File[]): Promise<File | File[] | undefined> {
    return emit('beforeRead', file)
}
</script>

<template>
    <van-uploader
        :class="!hasUploadedPictures ? 'my-uploader' : ''"
        :model-value="modelValue"
        multiple
        :max-count="maxCount"
        :preview-size="previewSize"
        :before-read="handleBeforeRead"
        @preview="handlePreview"
        @delete="handleDelete"
    >
        <div
            class="border-1px border-#00AC97 border-dashed rd-10px bg-#E4FAF9 flex items-center justify-center"
            :style="uploadButtonStyle"
        >
            <div v-if="!hasUploadedPictures" class="flex flex-col items-center justify-center gap-8px">
                <div class="w-24px h-24px bg-[url('@/assets/icons/checkin/checkin-camera.svg')] bg-no-repeat bg-center bg-contain"></div>
                <div v-if="showDescription" class="text-#868F9C text-12px font-400">{{ description }}</div>
            </div>
            <div v-else class="i-custom-plus w-25px h-25px text-#C9CDD4"></div>
        </div>

        <template v-if="isImageLoading" #preview-cover>
            <div class="w-full h-full flex items-center justify-center">
                <shared-unified-loading
                    size="small"
                    :rainbow="false"
                    :text="loadingText"
                    vertical
                />
            </div>
        </template>
    </van-uploader>
</template>

<style scoped>
:deep(.my-uploader .van-uploader__input-wrapper),
:deep(.my-uploader) {
    width: 100% !important;
    height: v-bind('uploadButtonHeight') !important;
}

:deep(.van-uploader) {
    --van-uploader-border-radius: 10px;
    --van-uploader-delete-icon-size: 24px;
    --van-uploader-delete-background: #11111126;
}

:deep(.van-uploader__preview-image) {
    border: 1px solid #F2F4F7;
}

:deep(.van-uploader__preview-delete--shadow) {
    border-top-right-radius: 10px;
}
</style>
