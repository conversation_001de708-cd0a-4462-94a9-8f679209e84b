<script setup lang="ts">
defineProps<{
    data: CheckInToolsItem
}>()
</script>

<template>
    <div class="h-81px w-147px rd-10px flex flex-col gap-4px items-center justify-center" style="background: linear-gradient(131.89deg, rgba(194, 201, 214, 0.1) 8.47%, rgba(194, 201, 214, 0.05) 88.34%);">
        <div :class="data.icon" class="w-24px h-24px"></div>

        <div class="text-14px text-t-5 font-600">
            {{ data.title }}
        </div>
    </div>
</template>
