<script setup lang="ts">
const { deletable = false, addable = false, data, clickable = true } = defineProps<{
    deletable?: boolean
    addable?: boolean
    data: CheckInCard
    clickable?: boolean
}>()

const emit = defineEmits(['handleAdd', 'fetchData'])

const cardMetaInfo: Record<string, { icon: string, title: string, unit: string, path: string }> = {
    meal: {
        icon: 'i-custom-checkin-card-meal',
        path: `/user/checkin/food?tab=${getMealKindByTime()}`,
        title: '今日饮食摄入',
        unit: 'Kcal',
    },
    sport: {
        icon: 'i-custom-checkin-card-sport',
        path: '/user/checkin/exercise',
        title: '今日运动消耗',
        unit: '步',
    },
    waterIntake: {
        icon: 'i-custom-checkin-card-water',
        path: '/user/checkin/water',
        title: '喝水记录',
        unit: 'ml',
    },
    weight: {
        icon: 'i-custom-checkin-card-weight',
        path: '/user/checkin/weight',
        title: '今日体重',
        unit: 'kg',
    },
    nutrition: {
        icon: 'i-custom-checkin-card-n',
        path: '/user/checkin/nutrition',
        title: '营养记录',
        unit: '',
    },
    waistCircumference: {
        icon: 'i-custom-checkin-card-waist',
        path: '/user/checkin/waist',
        title: '腰围宽度',
        unit: 'cm',
    },
}

async function handleDelete() {
    const { closeLoading } = useLoading()
    try {
        await useWrapFetch(`/checkInCard/deleteCustomerCard/${data.id}`)
        closeLoading()
        emit('fetchData')
    } catch (error) {
        console.error(error)
    } finally {
        closeLoading()
    }
}

function handleAdd() {
    emit('handleAdd', data)
}

function handleCardClick() {
    if (clickable) {
        if (cardMetaInfo[data.cardName]!.path) {
            navigateTo(`${cardMetaInfo[data.cardName]!.path}?value=${data.value}`)
        } else {
            showToast('功能开发中，敬请期待')
        }
    }
}
</script>

<template>
    <div
        class="p-16px rd-10px relative bg-white"
        :style="{
            background: $route.path.includes('card-setting') ? 'linear-gradient(131.89deg, rgba(194, 201, 214, 0.1) 8.47%, rgba(194, 201, 214, 0.05) 88.34%)' : 'white',
        }"
        @click="handleCardClick"
    >
        <!-- <div v-if="deletable" class="i-custom-delete w-20px h-20px absolute -top-10px -right-10px" @click="handleDelete">
        </div>

        <div v-if="addable" class="i-custom-add w-20px h-20px absolute -top-10px -right-10px" @click="handleAdd">
        </div> -->
        <div>
            <div text="12px t-4">
                {{ cardMetaInfo[data.cardName]!.title }}
            </div>
        </div>

        <div flex items-center justify-between h-28px gap-10px>
            <div v-if="data.value">
                <template v-if="(data as any)?.showText">
                    <span text="t-5 11px">今日已打卡</span>
                </template>
                <template v-else>
                    <span text="t-5 18px" font-600>{{ data.value }}</span>
                    <span text="t-3 10px" ml-2px>{{ cardMetaInfo[data.cardName]!.unit }}</span>
                </template>
            </div>

            <div v-else>
                <span text="t-5 11px">今日还未更新哦</span>
            </div>

            <div class="w-24px h-24px" :class="cardMetaInfo[data.cardName]!.icon">
            </div>
        </div>
    </div>
</template>
