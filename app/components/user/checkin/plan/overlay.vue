<script setup lang="ts">
const isPlanOverlayShow = ref(false)

const planData = ref<{
    inProgressPlans: NewPlan[]
    pendingPlans: NewPlan[]
}>()

defineExpose({
    planData,
})

const dayjs = useDayjs()

function handleOverlayShow() {
    const isTodayOverlayShowed = localStorage.getItem('last-plan-overlay-show-date')
    const today = dayjs().format('YYYY-MM-DD')

    if (dayjs(isTodayOverlayShowed).isSame(today, 'day'))
        return

    isPlanOverlayShow.value = true
    localStorage.setItem('last-plan-overlay-show-date', today)
}

const showActiveAction = ref(false)

async function init() {
    const { results } = await useWrapFetch<BaseResponse<{
        inProgressPlans: NewPlan[]
        pendingPlans: NewPlan[]
    }>>('/customerPlan/create')

    planData.value = results

    const inProgressPlan = planData.value.inProgressPlans[0]
    const pendingPlan = planData.value.pendingPlans

    localStorage.setItem('currentPlanId', inProgressPlan?.planId || 'fasting')

    if (inProgressPlan && inProgressPlan.planId === 'fasting' && pendingPlan.length > 0) {
        showActiveAction.value = true
        handleOverlayShow()
    }

    else if (inProgressPlan && inProgressPlan.planId === 'fasting'
        && dayjs(inProgressPlan.startTime).isSame(dayjs(), 'day')
        && pendingPlan.length === 0) {
        showActiveAction.value = false
        handleOverlayShow()
    }
}

function handleKnow() {
    isPlanOverlayShow.value = false
}

onMounted(() => {
    init()
})
</script>

<template>
    <van-overlay :show="isPlanOverlayShow">
        <div flex="~ col" w-full h-full gap-16px items-center justify-center>
            <div flex="~ col" items-center relative>
                <div class="background-plan-overlay"></div>
                <div
                    class="w-303px rd-16px -translate-y-40px flex flex-col gap-12px pt-48px pb-24px px-20px"
                    style="background: linear-gradient(152.19deg, #EBFFF1 0%, #FFFFFF 25.06%);"
                >
                    <div class="text-18px text-center font-500 text-18px">
                        体重管理方案准备就绪
                    </div>

                    <div class="text-15px font-400 text-t-3">
                        保持乐观向上心态、勇于分享、积极寻求家庭成员及社交圈的鼓励和支持等有助于长期坚持，提高减重效果
                    </div>

                    <div v-if="showActiveAction" class="flex items-center gap-8px">
                        <div class="i-custom:tips w-18px h-18px"></div>
                        <div class="text-warning-6 text-15px">
                            建议收到益生菌后同步激活哦
                        </div>
                    </div>

                    <div class="flex flex-col items-center gap-16px">
                        <van-button round type="primary" class="!w-207px" @click="handleKnow">
                            我知道了
                        </van-button>

                        <div v-if="showActiveAction" class="text-t-5 text-16px" @click="navigateTo(`/user/checkin/plan/active`)">
                            立即激活
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </van-overlay>
</template>

<style lang="scss" scoped>
.background-plan-overlay {
    background: url('@/assets/images/background/cool.png') no-repeat center/cover;
    --uno: w-143px h-116px relative z-1000;
}

.custom-input {
  border: 1px solid rgba(78, 89, 105, 1);
  --uno: bg-transparent text-16px rd-56px h-40px w-full indent-16px mt-10px;
//   opacity: v-bind(opacity);
}

.custom-input::placeholder {
  --uno: text-t-3;
  font-size: 16px;
}
</style>
