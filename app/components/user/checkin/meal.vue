<script setup lang="ts">
const { checkInDate, meals } = defineProps<{
    checkInDate: string
    meals: string[]
}>()

const emit = defineEmits<{
    (e: 'success'): void
}>()

const showAddActionSheet = defineModel({
    default: false,
})

async function handleCheckin(kind: DietType) {
    try {
        const { results } = await useWrapFetch<BaseResponse<boolean>>('/checkInCustomerMeal/save', {
            method: 'POST',
            body: {
                checkInDate,
                kind,
            },
        })

        if (results) {
            emit('success')
            showAddActionSheet.value = false
        } else {
            showToast('打卡失败')
        }
    } catch (error) {
        showToast('打卡失败')
    }
}
</script>

<template>
    <van-action-sheet class="linear-gradient-content" :show="showAddActionSheet" @close="showAddActionSheet = false">
        <div class="p-16px space-y-16px">
            <div class="flex justify-between">
                <div class="w-14px"></div>
                <div font-600 text-16px text-t-5>饮食打卡</div>
                <div class="text-right text-16px text-t-5 i-radix-icons:cross-2 w-16px h-16px" @click="showAddActionSheet = false"></div>
            </div>

            <div flex justify-between items-center>
                <div>
                    <div text="t-5 14px" font-500>
                        早餐打卡
                    </div>
                </div>

                <van-button :disabled="meals.includes('breakfast')" type="primary" class="h-30px! rd-10px! border-none!" @click="handleCheckin('breakfast')">
                    {{ meals.includes('breakfast') ? '已打卡' : '立即打卡' }}
                </van-button>
            </div>

            <div flex justify-between items-center>
                <div>
                    <div text="t-5 14px" font-500>
                        午餐打卡
                    </div>
                </div>

                <van-button :disabled="meals.includes('lunch')" type="primary" class="h-30px! rd-10px! border-none!" @click="handleCheckin('lunch')">
                    {{ meals.includes('lunch') ? '已打卡' : '立即打卡' }}
                </van-button>
            </div>

            <div flex justify-between items-center>
                <div>
                    <div text="t-5 14px" font-500>
                        晚餐打卡
                    </div>
                </div>

                <van-button :disabled="meals.includes('dinner')" type="primary" class="h-30px! rd-10px! border-none!" @click="handleCheckin('dinner')">
                    {{ meals.includes('dinner') ? '已打卡' : '立即打卡' }}
                </van-button>
            </div>

            <div h-16px></div>
        </div>
    </van-action-sheet>
</template>
