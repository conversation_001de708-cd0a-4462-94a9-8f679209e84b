<script setup lang="ts">
import CalorieProgress from './calorie-progress.vue'

interface Props {
    visible: boolean
    progressPercent: number
    caloricIntake: number
    stepCalorieEnabled: boolean
    archiveResults?: Archives
}

interface Emits {
    (e: 'update:visible', value: boolean): void
    (e: 'update:stepCalorieEnabled', value: boolean): void
    (e: 'save', data: { foodKcalIndex: number, stepCalorieEnabled: boolean }): void
}

const props = withDefaults(defineProps<Props>(), {
    visible: false,
    progressPercent: 0,
    caloricIntake: 0,
    stepCalorieEnabled: false,
})

const emit = defineEmits<Emits>()

const showStillEat = ref(false)
const localEnableStep = ref(false)
const intakeChecked = ref<'1' | '2'>('1')
const customStilleat = ref('')
const tempStillEatNum = ref<number>(0)
const customInput = useTemplateRef('customInput')

watch(
    () => props.visible,
    (val) => {
        showStillEat.value = val
    },
    { immediate: true },
)

watch(
    () => props.stepCalorieEnabled,
    (val) => {
        localEnableStep.value = val
    },
    { immediate: true },
)

watch(showStillEat, (val) => {
    emit('update:visible', val)
})

watch(localEnableStep, (val) => {
    emit('update:stepCalorieEnabled', val)
})

function onCustomBlur() {
    if (customStilleat.value === '') {
        return
    }
    if (Number(customStilleat.value) < 1000) {
        customStilleat.value = '1000'
    }
    tempStillEatNum.value = Number(customStilleat.value)
}

watch(() => intakeChecked.value, async (val) => {
    if (val === '2') {
        await nextTick()
        const inputEl = customInput.value?.$el?.querySelector('input')
        inputEl?.focus()
    } else if (val === '1') {
        tempStillEatNum.value = props.caloricIntake
    } else {
        customStilleat.value = ''
    }
})

// 弹框打开时初始化
function handleOpen() {
    tempStillEatNum.value = props.caloricIntake
}

// 弹框关闭时重置
function handleClose() {
    intakeChecked.value = '1'
    customStilleat.value = ''
}

// 保存设置
async function handleSave() {
    if (!intakeChecked.value) return

    emit('save', {
        foodKcalIndex: tempStillEatNum.value,
        stepCalorieEnabled: localEnableStep.value,
    })

    showStillEat.value = false
}
</script>

<template>
    <van-popup
        v-model:show="showStillEat"
        round
        closeable
        :style="{ padding: '40px' }"
        @open="handleOpen"
        @close="handleClose"
    >
        <div class="flex flex-col justify-center items-center gap-8px">
            <calorie-progress
                class="w-135px h-102px"
                :progress-percent="progressPercent"
                :still-eat-num="tempStillEatNum"
            />
            <div class="text-#1D2229 text-14px font-600">
                还可以吃=推荐摄入-饮食摄入+运动消耗×0.9
            </div>
            <van-radio-group v-model="intakeChecked" checked-color="#00AC97" class="w-full flex flex-col gap-8px">
                <div class="flex justify-between items-center">
                    <van-radio name="1">
                        推荐摄入
                        <template #icon="slotProps">
                            <div
                                :class="slotProps.checked ? 'i-custom:radio-checked' : 'i-custom:radio-unchecked'"
                                class="w-16px h-16px mt-2px"
                            ></div>
                        </template>
                    </van-radio>
                    <div class="text-#4E5969 text-15px font-600">
                        {{ caloricIntake }}Kcal
                    </div>
                </div>
                <div v-if="intakeChecked === '1'" class="text-#868F9C text-11px font-400">
                    根据您的综合信息自动推荐，保证营养的同时，建立热量缺口
                </div>
                <div class="flex justify-between items-center">
                    <van-radio name="2">
                        自定义摄入
                        <template #icon="slotProps">
                            <div
                                :class="slotProps.checked ? 'i-custom:radio-checked' : 'i-custom:radio-unchecked'"
                                class="w-16px h-16px mt-2px"
                            ></div>
                        </template>
                    </van-radio>
                    <div class="flex items-center gap-4px text-#4E5969 text-15px font-600">
                        <van-field
                            ref="customInput"
                            v-model="customStilleat"
                            class="!w-90px h-27px !py-0px !px-8px border-1px border-#868F9C rd-6px"
                            type="digit"
                            placeholder="不低于1000"
                            error-message="请输入自定义摄入"
                            @blur="onCustomBlur"
                        />Kcal
                    </div>
                </div>
                <div v-if="intakeChecked === '2'" class="bg-#F2F4F7 rd-10px py-4px px-8px text-#4E5969 text-12px font-400">
                    减肥绝不能靠饿肚子，而是在保证营养的同时，让消耗的热量略高于摄入
                </div>

                <div class="flex items-center justify-between w-full mt-16px">
                    <div class="text-#4E5969 text-14px font-400">步数是否加入热量计算</div>
                    <van-switch v-model="localEnableStep" active-color="#00AC97" inactive-color="#DCDEE0" />
                </div>
            </van-radio-group>
        </div>
        <van-button
            type="primary"
            round
            class="w-full !h-43px !mt-36px"
            @click="handleSave"
        >
            保存
        </van-button>
    </van-popup>
</template>

<style lang="scss" scoped>
:deep(.van-cell:not(.van-cell--clickable):not(.van-search__field):not(.original) .van-field__control) {
    color: #868F9C !important;
    font-size: 12px !important;
    height: 27px !important;
    line-height: 27px !important;
}
</style>
