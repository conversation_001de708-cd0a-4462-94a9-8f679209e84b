<script setup lang="ts">
const props = withDefaults(defineProps<{
    progressPercent: number
    stillEatNum: number
    showArrow?: boolean
}>(), {
    showArrow: false,
})

const dot = ref({ cx: 0, cy: 0 })
const progressPath = ref<SVGPathElement | null>(null)
const dotCircle = ref<SVGCircleElement | null>(null)

function setupProgressRing() {
    const basePath = document.getElementById('basePath') as unknown as SVGPathElement
    if (!basePath || !progressPath.value || !dotCircle.value) return

    try {
        const pathLength = basePath.getTotalLength()
        const percent = Number.isFinite(props.progressPercent) ? Math.min(Math.max(props.progressPercent, 0), 1) : 0
        const point = basePath.getPointAtLength(pathLength * percent)

        dot.value = { cx: point.x, cy: point.y }

        progressPath.value.setAttribute('stroke-dasharray', `${pathLength}`)
        progressPath.value.setAttribute('stroke-dashoffset', `${pathLength * (1 - percent)}`)
        progressPath.value.setAttribute('stroke', props.stillEatNum >= 0 ? '#00AC97' : '#F98804')

        dotCircle.value.setAttribute('fill', props.stillEatNum >= 0 ? '#00AC97' : '#F98804')
    } catch (error) {
        console.error('Error in setupProgressRing:', error)
    }
}

watch([() => props.progressPercent, () => props.stillEatNum], () => {
    nextTick(() => {
        setupProgressRing()
    })
}, { immediate: true })

onMounted(() => {
    nextTick(() => {
        setupProgressRing()
    })
})
</script>

<template>
    <div class="flex-1 relative flex flex-col items-center justify-center">
        <svg class="w-full h-full" viewBox="0 0 220 150">
            <defs>
                <filter id="dotShadow" x="-20%" y="-20%" width="140%" height="140%">
                    <feDropShadow dx="0" dy="1" stdDeviation="1" flood-color="#eee" />
                </filter>
            </defs>
            <g transform="translate(-10,18)">
                <path
                    id="basePath"
                    d="M41.72,124.28 A90,90 0 1,1 198.28,124.28"
                    fill="none"
                    stroke="#F5F7FA"
                    stroke-width="20"
                    stroke-linecap="round"
                />
                <path
                    ref="progressPath"
                    d="M41.72,124.28 A90,90 0 1,1 198.28,124.28"
                    fill="none"
                    stroke="#00AC97"
                    stroke-width="20"
                    stroke-linecap="round"
                    stroke-dasharray="1000"
                    stroke-dashoffset="1000"
                />
                <circle
                    ref="dotCircle"
                    :cx="dot.cx"
                    :cy="dot.cy"
                    r="10"
                    fill="#00AC97"
                    stroke="#fff"
                    stroke-width="6"
                    filter="url(#dotShadow)"
                />
            </g>
        </svg>
        <div class="absolute left-50% top-60% -translate-x-1/2 -translate-y-1/2 w-full text-center">
            <div class="text-[32px] font-800 font-ddinpro leading-none text-[#1D2229]">
                {{ Math.round(Math.abs(stillEatNum)) }}
            </div>
            <div class="text-[14px] text-[#4E5969] flex justify-center items-center gap-4px">
                {{ stillEatNum > 0 ? `还可以吃${showArrow ? ' >' : ''}` : '热量超支' }}
            </div>
        </div>
    </div>
</template>
