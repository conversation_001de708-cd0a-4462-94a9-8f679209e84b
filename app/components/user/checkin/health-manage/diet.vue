<script setup lang="ts">
import { CHECKIN_TOOLS } from '@/utils/checkinCard'
import CalorieProgress from './calorie-progress.vue'
import CalorieIntakeSettings from './calorie-intake-settings.vue'

interface SportData {
    allKcal: number
}

interface NutritionItem {
    carbohydrates?: number
    protein?: number
    fat?: number
    dietaryFiber?: number
    [key: string]: number | undefined
}

const props = defineProps<{
    sportData: SportData | null | undefined
    selectedDate: string
    waterIntakeNum: number
    stepNum: number
    isVisible?: boolean
}>()

const router = useRouter()
const { fastingState, fastingText, fastingStatus } = useFasting()
const { passedTime, progress: progressValue, setTime } = useTimer()
const { diaryId } = storeToRefs(useDiaryStore())
const { getPatternById } = useDietPatterns()
const archiveResults = ref<Archives>()
const showStillEat = ref(false)
const stepCalorieEnabled = ref(false)
const recommendKcal = ref<number>(0)
const isHasCustomed = ref(false)
const loadingStillEat = ref(false)
const caloricIntake = ref<number>(0)
const isInitialized = ref(false)

const { data: mealListData, refresh: refreshMeal } = useAPI<any>('/checkInCustomerMeal/list', {
    method: 'POST',
    body: computed(() => ({
        checkInDate: props.selectedDate,
    })),
})

const mealIntakeNum = computed(() => {
    if (!mealListData.value?.results?.length) return 0
    return mealListData.value.results.reduce((total: number, item: { calorie?: number }) => {
        return total + (item.calorie || 0)
    }, 0)
})

const activePattern = computed(() => getPatternById(diaryId.value as PatternId))
// const waterIntakeCard = computed(() => CHECKIN_TOOLS.find(item => item.key === 'waterIntake'))
const activityCard = computed(() => CHECKIN_TOOLS.find(item => item.key === 'activity'))
const stillEatNum = computed(() => {
    const sportKcal = props.sportData?.allKcal ?? 0
    // 步数是否加入热量计算
    const result = (recommendKcal.value + sportKcal * 0.9) - mealIntakeNum.value + (stepCalorieEnabled.value ? (props?.stepNum || 0) * (Number(archiveResults.value?.archiveWeight || 0) / 2000) : 0)
    return Math.round(result)
})

const progressPercent = computed<number>(() => {
    const sportKcal = props.sportData?.allKcal ?? 0
    if (recommendKcal.value + sportKcal * 0.9 === 0) return 0
    return mealIntakeNum.value / (recommendKcal.value + sportKcal * 0.9)
})
const nutritionList = ref([
    {
        title: '碳水化合物',
        key: 'carbohydrates',
        current: 0,
        total: 0,
        unit: 'g',
        gradient: {
            from: '#00AC97',
            to: '#52D2C2',
        },
    },
    {
        title: '蛋白质',
        key: 'protein',
        current: 0,
        total: 0,
        unit: '克',
        gradient: {
            from: '#3DB5FF',
            to: '#7ECEFF',
        },
    },
    {
        title: '脂肪',
        key: 'fat',
        current: 0,
        total: 0,
        unit: '克',
        gradient: {
            from: '#FFB348',
            to: '#FFD191',
        },
    },
    {
        title: '膳食纤维',
        key: 'dietaryFiber',
        current: 0,
        total: 0,
        unit: '克',
        gradient: {
            from: '#30DD4A',
            to: '#80F692',
        },
    },
])

const getPatternName = computed(() => {
    return activePattern.value.title.startsWith('低碳水化合物膳食') ? activePattern.value.title.replace('膳食', '') : activePattern.value.title
})

async function getCaloricIntake() {
    try {
        const { results, state } = await useWrapFetch<BaseResponse<string>>('/api/user/getCaloricIntake')
        if (state === 200 && results) {
            const dietPlanKcal = Number(results)
            const computedDietPlanKcal = (Number(archiveResults.value?.archiveHeight || 0) - 105) * 25
            if (!isHasCustomed.value) {
                if (dietPlanKcal) {
                    recommendKcal.value = dietPlanKcal
                } else {
                    recommendKcal.value = computedDietPlanKcal
                }
            }
            caloricIntake.value = dietPlanKcal || computedDietPlanKcal
        }
    } catch (error) {
        console.error('获取健康计划数据失败:', error)
    }
}

async function getPreliminaryArchive() {
    const { results } = await useWrapFetch<BaseResponse<Archives>>('/user/preliminaryArchive')
    if (results) {
        archiveResults.value = results
    }
}

function processNutritionData() {
    const totalKcal = recommendKcal.value
    nutritionList.value.forEach((item) => {
        item.current = 0
        switch (item.key) {
            case 'carbohydrates':
                item.total = Math.round((totalKcal * 0.55) / 4)
                break
            case 'fat':
                item.total = Math.round((totalKcal * 0.25) / 9)
                break
            case 'protein':
                item.total = Math.round((totalKcal * 0.20) / 4)
                break
            case 'dietaryFiber':
                item.total = 30
                break
        }
    })

    if (!mealListData.value?.results?.length) return

    const meals = mealListData.value.results
    const nutritionMap = new Map(
        nutritionList.value.map(item => [item.key, item]),
    )

    meals.forEach((meal: any) => {
        try {
            const mealContent = JSON.parse(meal.mealContent)
            if (!mealContent.items?.length) return

            mealContent.items.forEach((item: NutritionItem) => {
                for (const [key, value] of Object.entries(item)) {
                    const nutrition = nutritionMap.get(key)
                    if (nutrition && typeof value === 'number') {
                        nutrition.current = Number((nutrition.current + value).toFixed(1))
                    }
                }
            })
        } catch (error) {
            console.error('解析 mealContent 失败:', error)
        }
    })
}

function getProgress(current: number, total: number) {
    if (total === 0) return 0
    return Number((current / total).toFixed(2))
}

function goToDietPattern(patternId: PatternId) {
    router.push({ path: `/user/checkin/diet-pattern/${patternId}` })
}

function handleFasting() {
    if (fastingState.value.isChallenge) {
        navigateTo('/user/checkin/fasting')
    }
}

const sportConsume = computed(() => {
    return (props.sportData?.allKcal || 0) + ((props?.stepNum || 0) * (Number(archiveResults.value?.archiveWeight || 0) / 2000))
})

watch(fastingState, (val) => {
    if (val.isChallenge === true) {
        setTime(val.startAt, val.endAt)
    }
}, { immediate: true, deep: true })

watch([() => recommendKcal.value, () => mealListData.value], () => {
    processNutritionData()
}, { immediate: true })

async function getCustomerIndex() {
    try {
        const { results } = await useWrapFetch<BaseResponse<CustomerIndex>>('/checkInCustomerIndex/get', {
            method: 'post',
            body: {
                kind: '5',
            },
        })

        recommendKcal.value = results?.foodKcalIndex || 0
        isHasCustomed.value = !!results?.foodKcalIndex
        loadingStillEat.value = true
        stepCalorieEnabled.value = results?.stepCalorieEnabled || false
    } catch (error) {
        loadingStillEat.value = true
        recommendKcal.value = 0
    }
}

async function handleCalorieIntakeSave(data: { foodKcalIndex: number, stepCalorieEnabled: boolean }) {
    try {
        await useWrapFetch('/checkInCustomerIndex/save', { method: 'post', body: {
            foodKcalIndex: data.foodKcalIndex,
            stepCalorieEnabled: data.stepCalorieEnabled,
        } })
        recommendKcal.value = data.foodKcalIndex
        stepCalorieEnabled.value = data.stepCalorieEnabled
        showStillEat.value = false
    } catch {
        showFailToast('保存失败')
    }
}

function handleStillEat() {
    if (stillEatNum.value > 0) {
        showStillEat.value = true
    }
}

async function init() {
    if (isInitialized.value) return

    await getPreliminaryArchive()
    await getCustomerIndex()
    await getCaloricIntake()
    isInitialized.value = true
}

// 只有当组件可见时才初始化
watch(
    () => props.isVisible,
    (visible) => {
        if (visible && !isInitialized.value) {
            init()
        }
    },
    { immediate: true },
)

defineExpose({
    refreshMeal,
})
</script>

<template>
    <div class="flex flex-col gap-10px">
        <div class="h-130px flex justify-between gap-10px">
            <div
                class="flex-1 flex flex-col justify-between px-12px pt-6px pb-8px rd-10px bg-white bg-[linear-gradient(200.68deg,rgba(32,212,182,0.2)_0%,rgba(32,212,182,0)_34.98%)]"
                @click="goToDietPattern(activePattern.id)"
            >
                <div class="text-#1D2229 text-14px font-600">饮食模式</div>
                <div class="w-full h-47px flex flex-col items-center justify-center py-16px rd-10px relative">
                    <div class="absolute inset-0 rd-10px border-1 border-#00AC97 bg-[linear-gradient(180deg,#00AC97_0%,rgba(0,172,151,0.15)_100%)] op-10"></div>
                    <div class="relative text-#00AC97 text-15px font-600">
                        {{ getPatternName }}
                    </div>
                </div>
                <div class="w-full text-center text-#868F9C text-11px font-400">
                    {{ activePattern.tag.replace('、', ' ') }}
                </div>
            </div>
            <div id="step1" class="flex-1 flex flex-col justify-between px-12px pt-6px pb-12px rd-10px bg-white bg-[linear-gradient(200.68deg,rgba(32,212,182,0.2)_0%,rgba(32,212,182,0)_34.98%)]">
                <div class="text-#1D2229 text-14px font-600">营养食谱</div>
                <div class="flex flex-col">
                    <div class="text-#00AC97 text-17px font-600">MNT</div>
                    <div class="text-#868F9C text-11px font-400">循证营养指导</div>
                </div>
                <div class="relative">
                    <img src="@/assets/images/checkin/health-manage/nutritionist.png" width="105" height="105" class="absolute -right-32px -top-94px" />
                    <van-button type="primary" round class="w-full !text-13px !h-32px" @click="router.push('/user/recommend/diet')">
                        个性化营养推荐
                    </van-button>
                </div>
            </div>
        </div>

        <div class="h-fit flex flex-col p-12px justify-between bg-white rd-10px">
            <div class="text-#1D2229 text-14px font-600">热量收支</div>
            <div class="flex-1 flex gap-8px justify-between">
                <calorie-progress
                    v-if="loadingStillEat"
                    id="step3"
                    :progress-percent="progressPercent"
                    :still-eat-num="stillEatNum"
                    :show-arrow="true"
                    @click="handleStillEat"
                />
                <div v-else class="flex-1 relative flex flex-col items-center justify-center">
                    <shared-unified-loading size="small" :rainbow="false" />
                </div>
                <div id="step2" class="flex-1 flex flex-col gap-8px">
                    <div
                        class="w-full h-58px bg-#F5F7FA pl-8px rd-10px flex items-center gap-8px"
                        @click="navigateTo(`/user/checkin/food?date=${selectedDate}`)"
                    >
                        <div class="i-custom:checkin-health-manage-diet w-24px h-24px"></div>
                        <div class="flex flex-col">
                            <div class="text-#1D2229 text-20px font-800 flex items-center justify-center gap-5px font-ddinpro leading-none">
                                <v-countup
                                    :options="{ useGrouping: false }"
                                    :end-val="mealIntakeNum"
                                    :start-val="0"
                                    :duration="1"
                                    :decimal-places="0"
                                />
                                <div>
                                    Kcal
                                </div>
                            </div>
                            <div class="text-#868F9C text-10px font-400">饮食摄入 ></div>
                        </div>
                    </div>
                    <div
                        class="w-full h-58px bg-#F5F7FA pl-8px rd-10px flex items-center gap-8px"
                        @click="navigateTo(`${activityCard?.path}?date=${selectedDate}`)"
                    >
                        <div class="i-custom:checkin-health-manage-sport w-24px h-24px"></div>
                        <div class="flex flex-col">
                            <div class="text-#1D2229 text-20px font-800 flex items-center justify-center gap-5px font-ddinpro leading-none">
                                <v-countup
                                    :options="{ useGrouping: false }"
                                    :end-val="sportConsume"
                                    :start-val="0"
                                    :duration="1"
                                    :decimal-places="0"
                                />
                                <div>
                                    Kcal
                                </div>
                            </div>
                            <div class="text-#868F9C text-10px font-400">运动消耗 ></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="h-165px flex justify-between gap-10px">
            <div id="step4" class="flex-1 flex flex-col gap-10px bg-white p-12px rd-10px">
                <div class="text-#1D2229 text-14px font-600">营养元素摄入</div>
                <div class="flex-1 w-full flex flex-col gap-8px">
                    <div v-for="item in nutritionList" :key="item.title" class="flex w-full gap-4px items-center justify-between">
                        <div class="text-11px text-#868F9C font-400 w-55px">{{ item.title }}</div>
                        <div class="relative flex-1 h-3px rd-6px overflow-hidden">
                            <div class="absolute inset-0 bg-#F2F4F7"></div>
                            <div
                                class="absolute inset-0 rd-6px" :style="{
                                    width: `${getProgress(item.current, item.total) * 100}%`,
                                    background: `linear-gradient(to right, ${item.gradient.from}, ${item.gradient.to})`,
                                }"
                            ></div>
                        </div>
                        <div class="text-12px text-#868F9C font-400 w-60px flex items-center"><div class="text-#1D2229">{{ item.current }}</div>/{{ item.total }}g</div>
                    </div>
                </div>
            </div>
            <div
                id="step5"
                class="w-88px h-165px rd-10px relative overflow-hidden"
                :style="{
                    background: fastingState.isChallenge ? `linear-gradient(180deg, #ffffff ${100 - progressValue}%, ${fastingState.type === 0 ? '#D8FDF8' : '#CDE4FF'} 100%)` : 'white',
                }"
            >
                <template v-if="fastingStatus">
                    <div class="absolute top-12px left-12px flex flex-col">
                        <div class="text-14px text-#1D2229 font-600">轻断食</div>
                        <div class="text-13px text-#868F9C font-400">16-8模式</div>
                    </div>
                    <div
                        class="absolute top-60% left-50% -translate-x-50% -translate-y-50% flex w-full h-full flex-col items-center justify-center"
                        @click="handleFasting"
                    >
                        <template v-if="fastingState?.isChallenge">
                            <div class="text-#1D2229 text-16px font-800 font-ddinpro">
                                {{ passedTime.hour }}:{{ passedTime.minute }}:{{ passedTime.second }}
                            </div>
                            <div class="text-#868F9C text-10px font-400">{{ fastingText }}中</div>
                        </template>
                        <template v-else>
                            <img
                                src="@/assets/images/checkin/health-manage/light-fasting.png"
                                class="w-64px h-99px"
                                @click="navigateTo('/user/checkin/fasting/start')"
                            />
                        </template>
                    </div>
                </template>
                <shared-unified-loading v-else size="small" :rainbow="false" class="absolute top-50% left-50% -translate-x-50% -translate-y-50%" />
            </div>
            <!-- <user-checkin-tools-items-card
                v-if="waterIntakeCard"
                class="w-88px! h-165px!"
                :meta="{
                    ...waterIntakeCard,
                    showQuick: false,
                    value: waterIntakeNum,
                    customCard: true,
                }"
                @click="navigateTo(waterIntakeCard.path)"
            /> -->
        </div>

        <calorie-intake-settings
            v-model:visible="showStillEat"
            v-model:step-calorie-enabled="stepCalorieEnabled"
            :progress-percent="progressPercent"
            :caloric-intake="caloricIntake"
            :archive-results="archiveResults"
            @save="handleCalorieIntakeSave"
        />
    </div>
</template>

<style lang="scss" scoped>
:deep(.van-cell:not(.van-cell--clickable):not(.van-search__field):not(.original) .van-field__control) {
    color: #868F9C !important;
    font-size: 12px !important;
    height: 27px !important;
    line-height: 27px !important;
}
</style>
