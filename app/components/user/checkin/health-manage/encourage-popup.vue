<script setup lang="ts">
import weightIcon from '@/assets/images/checkin/health-manage/encourage-weight.png?url'
import dietIcon from '@/assets/images/checkin/health-manage/encourage-diet.png?url'
import sportIcon from '@/assets/images/checkin/health-manage/encourage-sport.png?url'

type EncourageType = 'weight' | 'diet' | 'sport'

const props = withDefaults(defineProps<{
    /** 类型：体重/饮食/运动 */
    type?: EncourageType
    /** 标题（可覆盖默认：记录体重/记录饮食/记录运动） */
    title?: string
    /** 一周打卡状态（从周日到周六） */
    weekStatus?: boolean[]
    /** 连续打卡天数 */
    continuousCount?: number
}>(), {
    type: 'weight',
    weekStatus: () => [],
})

const emit = defineEmits<Emits>()
const show = defineModel<boolean>('show', { required: true })

interface Emits {
    (e: 'update:show', value: boolean): void
}

function handleClose(): void {
    emit('update:show', false)
}

const dayLabels = ['S', 'M', 'T', 'W', 'T', 'F', 'S'] as const
const defaultTitles: Record<EncourageType, string> = {
    weight: '记录体重',
    diet: '记录饮食',
    sport: '记录运动',
}

const defaultSubtexts: Record<EncourageType, string> = {
    weight: '你的坚持正在改变自己！',
    diet: '每一顿用心都是对未来的投资！',
    sport: '每一滴汗水都是对健康的定义！',
}

const normalizedWeekStatus = computed<boolean[]>(() => {
    const source = props.weekStatus ?? []
    return Array.from({ length: 7 }, (_, index) => Boolean(source[index]))
})

const computedTitle = computed<string>(() => props.title ?? defaultTitles[props.type])
const checkedDays = computed<number>(() => props.continuousCount ?? normalizedWeekStatus.value.reduce((sum, checked) => sum + (checked ? 1 : 0), 0))
const computedSubtext = computed<string>(() => defaultSubtexts[props.type])

const iconMap: Record<EncourageType, string> = {
    weight: weightIcon,
    diet: dietIcon,
    sport: sportIcon,
}
const currentIcon = computed<string>(() => iconMap[props.type])
</script>

<template>
    <van-popup
        v-model:show="show"
        round
        :style="{ width: '337px', height: '460px', padding: '20px', background: 'linear-gradient(180deg, #E5FFFD 0%, #FFFFFF 100%)' }"
        @close="handleClose"
    >
        <div class="relative w-full h-full">
            <div class="flex items-center justify-center h-29px relative">
                <div class="i-custom-slmc w-40px h-18px absolute left-0 top-1/2 -translate-y-1/2"></div>
                <div class="text-#1D2229 text-16px">{{ computedTitle }}</div>
            </div>
            <div class="flex flex-col items-center justify-center">
                <div class="w-138px h-138px">
                    <img :src="currentIcon" alt="encourage-emoji" class="w-full h-full" />
                </div>
                <div class="flex h-38px items-center gap-4px">
                    连续打卡第
                    <div class="text-24px font-600 text-primary-6">{{ checkedDays }}</div>
                    天
                </div>
                <div class="flex h-25px items-center gap-4px text-#4E5969 text-14px">
                    {{ computedSubtext }}
                </div>
                <div class="mt-8px w-full flex items-center justify-between gap-4px">
                    <div
                        v-for="(label, index) in dayLabels"
                        :key="index"
                        class="flex flex-col items-center pt-10px pb-5px"
                    >
                        <div
                            class="text-14px"
                            :class="normalizedWeekStatus[index] ? 'text-primary-6' : 'text-#4E5969'"
                        >
                            {{ label }}
                        </div>
                        <template v-if="normalizedWeekStatus[index]">
                            <div class="i-custom:check-circle-fill w-30px h-30px rd-full flex items-center justify-center"></div>
                        </template>
                        <template v-else>
                            <div class="w-30px h-30px rd-full bg-#E5E7EB"></div>
                        </template>
                    </div>
                </div>
            </div>

            <div class="flex items-center mt-16px">
                <van-button type="primary" round class="w-full !h-51px" @click="handleClose">
                    继续坚持
                </van-button>
            </div>
        </div>
    </van-popup>
</template>
