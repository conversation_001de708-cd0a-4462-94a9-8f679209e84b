<script setup lang="ts">
import dayjs from 'dayjs'

const props = defineProps<{
    modelValue: boolean
    computedWeight: number
    weightAndHeight: {
        weight: string
        height: string
    }
    bmiRangeText: string
    initialWeightLossTarget: number
    initialTargetWeight: number
}>()

const emit = defineEmits<{
    'update:modelValue': [value: boolean]
    'confirm': [data: { weightLossTarget: number, targetWeight: number }]
    'cancel': []
}>()

const weightLossTarget = ref(props.initialWeightLossTarget)
const targetWeightNum = ref(props.initialTargetWeight)

watch(() => props.initialWeightLossTarget, (newVal) => {
    weightLossTarget.value = newVal
}, { immediate: true })

watch(() => props.initialTargetWeight, (newVal) => {
    targetWeightNum.value = newVal
}, { immediate: true })

const targetWeightNumMonth = computed(() => {
    return (props.computedWeight - weightLossTarget.value).toFixed(1)
})

const bmiNum = computed(() => {
    const height = Number(props.weightAndHeight.height) / 100
    return (targetWeightNum.value / (height * height)).toFixed(1)
})

const stillNeedLoss = computed(() => {
    return (Number(props.weightAndHeight.weight) - Number(targetWeightNumMonth.value)).toFixed(1)
})

// const dayLoss = computed(() => {
//     const currentDate = dayjs()
//     const daysInMonth = currentDate.daysInMonth()
//     const dailyTarget = weightLossTarget.value / daysInMonth

//     const currentDay = currentDate.date()
//     const remainingDays = daysInMonth - currentDay + 1

//     const remainingWeightLoss = dailyTarget * remainingDays

//     return remainingWeightLoss.toFixed(1)
// })

const stillNeedMonth = computed(() => {
    const currentDate = dayjs()
    const weightDifference = Number(props.computedWeight) - targetWeightNum.value

    if (weightDifference <= 0) {
        return 'ACHIEVED'
    }

    const monthsNeeded = Math.ceil(weightDifference / weightLossTarget.value)

    if (monthsNeeded <= 0) {
        return 'ACHIEVED'
    }

    const targetDate = currentDate.add(monthsNeeded, 'month')
    return targetDate.format('YYYY年M月')
})

const show = computed({
    get: () => props.modelValue,
    set: value => emit('update:modelValue', value),
})

function handleCancel() {
    show.value = false
    emit('cancel')
}

function handleConfirm() {
    emit('confirm', {
        weightLossTarget: weightLossTarget.value,
        targetWeight: targetWeightNum.value,
    })
    show.value = false
}

const sliderRuleRef1 = useTemplateRef('sliderRuleRef1')
const sliderRuleRef2 = useTemplateRef('sliderRuleRef2')

whenever(show, async () => {
    await nextTick()
    sliderRuleRef1.value?.renderRule()
    sliderRuleRef2.value?.renderRule()
})
</script>

<template>
    <van-popup
        v-model:show="show"
        round
        :close-on-click-overlay="false"
    >
        <div class="flex flex-col gap-12px my-30px">
            <div class="w-full text-center text-#1D2229 text-18px font-600">设置减重目标</div>
            <div class="flex flex-col mx-30px">
                <div class="flex items-center justify-between gap-8px">
                    <div class="flex items-center gap-8px">
                        <div class="w-8px h-8px rd-full bg-#6AD9CB"></div>
                        <div class="text-#1D2229 text-15px font-600">每月目标</div>
                    </div>
                    <!--
                      1. 根据月初（1号）体重 / 提交问卷中体重记录，如减重目标3.0kg，1号记录：97.0，则目标为94.0
                      2. 若无，则取1号以后最早打卡记录 / 提交问卷中体重记录，根据日期按比例生成，如减重目标3.0kg，本月30天，10号记录：97.0，则目标为95.0
                      3. 若再无，则取1号以前最晚打卡记录 / 提交问卷中体重记录，根据日期按比例生成，如减重目标3.0kg，上月30天，上月20号记录：97.0，则目标为93.0
                      4. 特殊备注：打卡时判段上述逻辑，更新目标值
                    -->
                    <div class="text-#4E5969 text-14px font-400">
                        本月目标：{{ targetWeightNumMonth }}kg
                    </div>
                </div>
            </div>
            <user-checkin-slider-rule
                ref="sliderRuleRef1"
                v-model="weightLossTarget"
                class="!my-0"
                unit="kg"
                :rule-props="{
                    maxValue: 4,
                    minValue: 0.1,
                    divide: 5,
                    precision: 0.1,
                }"
            >
                <template #footer>
                    <div class="flex flex-col gap-4px w-full -mt-16px">
                        <div v-if="Number(stillNeedLoss) > 0" class="w-full flex items-center text-#4E5969 text-12px font-400 justify-center">
                            本月还需减重
                            <!-- 还需减重=当前-目标（若≤0，提示已达成目标） -->
                            <span class="text-#00AC97 text-14px font-600">{{ stillNeedLoss }}</span>
                            公斤
                        </div>
                        <div v-else class="w-full flex items-center text-#4E5969 text-12px font-400 justify-center">
                            已达成目标
                        </div>
                        <div class="flex items-center gap-8px py-4px px-8px rd-6px bg-#F5F5F5 text-#4E5969 text-10px mx-30px">
                            <div class="i-custom:recommend-tips w-12px h-12px self-start mt-1px"></div>
                            <div>医生建议每月减重控制在2-4公斤</div>
                        </div>
                    </div>
                </template>
            </user-checkin-slider-rule>
            <div class="flex flex-col mx-30px">
                <div class="flex items-center justify-between gap-8px">
                    <div class="flex items-center gap-8px">
                        <div class="w-8px h-8px rd-full bg-#6AD9CB"></div>
                        <div class="text-#1D2229 text-15px font-600">最终目标</div>
                    </div>
                    <!-- 根据身高自动折算 -->
                    <div class="text-#4E5969 text-14px font-400">折合BMI：{{ bmiNum }}</div>
                </div>
            </div>
            <user-checkin-slider-rule
                ref="sliderRuleRef2"
                v-model="targetWeightNum"
                class="!my-0"
                unit="kg"
                :rule-props="{
                    maxValue: 100,
                    minValue: 40,
                    divide: 5,
                    precision: 0.1,
                }"
            >
                <template #footer>
                    <div class="flex flex-col gap-4px w-full -mt-16px">
                        <div class="w-full flex items-center text-#4E5969 text-12px font-400 justify-center">
                            <template v-if="stillNeedMonth === 'ACHIEVED'">
                                <span class="text-#00AC97 text-14px font-600">体重已达成目标</span>
                            </template>
                            <template v-else>
                                预计将在
                                <span class="text-#00AC97 text-14px font-600">{{ stillNeedMonth }}</span>
                                达成目标
                            </template>
                        </div>
                        <div class="flex items-center gap-8px py-4px px-8px rd-6px bg-#F5F5F5 text-#4E5969 text-10px mx-30px">
                            <div class="i-custom:recommend-tips w-12px h-12px self-start mt-1px"></div>
                            <div>健康BMI为{{ bmiRangeText }}（请结合个人需求调整）</div>
                        </div>
                    </div>
                </template>
            </user-checkin-slider-rule>
            <div class="flex justify-between items-center gap-12px mx-30px">
                <van-button
                    type="primary"
                    color="#E4FAF9"
                    class="flex-1 !h-43px !text-#00AC97 !rd-10px"
                    @click="handleCancel"
                >
                    取消
                </van-button>
                <van-button
                    type="primary"
                    class="flex-1 !h-43px text-white !rd-10px"
                    @click="handleConfirm"
                >
                    确定
                </van-button>
            </div>
        </div>
    </van-popup>
</template>
