<script setup lang="ts">
import dayjs from 'dayjs'

interface CheckInCustomerData {
    data: {
        checkInCount: number
        days: CheckinDataDays[]
        start: string
    }
    starCount: number
}

interface CheckinDataDays {
    checkInCount: number
    day: string
    nutrition: number
    sport: number
    waterIntake: number
    weight: number
    mealCount: string[]
    waistCircumference: number
    sportCalorie: number
}

const { checkinType } = defineProps<{
    // checkinType: 'nutrition' | 'sport' | 'waterIntake' | 'weight' | 'mealCount' | 'sportCalorie'
    // 目前健康管理只有饮食（mealCount）、运动（sport）、体重（weight）
    checkinType: 'mealCount' | 'sport' | 'weight'
}>()
const emit = defineEmits<{
    (e: 'dataChange', data: CheckInCustomerData): void
}>()
const WEEKDAYS = ['日', '一', '二', '三', '四', '五', '六'] as const
const COLORS = {
    active: 'text-#00AC97',
    normal: 'text-#4E5969',
    disabled: 'text-#C9CDD4',
} as const

const selectedDay = defineModel<string>({
    default: dayjs().format('YYYY-MM-DD'),
})

const today = dayjs().startOf('day')
const todayStr = today.format('YYYY-MM-DD')
const checkinData = ref<CheckInCustomerData>()

function isDateAfterToday(date: string) {
    return dayjs(date).startOf('day').isAfter(today)
}

async function getByDateData(checkInDate: string) {
    const { results } = await useWrapFetch<BaseResponse<CheckInCustomerData>>('/checkInCustomerData/getByDate', {
        method: 'POST',
        body: {
            checkInDate,
        },
    })

    checkinData.value = results
    emit('dataChange', results)
}

function getTextColorClass(date: string, isAfterToday: boolean) {
    if (selectedDay.value === date) return COLORS.active
    if (isAfterToday) return COLORS.disabled
    return COLORS.normal
}

function handleDayClick(date: string) {
    if (isDateAfterToday(date)) return
    selectedDay.value = date
}

const calenderData = computed(() => {
    const startDay = today.subtract(5, 'day')

    return Array.from({ length: 7 }, (_, i) => {
        const date = startDay.add(i, 'day')
        const formattedDate = date.format('YYYY-MM-DD')
        const findCheckinData = checkinData.value?.data.days.find(day => day.day === formattedDate)
        const isCheckin = Array.isArray(findCheckinData?.[checkinType]) ? !!(findCheckinData?.[checkinType].length > 0) : !!findCheckinData?.[checkinType]
        return {
            formattedDate,
            DD: date.date(),
            chineseWeekday: formattedDate === todayStr ? '今' : WEEKDAYS[date.day()]!,
            isAfterToday: isDateAfterToday(formattedDate),
            hasCheckedIn: isCheckin,
        }
    })
})

defineExpose({
    refresh: () => {
        getByDateData(selectedDay.value)
    },
    checkinData,
})

onMounted(() => {
    getByDateData(selectedDay.value)
})
</script>

<template>
    <div class="flex gap-4px h-64px">
        <div
            v-for="day in calenderData" :key="day.formattedDate"
            class="flex-1 flex flex-col items-center justify-center rounded-10px py-10px box-border"
            :class="{
                'cursor-pointer': !day.isAfterToday,
                'cursor-not-allowed': day.isAfterToday,
                'border-1px border-dashed border-#6AD9CB': selectedDay === day.formattedDate,
            }"
            @click="handleDayClick(day.formattedDate)"
        >
            <div v-if="day.hasCheckedIn">
                <span
                    class="i-custom-check-circle-fill inline-block w-17px h-17px transition-colors duration-300"
                    :class="getTextColorClass(day.formattedDate, day.isAfterToday)"
                ></span>
            </div>
            <div
                v-else
                class="transition-colors duration-300"
                :class="getTextColorClass(day.formattedDate, day.isAfterToday)"
            >
                {{ day.chineseWeekday }}
            </div>
            <div
                font-600
                class="transition-colors duration-300"
                :class="getTextColorClass(day.formattedDate, day.isAfterToday)"
            >
                {{ day.DD }}
            </div>
        </div>
    </div>
</template>
