<script setup lang="ts">
import TimeRangeChart from './time-range-chart.vue'

interface SportData {
    allKcal: number
    detailList: Array<{
        subList?: Array<{
            sportTime?: number
        }>
    }>
}

const props = defineProps<{
    sportData: SportData | null | undefined
    selectedDate: string
    isVisible?: boolean
}>()

const stepNum = defineModel<number>('stepNum')
const isInitialized = ref(false)

const totalSportTime = computed(() => {
    if (!props.sportData?.detailList?.length) return 0
    return props.sportData.detailList.reduce((total: number, item: { subList?: Array<{ sportTime?: number }> }) => {
        return total + (item.subList?.reduce((subTotal: number, subItem: { sportTime?: number }) => {
            return subTotal + (subItem.sportTime || 0)
        }, 0) || 0)
    }, 0)
})

interface ChartData {
    labels: string[]
    data: number[]
}

const sportChartData = reactive<{
    周: ChartData
    月: ChartData
    年: ChartData
}>({
    周: {
        labels: [],
        data: [],
    },
    月: {
        labels: [],
        data: [],
    },
    年: {
        labels: [],
        data: [],
    },
})

const stepChartData = reactive<{
    周: ChartData
    月: ChartData
    年: ChartData
}>({
    周: {
        labels: [],
        data: [],
    },
    月: {
        labels: [],
        data: [],
    },
    年: {
        labels: [],
        data: [],
    },
})

const allSportData = ref<any[]>([])
const allStepData = ref<any[]>([])
const isDataLoaded = ref(false)
let isLoadingData = false // 防止并发加载

const dayjs = useDayjs()

const timeRangeConfig = {
    周: { days: 7, label: 'M月D日' },
    月: { days: 30, label: 'M月D日' },
    年: { days: 365, label: 'M月D日' },
} as const

function processDataByTimeRange(
    allData: any[],
    tab: '周' | '月' | '年',
    valueKey: string,
): ChartData {
    const config = timeRangeConfig[tab]
    const cutoffDate = dayjs().subtract(config.days, 'day')

    const filteredData = allData
        .filter(item => dayjs(item.checkInDate).isAfter(cutoffDate))
        .sort((a, b) => dayjs(a.checkInDate).diff(dayjs(b.checkInDate)))

    const labels = filteredData.map(item =>
        dayjs(item.checkInDate).format(config.label),
    )
    const data = filteredData.map(item => Number(item[valueKey]))

    return { labels, data }
}

async function fetchAllData() {
    if (isDataLoaded.value || isLoadingData) return

    isLoadingData = true
    const endDate = dayjs().format('YYYY-MM-DD')
    const startDate = dayjs().subtract(365, 'day').format('YYYY-MM-DD')

    try {
        const [sportResponse, stepResponse] = await Promise.all([
            useWrapFetch<BaseResponse<any[]>>('/checkInCustomerSport/getSportTrendData', {
                method: 'post',
                body: { startDate, endDate },
            }),
            useWrapFetch<BaseResponse<any[]>>('/checkInCustomerSport/getFootTrendData', {
                method: 'post',
                body: { startDate, endDate },
            }),
        ])

        if (Array.isArray(sportResponse.results)) {
            allSportData.value = sportResponse.results.sort((a, b) =>
                dayjs(a.checkInDate).diff(dayjs(b.checkInDate)),
            )
        }

        if (Array.isArray(stepResponse.results)) {
            allStepData.value = stepResponse.results.sort((a, b) =>
                dayjs(a.checkInDate).diff(dayjs(b.checkInDate)),
            )
        }

        isDataLoaded.value = true
    } catch (error) {
        console.error('获取数据失败:', error)
    } finally {
        isLoadingData = false
    }
}

async function getSportTrendData(tab: '周' | '月' | '年') {
    if (!isDataLoaded.value) {
        await fetchAllData()
    }

    if (allSportData.value.length > 0) {
        sportChartData[tab] = processDataByTimeRange(allSportData.value, tab, 'kcal')
    }
}

async function getStepsTrendData(tab: '周' | '月' | '年') {
    if (!isDataLoaded.value) {
        await fetchAllData()
    }

    if (allStepData.value.length > 0) {
        stepChartData[tab] = processDataByTimeRange(allStepData.value, tab, 'steps')
    }
}

function updateChartDataForTimeRange(timeRange: '周' | '月' | '年') {
    if (allSportData.value.length > 0) {
        sportChartData[timeRange] = processDataByTimeRange(allSportData.value, timeRange, 'kcal')
    }
    if (allStepData.value.length > 0) {
        stepChartData[timeRange] = processDataByTimeRange(allStepData.value, timeRange, 'steps')
    }
}

function initializeChartData() {
    const timeRanges = ['周', '月', '年'] as const
    timeRanges.forEach(updateChartDataForTimeRange)
}

async function initData() {
    if (isInitialized.value) return

    await fetchAllData()
    initializeChartData()
    isInitialized.value = true
}

watch(
    () => props.isVisible,
    (visible) => {
        if (visible && !isInitialized.value) {
            initData()
        }
    },
    { immediate: true },
)

const route = useRoute()

async function syncStepFromWx() {
    if (route.query.step) {
        const { updateWxRunData } = useWxRunData()
        const { wxRunData } = storeToRefs(useWxRunData())

        const step = Math.floor(Number(route.query.step) / 10) * 10
        const isReject = route.query.isReject === 'true'
        updateWxRunData({
            step: step.toString(),
            stepTime: route.query.stepTime as string,
            lastStepUpdateTime: route.query.lastStepUpdateTime as string,
            isReject,
        })

        const currentStep = stepNum.value || 0

        const wxStep = Number(wxRunData.value?.step || 0)
        const wxStepTime = Number(wxRunData.value?.stepTime || 0)
        if (wxStep > currentStep && dayjs.unix(wxStepTime).isSame(dayjs(), 'day')) {
            await useWrapFetch('/checkInCustomerSport/save/foot', {
                method: 'post',
                body: {
                    steps: wxStep,
                    checkInDate: dayjs().format('YYYY-MM-DD'),
                },
            })

            stepNum.value = wxStep

            await refreshData()
        }

        if (route.hash.includes('navToSport') && !sessionStorage.getItem('navToSport')) {
            sessionStorage.setItem('navToSport', 'true')
            navigateTo('/user/checkin/exercise')
        }
    }
}

async function handleClickStep() {
    await useAuthWxRun()
    navigateTo('/user/checkin/exercise')
}

async function refreshData() {
    allSportData.value = []
    allStepData.value = []
    isDataLoaded.value = false

    await fetchAllData()
    initializeChartData()
}

onUnmounted(() => {
    allSportData.value = []
    allStepData.value = []
    isDataLoaded.value = false
})

defineExpose({
    syncStepFromWx,
    refreshData,
})
</script>

<template>
    <div class="flex flex-col gap-10px">
        <div class="flex h-144px gap-8px">
            <div class="flex flex-col flex-1 justify-around bg-white rd-10px p-16px">
                <div class="w-full flex items-center">
                    <div class="flex flex-col w-80px">
                        <div class="flex items-ceeter justify-center">
                            <div class="text-#1D2229 flex items-center justify-center text-20px font-800 font-ddinpro leading-none">
                                <v-countup
                                    :options="{ useGrouping: false }"
                                    :end-val="sportData?.allKcal"
                                    :start-val="0"
                                    :duration="1"
                                    :decimal-places="0"
                                />
                            </div>
                            <div class="text-#868F9C text-12px font-400 relative top-3px">Kcal</div>
                        </div>
                        <div class="text-#868F9C text-12px font-400 mt-3px text-center">热量消耗</div>
                    </div>
                    <div class="flex flex-col w-80px">
                        <div class="flex items-center justify-center">
                            <div class="text-#1D2229 flex items-center justify-center text-20px font-800 font-ddinpro leading-none">
                                <v-countup
                                    :options="{ useGrouping: false }"
                                    :end-val="totalSportTime"
                                    :start-val="0"
                                    :duration="1"
                                    :decimal-places="0"
                                />
                            </div>
                            <div class="text-#868F9C text-12px font-400 relative top-3px">分钟</div>
                        </div>
                        <div class="text-#868F9C text-12px font-400 text-center mt-3px">运动时长</div>
                    </div>
                </div>
                <van-button
                    type="primary" color="#4E5969" round class="flex items-center justify-center gap-8px w-full !h-34px"
                    @click="navigateTo('/user/checkin/sportCalorie')"
                >
                    <div class="flex items-center justify-center w-full h-full gap-8px">
                        <div class="i-custom-plus w-16px h-16px inline-block"></div>
                        <span>记录运动</span>
                    </div>
                </van-button>
            </div>
            <div
                class="w-138px flex flex-col justify-between gap-10px bg-white p-16px rd-10px"
                @click="handleClickStep"
            >
                <div class="text-center text-#868F9C text-12px font-400">今日步数</div>
                <div class="flex justify-center">
                    <div class="i-custom:checkin-step w-32px h-32px"></div>
                </div>
                <div class="text-center text-#1D2229 text-20px font-800">
                    <v-countup
                        :options="{ useGrouping: false }"
                        :end-val="stepNum"
                        :start-val="0"
                        :duration="1"
                        :decimal-places="0"
                    />
                </div>
            </div>
        </div>
        <time-range-chart
            title="运动记录"
            subtitle="热量(Kcal)"
            :chart-data="sportChartData"
            :is-visible="isVisible"
            @tab-change="getSportTrendData"
        >
            <template #empty-state>
                <img src="@/assets/icons/checkin/sport-3.svg" class="w-32px h-32px" />
                <div
                    class="w-125px h-35px flex items-center justify-center rd-100px text-15px font-400 cursor-pointer border-1px border-#00AC97 text-#00AC97 bg-white cursor-pointer"
                    @click="navigateTo('/user/checkin/sportCalorie')"
                >
                    去记录
                </div>
            </template>
        </time-range-chart>
        <time-range-chart
            title="步数记录"
            :chart-data="stepChartData"
            :is-visible="isVisible"
            @tab-change="getStepsTrendData"
        >
            <template #empty-state>
                <img src="@/assets/icons/checkin/step.svg" class="w-32px h-32px" />
                <div
                    class="w-125px h-35px flex items-center justify-center rd-100px text-15px font-400 cursor-pointer border-1px border-#00AC97 text-#00AC97 bg-white cursor-pointer"
                    @click="handleClickStep"
                >
                    去记录
                </div>
            </template>
        </time-range-chart>
    </div>
</template>
