<script setup lang="ts">
const {
    defaultTargetValue = 500,
} = defineProps<{
    defaultTargetValue?: number
}>()

const emit = defineEmits<{
    (e: 'success'): void
}>()

const showAddActionSheet = defineModel({
    default: false,
})

const targetValue = ref()

watch(() => defaultTargetValue, () => {
    targetValue.value = defaultTargetValue
}, { immediate: true })

async function handleSave() {
    try {
        await useWrapFetch('/checkInCustomerIndex/save', { method: 'post', body: {
            sportKcalIndex: targetValue.value,
        } })
        emit('success')
    } catch {
        showFailToast('保存失败')
    }
}

const sliderRuleRef = useTemplateRef('sliderRuleRef')
whenever(showAddActionSheet, async () => {
    await nextTick()
    sliderRuleRef.value?.renderRule()
})
</script>

<template>
    <van-action-sheet class="linear-gradient-content" :show="showAddActionSheet" @close="showAddActionSheet = false">
        <div>
            <div class="flex justify-between items-center p-16px">
                <div class="text-center font-600 text-16px text-t-5">设置每日目标卡路里</div>
                <div class="text-right text-16px text-t-5 i-radix-icons:cross-2 w-16px h-16px" @click="showAddActionSheet = false"></div>
            </div>

            <user-checkin-slider-rule
                ref="sliderRuleRef"
                v-model="targetValue"
                unit="千卡"
                dialog-title="设置每日目标卡路里"
                :rule-props="{
                    maxValue: 2000,
                    minValue: 100,
                    precision: 10,
                    divide: 5,
                }"
                btn-text="确定"
                @save="handleSave"
            />

            <div h-16px></div>
        </div>
    </van-action-sheet>
</template>
