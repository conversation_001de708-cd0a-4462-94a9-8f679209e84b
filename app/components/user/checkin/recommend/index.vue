<script setup lang="ts">
const currTab = ref<'diet' | 'exercise'>('diet')

const tabs = [
    { key: 'diet' as const, label: '饮食' },
    { key: 'exercise' as const, label: '运动' },
]
</script>

<template>
    <div mx-16px>
        <!-- tab -->
        <div bg-fill-2 flex rounded-16px>
            <div
                v-for="tab in tabs" :key="tab.key"
                flex-1 flex items-center justify-center h-48px px-6px
                @click="currTab = tab.key"
            >
                <span
                    w-full text-center h-36px flex items-center justify-center rounded-10px
                    :class="[tab.key === currTab ? 'text-primary-6 bg-white font-600' : 'text-t-4']"
                >
                    {{ tab.label }}
                </span>
            </div>
        </div>

        <div pt-16px>
            <div v-if="currTab === 'diet'" space-y-16px>
                <user-checkin-recommend-diet />
            </div>
            <div v-if="currTab === 'exercise'">
                <user-checkin-recommend-exercise />
            </div>
        </div>
    </div>
</template>
