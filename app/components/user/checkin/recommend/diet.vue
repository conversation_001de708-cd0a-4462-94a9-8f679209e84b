<script setup lang="ts">
const dietType = ref<DietType>('breakfast')

const typeMap: Record<DietType, { title: string, code: number }> = {
    breakfast: {
        title: '早餐',
        code: 0,
    },
    lunch: {
        title: '午餐',
        code: 1,
    },
    dinner: {
        title: '晚餐',
        code: 2,
    },
    snack: {
        title: '加餐',
        code: 3,
    },
}

const currentDietCode = computed(() => typeMap[dietType.value].code)

const foodRecommend = ref<any[]>([])

const { data, status, refresh: refreshFoodRecommend } = useAPI<any>('/checkInFoodRecommend/get', {
    params: {
        kind: currentDietCode,
    },
})

useStatusLoading(status)

const selectedDay = inject<Ref<string>>('selectedDay')!

watch(selectedDay, () => {
    refreshFoodRecommend()
})

watch(data, (value) => {
    foodRecommend.value = value?.results.foodRecommend || []
})

async function handleRefresh(item: any, index: number) {
    try {
        const { results } = await useWrapFetch<BaseResponse<any>>('/checkInFoodRecommend/refresh', {
            method: 'post',
            body: {
                kind: typeMap[dietType.value].code,
                foodType: item.foodType,
                numbers: item.numbers,
                childFoodType: item.childFoodType,
            },
        })

        foodRecommend.value[index] = results
    } catch (error) {
        console.error(error)
    }
}

function handleToggleDietType(type: DietType) {
    dietType.value = type
    refreshFoodRecommend()
}
</script>

<template>
    <div>
        <div flex items-center space-x-8px>
            <div class="w-24px h-24px flex items-center justify-center bg-warning-1 rounded-10px">
                <span class="i-custom-checkin-diet w-12px h-12px"></span>
            </div>
            <span font-600 text-t-5>三餐推荐</span>
            <span flex-1></span>
            <span
                text-primary-6 @click="() => {
                    showSuccessToast('预约成功')
                }"
            >预约营养师</span>
        </div>
        <div grid grid-cols-3 my-16px gap-8px>
            <div class="flex flex-col items-center justify-center rounded-10px w-109px h-96px" :class="dietType === 'breakfast' ? 'bg-#F2FCFD' : 'bg-fill-1'" @click="handleToggleDietType('breakfast')">
                <img src="~/assets/images/checkin/diet/breakfast.png" w-59px h-58px />
                <span text-t-4 text-12px>早餐</span>
            </div>
            <div class="flex flex-col items-center rounded-10px justify-center w-109px h-96px" :class="dietType === 'lunch' ? 'bg-#F2FCFD' : 'bg-fill-1'" @click="handleToggleDietType('lunch')">
                <img src="~/assets/images/checkin/diet/lunch.png" w-62px h-62px />
                <span text-t-4 text-12px>午餐</span>
            </div>
            <div class="flex flex-col items-center rounded-10px justify-center w-109px h-96px" :class="dietType === 'dinner' ? 'bg-#F2FCFD' : 'bg-fill-1'" @click="handleToggleDietType('dinner')">
                <img src="~/assets/images/checkin/diet/dinner.png" w-62px h-62px />
                <span text-t-4 text-12px>晚餐</span>
            </div>
        </div>

        <div>
            <div space-y-8px>
                <div flex items-center space-x-8px>
                    <div class="w-24px h-24px flex items-center justify-center bg-primary-1 rounded-10px">
                        <span class="i-custom-checkin-diet-breakfast inline-block w-12px h-12px"></span>
                    </div>
                    <span text-t-5 font-600>{{ typeMap[dietType].title }}建议</span>
                </div>

                <p text="t-5 12px">
                    {{ data?.results.recommendTip }}
                </p>
            </div>

            <div mt-16px style="background: linear-gradient(131.89deg, rgba(194, 201, 214, 0.1) 8.47%, rgba(194, 201, 214, 0.05) 88.34%);">
                <div class="rd-10px flex justify-between items-center" style="background: linear-gradient(90deg, #FFFFFF 0%, #FEE1D0 100%);">
                    <div class="rd-10px h-full w-90px bg-#FDEBD5 text-#8C3E12 text-center leading-41px">
                        餐品推荐
                    </div>

                    <div mr-16px flex items-center gap-4px>
                        <div class="i-custom-checkin-hot w-12px h-13px">
                        </div>

                        <span text="14px t-5" font-600>
                            {{ typeMap[dietType].title }}热量：{{ ` ${data?.results.kcal} ` }} kcal
                        </span>
                    </div>
                </div>

                <div class="flex flex-col gap-12px p-16px">
                    <div v-for="(item, index) in foodRecommend" :key="item.id" flex items-center space-x-7px>
                        <van-image class="w-56px h-56px" :src="formatResource(item.foodImageName)" />
                        <div flex-1 flex flex-col space-y-4px>
                            <div>
                                <div flex items-center gap-8px>
                                    <div line-clamp-1 max-w-120px font-600 text-t-5>{{ item.foodTypeName }}</div>
                                    <div text-10px text-t-3>({{ item.childFoodType }})</div>
                                </div>
                                <div text-10px text-t-3 line-clamp-2>{{ item.foodExample }}</div>
                            </div>
                        </div>
                        <div flex space-x-2px>
                            <span text-primary-6>{{ item.weight }}</span>
                            <span>g</span>
                        </div>
                        <span i-custom-loop w-14px h-14px @click="handleRefresh(item, index)"></span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
