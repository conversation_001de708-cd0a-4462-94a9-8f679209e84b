<script setup lang="ts">
import aerobicRecommendation from '~/assets/images/checkin/exercise/aerobic-recommendation.png'
import anaerobicRecommendation from '~/assets/images/checkin/exercise/anaerobic-recommendation.png'

const exerciseType = ref<ExerciseType>('aerobic')

const typeMap: Record<ExerciseType, { title: string, code: number }> = {
    aerobic: {
        title: '有氧运动',
        code: 0,
    },
    anaerobic: {
        title: '无氧运动',
        code: 1,
    },
}
</script>

<template>
    <div flex items-center space-x-8px>
        <div class="w-24px h-24px flex items-center justify-center bg-primary-1 rounded-10px">
            <span class="i-custom-checkin-exercise-running w-12px h-12px"></span>
        </div>
        <span font-600 text-t-5>运动推荐</span>
    </div>

    <span text-t-4 text-12px>
        运动有利于身心健康，通常身体活动量应占总能量消耗的15%以上。长期缺乏运动可引起肌肉萎缩、心肺功能低下等问题，主动身体活动最好每天6000步，同时每周至少进行5天中等强度身体活动，累计150分钟以上。
    </span>

    <div flex gap-16px mt-8px>
        <div :class="exerciseType === 'aerobic' ? 'bg-[#FFE0CE]' : 'bg-fill-1'" flex-1 rd-12px gap-8px p-16px flex items-center @click="exerciseType = 'aerobic'">
            <div class="i-custom-checkin-sport-1 w-44px h-44px"></div>

            <div>
                <div text="14px t-5" font-600>
                    有氧运动
                </div>

                <div text="10px t-4">
                    每周 4-5 次
                </div>
            </div>
        </div>

        <div :class="exerciseType === 'anaerobic' ? 'bg-[#FFE0CE]' : 'bg-fill-1'" flex-1 rd-12px gap-8px p-16px flex items-center @click="exerciseType = 'anaerobic'">
            <div class="i-custom-checkin-sport-2 w-44px h-44px"></div>

            <div>
                <div text="14px t-5" font-600>
                    无氧运动
                </div>

                <div text="10px t-4">
                    每周 1-2 次
                </div>
            </div>
        </div>
    </div>

    <div mt-16px space-y-16px>
        <div space-y-8px>
            <div flex items-center space-x-8px>
                <div class="w-24px h-24px flex items-center justify-center bg-primary-1 rounded-10px">
                    <span class="i-custom-checkin-exercise inline-block w-12px h-12px"></span>
                </div>
                <span text-t-5 font-600>{{ typeMap[exerciseType].title }}</span>
            </div>

            <p text="t-5 12px">
                {{ exerciseType === 'aerobic'
                    ? '有氧运动是在有氧代谢状态下进行的运动，能使心脏收缩次数、血液输出量、氧气需求等增加，肺部收张程度变大。运动持续时，心肺需供应氧气并运走肌肉废物。它可促进血液循环和新陈代谢，增强心肺等功能，常见的有氧运动有步行、慢跑、爬山、游泳、骑自行车、健身操、瑜伽等。'
                    : '无氧运动是肌肉在 “缺氧” 状态下的高速剧烈运动。像百米冲刺、举重等剧烈或急速爆发的运动就属于无氧运动，运动时负荷强度高、瞬间性强，不能持续太久，疲劳消除也慢。常见的无氧运动有短距离赛跑、举重、投掷、跳高、拔河、肌力训练等，可增强肌肉力量，提高身体适应能力，是增加肌肉的主要来源。'
                }}
            </p>
        </div>

        <img :src="exerciseType === 'aerobic' ? aerobicRecommendation : anaerobicRecommendation" />
    </div>
</template>
