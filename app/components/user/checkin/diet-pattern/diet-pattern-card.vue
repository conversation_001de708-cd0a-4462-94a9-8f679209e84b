<script setup lang="ts">
import DietPatternBg from '~/components/user/checkin/diet-pattern/diet-pattern-bg.vue'

defineProps<{
    pattern: {
        id: string
        title: string
        tag: string
        cardBackgroundColor: string
        decorationColor: string
        icon: string
        isActive?: boolean
    }
    showActiveTag?: boolean
    size?: 'small' | 'large'
}>()

function getPatternName(title: string) {
    return title.startsWith('低碳水化合物膳食') ? title.replace('膳食', '') : title
}
</script>

<template>
    <div
        class="flex items-center p-16px rounded-10px relative"
        :class="[size === 'large' ? 'h-112px' : 'h-104px']"
        :style="{ backgroundColor: pattern.cardBackgroundColor }"
    >
        <div
            v-if="showActiveTag && pattern.isActive"
            class="absolute top-0 left-0 -translate-y-1/2 w-80px h-22px rounded-tl-10px rounded-tr-10px rounded-br-10px p-8px flex items-center justify-center text-10px text-white bg-[#F98804] z-10"
            style="transform-origin: top left"
        >
            当前使用中
        </div>
        <div class="flex flex-col">
            <div
                class="text-#4E5969 mb-4px"
                :class="[size === 'large' ? 'text-16px' : 'text-14px']"
            >
                {{ pattern.tag }}
            </div>
            <div
                class="font-medium text-#1D2229"
                :class="[size === 'large' ? 'text-24px' : 'text-17px']"
            >
                {{ getPatternName(pattern.title) }}
            </div>
        </div>
        <div
            class="absolute bottom-0 right-0 overflow-hidden h-full w-1/2"
            :class="{ 'rounded-br-10px': size !== 'large' }"
        >
            <div class="absolute bottom-0 right-0">
                <diet-pattern-bg :fill-color="pattern.decorationColor" />
            </div>
            <img
                :src="pattern.icon"
                class="absolute left-1/2 top-1/2 -translate-x-1/4 -translate-y-1/2 w-82px h-82px object-contain z-20"
            />
        </div>
    </div>
</template>
