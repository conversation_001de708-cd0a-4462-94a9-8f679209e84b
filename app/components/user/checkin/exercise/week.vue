<script setup lang="ts">
import Chart from 'chart.js/auto'
import dayjs from 'dayjs'

const { exerciseData } = defineProps<{
    exerciseData: ExerciseData[]
}>()

const chartRef = useTemplateRef('chartRef')

let chartInstance: Chart | null = null // 添加图表实例变量

function render() {
    // 销毁已存在的图表实例
    if (chartInstance) {
        chartInstance.destroy()
    }

    const canvas = chartRef.value!
    canvas.height = 200

    // 创建一个包含整周数据的数组，默认值为0
    const weekData = Array(7).fill(0)

    // 遍历 exerciseData 填充实际数据
    exerciseData.forEach((item) => {
        const dayIndex = dayjs(item.checkInDate).day() // 获取星期几(0-6)
        const adjustedIndex = dayIndex === 0 ? 6 : dayIndex - 1 // 调整为周一为0，周日为6
        weekData[adjustedIndex] = item.steps || 0
    })

    // 保存新创建的图表实例
    chartInstance = new Chart(chartRef.value!, {
        type: 'bar',
        data: {
            labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
            datasets: [
                {
                    label: '',
                    data: weekData,
                    barThickness: 20,
                    backgroundColor: '#00AC97',
                    borderColor: '#00AC97',
                },
            ],
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    position: 'top',
                    labels: {
                        boxWidth: 0,
                    },
                },
            },
        },
    })
}

watch(() => exerciseData, () => {
    render()
}, { deep: true })
</script>

<template>
    <canvas ref="chartRef"></canvas>
</template>
