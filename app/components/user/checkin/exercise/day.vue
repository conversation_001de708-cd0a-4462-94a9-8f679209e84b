<script setup lang="ts">
const { target, exerciseData } = defineProps<{
    target: string
    exerciseData: ExerciseData[]
}>()

const currentRate = ref(0)
const rate = ref(0)

watch([() => target, () => exerciseData], () => {
    let _target = 0
    if (Number(target) === 0)
        _target = 6000
    else
        _target = Number(target)

    rate.value = (Number(exerciseData[0]?.steps || 0) / Number(_target)) * 100
})
</script>

<template>
    <div flex justify-center>
        <van-circle
            v-model:current-rate="currentRate"
            :rate="rate"
            :speed="100"
            :stroke-width="70"
            text="text"
            :color="{
                '0%': '#21DEC7',
                '100%': '#00AC97',
            }"
            layer-color="#F2F3F5"
        >
            <template #default>
                <div h-full flex="~ col" justify="center">
                    <div text="t-4 14px">
                        步数
                    </div>

                    <div text="t-5 20px">
                        {{ exerciseData[0]?.steps || 0 }}
                    </div>

                    <div text="t-3 11px">
                        目标 {{ target }}
                    </div>
                </div>
            </template>
        </van-circle>
    </div>
</template>
