<script setup lang="ts">
import Chart from 'chart.js/auto'
import dayjs from 'dayjs'

const { exerciseData, date } = defineProps<{
    exerciseData: ExerciseData[]
    date: string
}>()

const chartRef = useTemplateRef('chartRef')

let chartInstance: Chart | null = null // 添加图表实例变量

// 计算需要的画布宽度（根据数据量）
function calculateCanvasWidth() {
    // 获取当月天数
    const daysInMonth = dayjs(date).daysInMonth()
    const minBarWidth = 30
    return daysInMonth * minBarWidth
}

function render() {
    if (chartInstance) {
        chartInstance.destroy()
    }

    const canvas = chartRef.value!
    const containerWidth = canvas.parentElement?.clientWidth || 800

    // 获取当月天数
    const daysInMonth = dayjs(date).daysInMonth()

    // 设置canvas尺寸
    canvas.width = Math.max(calculateCanvasWidth(), containerWidth)
    canvas.height = 200

    chartInstance = new Chart(canvas, {
        type: 'bar',
        data: {
            labels: Array.from({ length: daysInMonth }, (_, i) => `${i + 1}`),
            datasets: [
                {
                    data: Array.from({ length: daysInMonth }, (_, index) => {
                        const dayData = exerciseData.find(item =>
                            dayjs(item.checkInDate).date() === index + 1,
                        )
                        return dayData ? dayData.steps : 0
                    }),
                    borderWidth: 1,
                    backgroundColor: '#00AC97',
                    borderColor: '#00AC97',
                },
            ],
        },
        options: {
            responsive: false, // 禁用响应式
            maintainAspectRatio: false,
            scales: {
                x: {
                    ticks: {
                        autoSkip: false, // 强制显示所有标签
                    },
                },
            },
            plugins: {
                legend: {
                    display: false,
                },
            },
        },
    })
}

watch(() => exerciseData, () => {
    render()
}, { deep: true })
</script>

<template>
    <div class="chart-container">
        <canvas ref="chartRef"></canvas>
    </div>
</template>

<style scoped>
.chart-container {
  max-width: 100%;
  overflow-x: auto;
  position: relative;
  height: 200px; /* 固定容器高度 */
}

.chart-container canvas {
  height: 200px !important; /* 保持图表高度固定 */
}
</style>
