<script setup lang="ts">
const props = defineProps<{
    checkinData?: NewCheckinData
}>()

const emit = defineEmits<{
    (e: 'refresh'): void
}>()

const checkInCardList = ref<CheckInToolsItem[]>(CHECKIN_TOOLS)

async function getDiaryCount() {
    try {
        const { results } = await useWrapFetch<BaseResponse<number>>('/diary/count')
        const diaryIndex = checkInCardList.value.findIndex(item => item.key === 'diary')
        checkInCardList.value[diaryIndex]!.value = results || 0
    } catch (error) {
        console.log(error)
    }
}

getDiaryCount()

const sortedList = computed(() => {
    return checkInCardList.value.filter(item => item.status === 1).sort((a, b) => {
        return (a.sort || 0) - (b.sort || 0)
    })
})

const { cardList } = useCheckinCard()

watch(cardList, (val) => {
    if (val) {
        checkInCardList.value.forEach((item) => {
            const find = val.find(v => v.cardName === item.key)
            if (find) {
                item.status = find.status
                item.sort = find.sort
                item.id = find.id
            }
        })
    }
})
const dayjs = useDayjs()

const checkInDate = ref(dayjs().format('YYYY-MM-DD'))

async function syncStepFromWx() {
    const { wxRunData } = useWxRunData()
    const currentStep = checkInCardList.value.find(item => item.key === 'sport')?.value as number

    const wxStep = Number(wxRunData?.step || 0)
    const wxStepTime = Number(wxRunData?.stepTime || 0)
    if (wxStep > currentStep && dayjs.unix(wxStepTime).isSame(dayjs(), 'day')) {
        await useWrapFetch('/checkInCustomerSport/save/foot', {
            method: 'post',
            body: {
                steps: wxStep,
                checkInDate: checkInDate.value,
            },
        })

        checkInCardList.value.find(item => item.key === 'sport')!.value = wxStep
    }
}

watch(() => props.checkinData, (v) => {
    if (v) {
        checkInCardList.value.forEach((item) => {
            const detail = v.details.find(item2 => item2.kind === item.key)
            // 只在有新值时才更新，避免用 0 覆盖已有值
            if (detail?.result !== undefined) {
                item.value = detail.result
            }
            item.lastUpdateTime = detail?.updateTime ? `${dayjs(detail.updateTime).format('HH:mm')} 更新` : ''
        })

        syncStepFromWx()
    }
})

const { data: archiveResults } = useAPI<Archives>('/user/preliminaryArchive')

const weightAndHeight = computed(() => {
    return {
        weight: archiveResults.value?.results.archiveWeight || '',
        height: archiveResults.value?.results.archiveHeight || '',
    }
})

const quickCheckinActionSheet = ref<Record<string, boolean>>({
    weight: false,
    waterIntake: false,
    waistCircumference: false,
})

function handleQuickCheckin(item: CheckInToolsItem) {
    quickCheckinActionSheet.value[item.key] = true
}

function handleQuickCheckinSuccess(key: string, newVal: number) {
    quickCheckinActionSheet.value[key] = false
    checkInCardList.value.find(item => item.key === key)!.value = newVal
    checkInCardList.value.find(item => item.key === key)!.lastUpdateTime = `${dayjs().format('HH:mm')} 更新`

    if (key === 'weight') {
        emit('refresh')
    }
}

async function handleCheckinCardClick(item: CheckInToolsItem) {
    if (item.key === 'sport' && isOnWechatMP()) {
        const { wxRunData } = useWxRunData()
        if (wxRunData?.step === undefined) {
            const wx = await useWxBridge({})

            wx?.miniProgram.redirectTo({
                url: `/pages/run/index`,
            })

            // showCheckInExerciseActionSheet.value = true
        } else if (!dayjs.unix(Number(wxRunData?.lastStepUpdateTime)).isSame(dayjs(), 'day')) {
            const wx = await useWxBridge({})

            wx?.miniProgram.redirectTo({
                url: `/pages/run/index`,
            })
        } else {
            navigateTo(item.path)
        }
    }
    else if (item.key === 'fasting') {
        const { results } = await useWrapFetch<BaseResponse<any>>('/light-fasting-record/latestLightFastingRecord')
        if (results && !results.endTime) {
            navigateTo('/user/checkin/fasting')
        } else {
            navigateTo('/user/checkin/fasting/start')
        }
    }
    else {
        navigateTo(item.path)
    }
}
</script>

<template>
    <div>
        <div class="flex flex-wrap justify-between gap-10px">
            <user-checkin-tools-items-card
                v-for="item in sortedList"
                :key="item.key"
                :meta="item"
                @click="handleCheckinCardClick(item)"
                @quick="handleQuickCheckin(item)"
            />
        </div>

        <van-button class="!bg-white !rd-10px !h-50px !border-primary-6 !mt-16px" block @click="navigateTo('/user/checkin/card-setting')">
            <div class="flex items-center gap-4px">
                <div class="i-custom:pin w-17px h-17px"></div>
                <div class="text-primary-6 text-15px font-600">
                    设置记录工具
                </div>
            </div>
        </van-button>

        <user-checkin-weight
            v-model="quickCheckinActionSheet.weight"
            :weight-and-height
            :check-in-date
            @success="(newVal) => handleQuickCheckinSuccess('weight', newVal!)"
        />

        <user-checkin-water
            v-model="quickCheckinActionSheet.waterIntake"
            :water-intake="(checkInCardList.find(item => item.key === 'waterIntake')!.value as number)"
            :check-in-date
            @success="(newVal) => handleQuickCheckinSuccess('waterIntake', newVal!)"
        />

        <user-checkin-waist
            v-model="quickCheckinActionSheet.waistCircumference"
            :check-in-date
            :waist="(checkInCardList.find(item => item.key === 'waistCircumference')!.value as number)"
            @success="(newVal) => handleQuickCheckinSuccess('waistCircumference', newVal!)"
        />
    </div>
</template>
