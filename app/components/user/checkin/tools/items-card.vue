<script setup lang="ts">
const props = defineProps<{
    meta: CheckInToolsItem
}>()

const emit = defineEmits<{
    (e: 'quick', item: CheckInToolsItem): void
}>()

const oldVal = ref(0)

watch(() => props.meta.value, (v) => {
    setTimeout(() => {
        oldVal.value = v as number
    }, 1000)
})
</script>

<template>
    <div class="h-133px w-166px bg-#fff rd-10px p-16px flex flex-col justify-between">
        <div class="flex justify-between">
            <div>
                <div class="text-t-5 font-600 text-15px">
                    {{ meta.title }}
                </div>

                <div v-if="meta.lastUpdateTime" class="text-t-3 text-12px">
                    {{ meta.lastUpdateTime }}
                </div>

                <div v-else-if="meta.customSubTitle" class="text-t-3 text-12px">
                    {{ meta.customSubTitle }}
                </div>
            </div>

            <div v-if="meta.showQuick" class="i-custom:checkin-add w-26px h-26px" @click.stop="emit('quick', meta)">
            </div>
        </div>

        <div v-if="meta.key === 'waterIntake' && meta.customCard" class="flex flex-1 flex-col justify-between">
            <div class="flex flex-1 justify-center items-center">
                <div class="w-32px h-32px flex-shrink-0" :class="meta.icon"></div>
            </div>
            <div class="flex items-end gap-4px">
                <div class="text-24px leading-none font-800 font-ddinpro">
                    <v-countup
                        :options="{ useGrouping: false }"
                        :end-val="meta.value"
                        :start-val="oldVal"
                        :duration="1"
                        :decimal-places="meta.decimalPlaces || 0"
                    />
                </div>
                <div class="text-t-3">
                    {{ meta.unit }}
                </div>
            </div>
        </div>
        <div v-else class="flex justify-between items-end">
            <template v-if="meta.key === 'fasting'">
                <user-checkin-tools-fasting />
            </template>

            <div v-else class="flex items-end gap-4px">
                <div class="text-24px leading-none font-800 font-ddinpro">
                    <v-countup
                        :options="{ useGrouping: false }"
                        :end-val="meta.value"
                        :start-val="oldVal"
                        :duration="1"
                        :decimal-places="meta.decimalPlaces || 0"
                    />
                </div>
                <div class="text-t-3">
                    {{ meta.unit }}
                </div>
            </div>

            <div class="w-32px h-32px flex-shrink-0" :class="meta.icon">
            </div>
        </div>
    </div>
</template>
