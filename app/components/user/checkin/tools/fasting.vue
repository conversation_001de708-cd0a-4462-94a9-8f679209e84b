<script setup lang="ts">
const { fastingState, fastingText, getLatestLightFastingRecord } = useFasting()

const { setTime, remainingTime, isReachEndTime } = useTimer()

watch(fastingState, (val) => {
    if (val?.isChallenge && val?.startAt) {
        setTime(val.startAt, val.endAt)
    }
}, { immediate: true, deep: true })

const refreshFastingCard = inject<Ref<boolean>>('refreshFastingCard')!

watch(refreshFastingCard, async (val) => {
    if (val) {
        await getLatestLightFastingRecord()
        refreshFastingCard.value = false
    }
})
</script>

<template>
    <div v-if="fastingState?.isChallenge" class="flex flex-col justify-end">
        <div class="text-t-3 text-11px">
            {{ fastingText }}时间{{ isReachEndTime ? '已超时' : '剩余' }}
        </div>

        <div class="text-t-5 text-20px font-800 font-ddinpro leading-none">
            {{ remainingTime.hour }} : {{ remainingTime.minute }} : {{ remainingTime.second }}
        </div>
    </div>

    <div v-else class="text-t-3 text-11px">
        轻断食挑战未开始
    </div>
</template>
