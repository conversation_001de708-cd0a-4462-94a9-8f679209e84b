<script setup lang="ts">
import Chart from 'chart.js/auto'

const props = withDefaults(defineProps<Props>(), {
    isVisible: true,
})
const emit = defineEmits<{
    (e: 'tabChange', tab: TabType): void
}>()
const TABS = ['周', '月', '年'] as const
type TabType = (typeof TABS)[number]

const POINT_WIDTH = 50
const CANVAS_HEIGHT = 180

const CHART_COLORS = {
    primary: '#00AC97',
    grid: '#F2F4F7',
    gradientStart: 'rgba(181, 243, 235, 0.4)',
    gradientEnd: 'rgba(255, 255, 255, 0.4)',
    transparent: 'rgba(0,0,0,0)',
} as const

interface ChartDataType {
    labels: string[]
    data: number[]
}
interface ChartDataMap {
    周: ChartDataType
    月: ChartDataType
    年: ChartDataType
}
interface Props {
    title?: string
    subtitle?: string
    chartData: ChartDataMap
    isVisible?: boolean
}

const chartCanvasRef = ref<HTMLCanvasElement | null>(null)
const activeTab = ref(0)
let chartInstance: Chart | null = null

const currentTab = computed<TabType>(() => TABS[activeTab.value] ?? '周')
const currentChartData = computed(() => props.chartData[currentTab.value])
const isChartDataValid = computed(() => Boolean(currentChartData.value?.labels?.length))
const isEmpty = computed(() => !currentChartData.value?.data?.length)

defineExpose({ currentTab })

function createGradientBackground(chart: Chart) {
    const { chartArea, ctx } = chart
    if (!chartArea) return CHART_COLORS.transparent
    const gradient = ctx.createLinearGradient(0, chartArea.top, 0, chartArea.bottom)
    gradient.addColorStop(0, CHART_COLORS.gradientStart)
    gradient.addColorStop(0.8571, CHART_COLORS.gradientEnd)
    return gradient
}

function renderChart(): void {
    try {
        if (!isChartDataValid.value || props.isVisible === false) return
        if (chartInstance) chartInstance.destroy()
        const canvas = chartCanvasRef.value
        if (!canvas) return
        const container = canvas.parentElement
        if (!container) return
        if (container.clientWidth === 0) {
            nextTick(() => {
                if (props.isVisible !== false) renderChart()
            })
            return
        }
        const canvasWidth = Math.max(currentChartData.value.labels.length * POINT_WIDTH, container.clientWidth)
        canvas.width = canvasWidth
        canvas.height = CANVAS_HEIGHT
        chartInstance = new Chart(canvas.getContext('2d')!, {
            type: 'line',
            data: {
                labels: currentChartData.value.labels,
                datasets: [{
                    data: currentChartData.value.data,
                    borderColor: CHART_COLORS.primary,
                    pointBackgroundColor: CHART_COLORS.primary,
                    pointBorderColor: CHART_COLORS.primary,
                    pointRadius: 2,
                    borderWidth: 2,
                    tension: 0.4,
                    fill: 'start',
                    backgroundColor: context => createGradientBackground(context.chart),
                }],
            },
            options: {
                responsive: false,
                maintainAspectRatio: false,
                plugins: {
                    legend: { display: false },
                    tooltip: {
                        enabled: true,
                        mode: 'nearest',
                        intersect: true,
                        displayColors: false,
                        backgroundColor: '#E4FAF9',
                        bodyColor: '#00AC97',
                        callbacks: {
                            label: context => `${context.parsed.y} cm`,
                            title: () => '',
                        },
                    },
                },
                scales: {
                    y: {
                        beginAtZero: false,
                        grid: {
                            color: CHART_COLORS.grid,
                            display: true,
                            drawTicks: false,
                            drawOnChartArea: true,
                        },
                        border: {
                            display: true,
                            dash: [4, 4],
                            color: CHART_COLORS.grid,
                            width: 1,
                        },
                    },
                    x: {
                        grid: {
                            color: CHART_COLORS.grid,
                            display: true,
                            drawTicks: false,
                            drawOnChartArea: true,
                        },
                        border: {
                            display: true,
                            dash: [],
                            color: CHART_COLORS.grid,
                            width: 1,
                        },
                        ticks: {
                            autoSkip: false,
                            minRotation: 0,
                            maxRotation: 0,
                        },
                    },
                },
            },
        })
    } catch (error) {
        console.error('Error rendering chart:', error)
    }
}

function handleTabClick(index: number): void {
    if (activeTab.value === index) return
    activeTab.value = index
    emit('tabChange', TABS[index]!)
}

onMounted(() => renderChart())
onUnmounted(() => chartInstance?.destroy())

watch(
    () => props.chartData,
    () => { if (isChartDataValid.value) renderChart() },
    { deep: true },
)
watch(
    () => props.isVisible,
    (newVisible) => { if (newVisible) nextTick(() => renderChart()) },
)
</script>

<template>
    <div class="w-full flex flex-col bg-white rd-10px p-12px h-233px" :class="$attrs.class">
        <div class="flex justify-between items-center w-full">
            <slot name="title">
                <div class="flex items-center">
                    <h3 class="text-#1D2229 text-14px font-600">{{ title }}</h3>
                    <p v-if="subtitle" class="text-#868F9C text-12px font-400 ml-8px">{{ subtitle }}</p>
                </div>
            </slot>
            <div class="flex w-140px h-25px bg-#F2F4F7 rd-8px p-2px">
                <button
                    v-for="(tab, index) in TABS"
                    :key="tab"
                    class="h-full flex-1 flex items-center justify-center rd-6px text-12px cursor-pointer transition-all duration-300 border-none bg-transparent text-#4E5969 font-400"
                    :class="{ 'tab-button--active': activeTab === index }"
                    @click="handleTabClick(index)"
                >
                    {{ tab }}
                </button>
            </div>
        </div>
        <div class="chart-container w-full h-fit relative overflow-y-hidden scrollbar-hide">
            <canvas ref="chartCanvasRef" class="h-180px block no-swipe"></canvas>
            <div v-if="isEmpty && activeTab === 0" class="absolute inset-0 flex flex-col items-center justify-center gap-8px empty-chart-bg">
                <slot name="empty-state"></slot>
            </div>
        </div>
    </div>
</template>

<style scoped>
.tab-button--active {
    @apply bg-white text-#1D2229 font-600;
}

.empty-chart-bg {
    background: url('@/assets/images/service/empty-chart.png') center center no-repeat, #FFFFFF96;
    background-size: 100% 100%;
}

.chart-container {
    scrollbar-width: thin;
    scrollbar-color: #b4bcc8 #f2f4f7;
}

.chart-container::-webkit-scrollbar {
    height: 8px;
    background: #f2f4f7;
    border-radius: 8px;
}

.chart-container::-webkit-scrollbar-thumb {
    background: linear-gradient(90deg, #e5e7eb 10%, #b4bcc8 90%);
    border-radius: 8px;
    border: 2px solid #f2f4f7;
    min-width: 30px;
    transition: background 0.3s;
}

.chart-container::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(90deg, #00AC97 10%, #b4bcc8 90%);
}

.chart-container::-webkit-scrollbar-corner {
    background: #f2f4f7;
}
</style>
