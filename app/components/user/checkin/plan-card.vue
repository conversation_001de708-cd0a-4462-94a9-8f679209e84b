<script setup lang="ts">
const { data: archiveResults, refresh: refreshArchive } = useAPI<Archives>('/user/preliminaryArchive')
const { data: initialWeight, refresh: refreshInitialWeight } = useAPI<string>('/user/getInitialWeight')
const { data: customerIndexResults, refresh: refreshCustomerIndex } = useAPI<CustomerIndex>('/checkInCustomerIndex/get', { method: 'post', body: {} })

const weightAndHeight = computed(() => {
    return {
        weight: archiveResults.value?.results.archiveWeight || '',
        height: archiveResults.value?.results.archiveHeight || '',
    }
})

defineExpose({
    refreshArchive,
})

const dayjs = useDayjs()

const showWeightTargetSheet = ref(false)
const showWeightSheet = ref(false)
const showInitialWeightSheet = ref(false)

const planOverlayRef = useTemplateRef<any>('planOverlayRef')

const planMetaData = computed(() => {
    const dataFromOverlay = planOverlayRef.value?.planData

    if (!dataFromOverlay) {
        return {
            planName: '',
        }
    }

    // const planName = dataFromOverlay.inProgressPlans[0].planId

    const planId = dataFromOverlay.inProgressPlans[0]?.planId || 'fasting'
    const period = dataFromOverlay.inProgressPlans[0]?.period || 0
    const startTime = dataFromOverlay.inProgressPlans[0]?.startTime || ''
    const endTime = dataFromOverlay.inProgressPlans[0]?.endTime || ''

    const fromNowDays = dayjs().diff(dayjs(startTime), 'day') + 1

    return {
        planId,
        planName: PLAN_ID_MAP[planId as keyof typeof PLAN_ID_MAP],
        period,
        startTime,
        endTime,
        fromNowDays,
    }
})

async function handlePlanClick() {
    const wx = await useWxBridge({})

    wx?.miniProgram.navigateTo({
        url: `/pages/order/new-pay?planId=${planMetaData.value?.planId}`,
    })
}
</script>

<template>
    <div>
        <div class="rd-10px pt-12px px-16px text-#fff pb-30px h-138px" style="background: linear-gradient(180deg, #00AC97 0%, #52D2C2 93.5%);">
            <div class="flex items-center justify-between">
                <div class="flex items-center" @click="handlePlanClick">
                    <div class="i-custom:checkin-plan w-20px h-20px"></div>

                    <div class="text-14px ml-4px font-600">
                        {{ planMetaData?.planName }}
                    </div>
                    <div class="i-radix-icons:caret-right w-20px h-20px"></div>
                </div>

                <template v-if="planMetaData.period !== 0">
                    <div class="flex items-center gap-4px" @click="navigateTo(`/user/checkin/plan/active`)">
                        第
                        <div class="h-20px bg-#fff rd-4px p-4px text-primary-6 font-600 flex items-center justify-center">
                            {{ planMetaData.fromNowDays }}/{{ planMetaData.period }}
                        </div>
                        天
                    </div>
                </template>

                <template v-else>
                    <div class="flex items-center gap-4px" @click="navigateTo(`/user/checkin/plan/active`)">
                        第
                        <div class="h-20px bg-#fff rd-4px p-4px text-primary-6 font-600 flex items-center justify-center">
                            {{ planMetaData.fromNowDays }}
                        </div>
                        天
                    </div>
                </template>
            </div>

            <div class="flex justify-between">
                <div class="flex flex-col items-center" @click="showInitialWeightSheet = true">
                    <div class="flex items-end gap-4px mt-20px">
                        <div class="font-800 text-#fff leading-none text-32px font-ddinpro">
                            <v-countup :options="{ useGrouping: false }" :end-val="initialWeight?.results || 0" :decimal-places="1" :duration="1" />
                        </div>

                        <div>
                            kg
                        </div>
                    </div>

                    <div class="text-12px mt-4px">
                        初始体重
                    </div>
                </div>

                <div class="flex flex-col items-center" @click="showWeightSheet = true">
                    <div class="flex items-end gap-4px mt-20px">
                        <div class="font-800 text-#fff leading-none text-32px font-ddinpro">
                            <v-countup :options="{ useGrouping: false }" :end-val="archiveResults?.results.archiveWeight || 0" :decimal-places="1" :duration="1" />
                        </div>

                        <div>
                            kg
                        </div>
                    </div>

                    <div class="text-12px mt-4px">
                        当前体重
                    </div>
                </div>

                <div class="flex flex-col items-center" @click="showWeightTargetSheet = true">
                    <div class="flex items-end gap-4px mt-20px">
                        <div class="font-800 text-#fff leading-none text-32px font-ddinpro">
                            <span v-if="customerIndexResults?.results.weightIndex">
                                <v-countup :options="{ useGrouping: false }" :end-val="customerIndexResults?.results.weightIndex || 0" :decimal-places="1" :duration="1" />
                            </span>
                            <span v-else>
                                -
                            </span>
                        </div>

                        <div v-if="customerIndexResults?.results.weightIndex">
                            kg
                        </div>
                    </div>

                    <div class="text-12px mt-4px">
                        目标体重
                    </div>
                </div>
            </div>
        </div>

        <user-checkin-weight
            v-model="showWeightSheet"
            :weight-and-height="weightAndHeight"
            @success="() => {
                refreshArchive()
                showWeightSheet = false
            }"
        />

        <user-checkin-weight-target
            v-model="showWeightTargetSheet" @success="() => {
                refreshCustomerIndex()
            }"
        />

        <user-checkin-initial-weight
            v-model="showInitialWeightSheet"
            :initial-weight="Number(initialWeight?.results)"
            @success="() => {
                refreshInitialWeight()
                showInitialWeightSheet = false
            }"
        />
        <user-checkin-plan-overlay ref="planOverlayRef" />
    </div>
</template>
