<script setup lang="ts">
import foodTipsBg from '@/assets/images/checkin/food-tips.svg'

const background = computed(() => {
    return `url(${foodTipsBg}) no-repeat center/cover`
})

const isOverlayShow = defineModel('modelValue', { type: Boolean, default: true })
</script>

<template>
    <van-overlay :show="isOverlayShow" teleport="body">
        <div flex="~ col" w-full h-full gap-16px items-center justify-center>
            <div class="food-tips-bg">
                <van-button
                    class="bg-#E4FAF9! text-primary-6! relative bottom-30px! !h-50px !w-145px"
                    border="1px! solid! #E4FAF9!"
                    round
                    @click="isOverlayShow = false"
                >
                    知道了
                </van-button>
            </div>
        </div>
    </van-overlay>
</template>

<style lang="scss" scoped>
.food-tips-bg {
    background: v-bind(background);
    --uno: w-303px h-480px flex justify-center items-end;
}
</style>
