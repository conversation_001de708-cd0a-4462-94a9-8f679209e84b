<script setup lang="ts">
const { activeKey } = defineProps<{
    activeKey: string
}>()

const boxShadow = computed(() => {
    return activeKey === 'nutrition' ? '0px 0px 10px 0px rgba(0, 0, 0, 0.2)' : 'none'
})
</script>

<template>
    <div class="bg-mask">
        <div class="flex items-center gap-4px">
            <div class="i-custom-checkin-card-top-n w-28px h-28px"></div>

            <div text="14px t-5" font-600>
                营养
            </div>
        </div>

        <div class="flex justify-center flex-col items-center">
            <div class="i-custom-checkin-card-center-n w-78px h-78px"></div>

            <div text="14px t-5" relative bottom-8px font-600>
                健康每益天
            </div>
        </div>

        <div class="flex justify-center">
            <div class="w-82px flex items-center justify-center h-24px rd-20px text-10px w-82px h-24px bg-#5CFFF7" border="2px solid t-5">
                记录营养
            </div>
        </div>
    </div>
</template>

<style scoped>
.bg-mask {
    background: url('@/assets/images/checkin/cards/nutrition-bg.png') no-repeat center center / 100% 100%;
    backdrop-filter: blur(50px);
    --uno: w-148px h-211px rd-10px px-12px py-12px pb-15px flex flex-col justify-between;
    box-shadow: v-bind(boxShadow);
}
</style>
