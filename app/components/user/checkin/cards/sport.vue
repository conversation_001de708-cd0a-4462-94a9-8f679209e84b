<script setup lang="ts">
const { value, activeIndex } = defineProps<{
    value: number
    activeIndex: number
}>()

const boxShadow = computed(() => {
    return activeIndex === 3 ? '0px 0px 10px 0px rgba(0, 0, 0, 0.2)' : 'none'
})
</script>

<template>
    <div class="bg-mask">
        <div class="flex items-center gap-4px">
            <div class="i-custom-checkin-card-top-sport w-28px h-28px"></div>

            <div text="14px t-5" font-600>
                运动
            </div>
        </div>

        <div class="flex justify-center flex-col items-center">
            <div class="i-custom-checkin-card-center-sport w-78px h-78px"></div>

            <div class="relative bottom-12px flex items-center mt-4px gap-4px text-t-5! text-14px! font-600!">
                <v-countup :options="{ useGrouping: false }" :end-val="getCeilSportValue(value)" />
                <div>
                    步
                </div>
            </div>
        </div>

        <div class="flex justify-center">
            <div class="w-82px flex items-center justify-center h-24px rd-20px text-10px w-82px h-24px bg-#07D8BF" border="2px solid t-5">
                记录步数
            </div>
        </div>
    </div>
</template>

<style scoped>
.bg-mask {
    background: url('@/assets/images/checkin/cards/sport-bg.png') no-repeat center center / 100% 100%;
    --uno: w-148px h-211px rd-10px px-12px py-12px pb-15px flex flex-col justify-between relative;
    box-shadow: v-bind(boxShadow);
}
</style>
