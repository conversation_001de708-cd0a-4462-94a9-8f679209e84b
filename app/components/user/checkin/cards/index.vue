<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'

import 'swiper/css'

import 'swiper/css/effect-coverflow'
import 'swiper/css/pagination'
import { EffectCoverflow } from 'swiper/modules'

import WaterCard from '@/components/user/checkin/cards/water.vue'
import NutritionCard from '@/components/user/checkin/cards/nutrition.vue'
import SportCard from '@/components/user/checkin/cards/sport.vue'
import FoodCard from '@/components/user/checkin/cards/food.vue'
import WeightCard from '@/components/user/checkin/cards/weight.vue'
import WaistCard from '@/components/user/checkin/cards/waist.vue'

const { checkInDate } = defineProps<{
    checkInDate: string
}>()

const modules = [EffectCoverflow]

const activeIndex = ref(2)

const showCheckInExerciseActionSheet = ref(false)
const showCheckInWaterActionSheet = ref(false)
const showCheckInWeightActionSheet = ref(false)
const showCheckInNutritionActionSheet = ref(false)
const showCheckInMealActionSheet = ref(false)
const showCheckInWaistActionSheet = ref(false)

const route = useRoute()
onMounted(() => {
    if (route.hash.includes('sportDialogOpen') && !sessionStorage.getItem('sportDialogOpen')) {
        showCheckInExerciseActionSheet.value = true

        sessionStorage.setItem('sportDialogOpen', 'true')

        // 清除 hash 中的 sportDialogOpen
        // history.replaceState(null, document.title, window.location.pathname + window.location.search)
    }
})

const dayjs = useDayjs()

const cards = ref<CheckInCardData[]>([
    {
        key: 'nutrition',
        component: markRaw(NutritionCard),
        value: 0,
        showText: true,
        leftText: () => {
            return `每日营养别忘记`
        },
        rightText: '',
        color: '#15D323',
        onClick: async () => {
            try {
                navigateTo('/user/checkin/nutrition')
                // if (!dayjs(checkInDate).isSame(dayjs(), 'day')) {
                //     showToast('只允许当天打卡')
                //     return false
                // }

                // if (cards.value[0]?.value === 1) {
                //     showToast('今日已打卡')
                //     return false
                // }

                // showCheckInNutritionActionSheet.value = true

                // await useWrapFetch('/checkInCustomerNutrition/save', {
                //     method: 'post',
                //     body: {
                //         checkInDate,
                //     },
                // })

                // getCheckInDataByDate(checkInDate)

                // showToast('打卡成功')
            } catch (error) {
                showToast('打卡失败')
            }

            // showCheckInExerciseActionSheet.value = true
        },
    },
    {
        key: 'waterIntake',
        component: markRaw(WaterCard),
        value: 0,
        leftText: (v: number) => {
            return `每日 ${v} ml`
        },
        rightText: '',
        color: '#23C4FF',
        onClick: () => {
            if (!dayjs(checkInDate).isSame(dayjs(), 'day')) {
                showToast('只允许当天打卡')
                return
            }
            showCheckInWaterActionSheet.value = true
        },
    },
    {
        key: 'weight',
        component: markRaw(WeightCard),
        value: 0,
        leftText: () => {
            return `今日体重`
        },
        rightText: '',
        color: '#B9A7FF',
        onClick: () => {
            if (!dayjs(checkInDate).isSame(dayjs(), 'day')) {
                showToast('只允许当天打卡')
                return
            }
            showCheckInWeightActionSheet.value = true
        },
    },
    {
        key: 'sport',
        component: markRaw(SportCard),
        value: 0,
        leftText: (v: number) => {
            return `每日 ${getCeilSportValue(v)} 步`
        },
        rightText: '',
        color: '#00D3BA',
        onClick: async () => {
            if (!dayjs(checkInDate).isSame(dayjs(), 'day')) {
                showToast('只允许当天打卡')
                return
            }

            if (!isOnWechatMP()) {
                showCheckInExerciseActionSheet.value = true
                return
            }

            const { wxRunData } = useWxRunData()

            if (wxRunData?.step === undefined) {
                const wx = await useWxBridge({})

                wx?.miniProgram.redirectTo({
                    url: `/pages/run/index`,
                })

                // showCheckInExerciseActionSheet.value = true
            } else if (!dayjs.unix(Number(wxRunData?.lastStepUpdateTime)).isSame(dayjs(), 'day')) {
                const wx = await useWxBridge({})

                wx?.miniProgram.redirectTo({
                    url: `/pages/run/index`,
                })
            } else {
                showCheckInExerciseActionSheet.value = true
            }
        },
    },
    {
        key: 'meal',
        component: markRaw(FoodCard),
        value: [],
        leftText: () => {
            return `三餐要按时`
        },
        rightText: '0/3',
        color: '#FFB62F',
        showText: true,
        onClick: () => {
            navigateTo(`/user/checkin/food?tab=${getMealKindByTime()}`)
        },
    },

    {
        key: 'waistCircumference',
        component: markRaw(WaistCard),
        value: 0,
        leftText: () => {
            return `今日腰围`
        },
        rightText: '',
        color: '#F98804',
        onClick: () => {
            if (!dayjs(checkInDate).isSame(dayjs(), 'day')) {
                showToast('只允许当天打卡')
                return
            }
            showCheckInWaistActionSheet.value = true
        },
    },
])

const activeKey = computed(() => {
    return cards.value[activeIndex.value]?.key
})

const waterIntake = computed(() => {
    const findWater = cards.value.find(item => item.key === 'waterIntake')
    return (findWater?.value || 0) as number
})

const { results: archiveResults } = await useWrapFetch<BaseResponse<Archives>>('/user/preliminaryArchive')

const weightAndHeight = computed(() => {
    return {
        weight: archiveResults.archiveWeight,
        height: archiveResults.archiveHeight,
    }
})

const sport = computed(() => {
    const findSport = cards.value.find(item => item.key === 'sport')
    return (findSport?.value || 0) as number
})

const waist = computed(() => {
    const findWaist = cards.value.find(item => item.key === 'waistCircumference')
    return (findWaist?.value || 0) as number
})

const percentage = ref(0)

function handleSlideChange(e: any) {
    activeIndex.value = e.activeIndex
    const key = cards.value[activeIndex.value]?.key
    switch (key) {
        case 'nutrition': {
            const findNutrition = cards.value.find(item => item.key === 'nutrition')
            const value = (findNutrition?.value || 0) as number
            percentage.value = Math.min(100, (value / 1) * 100)
            break
        }

        case 'waterIntake': {
            const findWater = cards.value.find(item => item.key === 'waterIntake')
            const value = (findWater?.value || 0) as number
            const target = Number(findWater?.rightText || 1700)
            percentage.value = Math.min(100, (value / target) * 100)
            break
        }

        case 'sport': {
            const findSport = cards.value.find(item => item.key === 'sport')
            const value = (findSport?.value || 0) as number
            const target = Number(findSport?.rightText || 6000)

            percentage.value = Math.min(100, (value / target) * 100)
            break
        }

        case 'meal': {
            const findMeal = cards.value.find(item => item.key === 'meal')
            const value = (findMeal?.value || 0) as number

            percentage.value = Math.min(100, (value / 3) * 100)
            break
        }

        case 'weight': {
            const findWeight = cards.value.find(item => item.key === 'weight')
            const value = (findWeight?.value || 0) as number
            percentage.value = Math.min(100, (value / 1) * 100)
            break
        }

        case 'waistCircumference': {
            const findWaist = cards.value.find(item => item.key === 'waistCircumference')
            const value = (findWaist?.value || 0) as number
            percentage.value = Math.min(100, (value / 1) * 100)
            break
        }
        default:
            percentage.value = 0
            break
    }
}

const isCheckinDateFetching = ref(false)

async function getCheckInDataByDate(date: string, refreshProgress = false, updateFromeWx = false) {
    try {
        if (isCheckinDateFetching.value) {
            return
        }

        isCheckinDateFetching.value = true

        const { results } = await useWrapFetch<BaseResponse<CheckInCustomerData>>('/checkInCustomerData/getByDate', {
            method: 'POST',
            body: {
                checkInDate: date,
            },
        })

        const findDay = results.data.days.find(day => day.day === date)
        const findWeight = cards.value.find(item => item.key === 'weight')
        const findWaist = cards.value.find(item => item.key === 'waistCircumference')
        const findWater = cards.value.find(item => item.key === 'waterIntake')
        const findMeal = cards.value.find(item => item.key === 'meal')
        const findSport = cards.value.find(item => item.key === 'sport')
        const findNutrition = cards.value.find(item => item.key === 'nutrition')

        findNutrition!.value = findDay?.nutrition || 0

        if (findDay?.nutrition === 1) {
            findNutrition!.rightText = '✔'
        }

        if (findDay?.weight) {
            findWeight!.rightText = '✔'
        }

        if (findDay?.waistCircumference) {
            findWaist!.rightText = '✔'
        }

        findWater!.value = findDay?.waterIntake || 0

        findSport!.value = getCeilSportValue(findDay?.sport || 0)

        const filterMealCount = (findDay?.mealCount || []).filter(item => item !== 'snack')
        findMeal!.value = filterMealCount.length

        findWeight!.value = findDay?.weight || 0

        findWaist!.value = findDay?.waistCircumference || 0

        findMeal!.rightText = `${filterMealCount.length}/3`

        if (updateFromeWx) {
            const { wxRunData } = useWxRunData()
            const currentStep = findSport!.value

            const wxStep = Number(wxRunData?.step || 0)
            const wxStepTime = Number(wxRunData?.stepTime || 0)

            if (wxStep > currentStep && dayjs.unix(wxStepTime).isSame(dayjs(), 'day')) {
                await useWrapFetch('/checkInCustomerSport/save/foot', { method: 'post', body: {
                    steps: wxStep,
                    checkInDate: dayjs().format('YYYY-MM-DD'),
                },
                })

                findSport!.value = wxStep
            }
        }

        if (refreshProgress) {
            handleSlideChange({
                activeIndex: activeIndex.value,
            })
        }
    } catch (error) {
        showToast('获取数据失败')
    } finally {
        isCheckinDateFetching.value = false
    }
}

async function getCheckInTarget() {
    const { results } = await useWrapFetch<BaseResponse<CustomerIndex>>('/checkInCustomerIndex/get', {
        method: 'post',
        body: {
            kind: '4',
        },
    })

    if (results) {
        const findWater = cards.value.find(item => item.key === 'waterIntake')
        const findSport = cards.value.find(item => item.key === 'sport')

        findWater!.rightText = results.waterIndex || '1700'
        findSport!.rightText = results.footIndex || '6000'
    } else {
        const findWater = cards.value.find(item => item.key === 'waterIntake')
        const findSport = cards.value.find(item => item.key === 'sport')

        findWater!.rightText = '1700'
        findSport!.rightText = '6000'
    }
}

getCheckInTarget()

whenever(() => checkInDate, async (val) => {
    await getCheckInDataByDate(val, true)
})

onMounted(() => {
    getCheckInDataByDate(checkInDate, true, true)
})

function handleCheckInSuccess() {
    showCheckInExerciseActionSheet.value = false
    showCheckInWaterActionSheet.value = false
    showCheckInWeightActionSheet.value = false
    showCheckInNutritionActionSheet.value = false
    showCheckInMealActionSheet.value = false
    showCheckInWaistActionSheet.value = false
    showSuccessToast('打卡成功')
    getCheckInDataByDate(checkInDate, true)
}

// eslint-disable-next-line vue/no-expose-after-await
defineExpose({
    cards,
    getCheckInDataByDate,
})
</script>

<template>
    <div>
        <swiper
            effect="coverflow"
            :grab-cursor="true"
            :centered-slides="true"
            slides-per-view="auto"
            :initial-slide="2"
            :coverflow-effect="{
                rotate: 0,
                stretch: 50,
                depth: 350,
                modifier: 1,
                slideShadows: false,
            }"
            :modules="modules"
            :touch-events-target="isIOS() ? 'container' : 'wrapper'"
            class="!py-16px"
            @slide-change="handleSlideChange"
            @click="isIOS() ? cards[activeIndex]?.onClick() : null"
        >
            <swiper-slide
                v-for="(slide, index) in cards" :key="index" class="swiper-slide"
                @click="isIOS() ? null : slide.onClick()"
            >
                <component
                    :is="slide.component"
                    :key="index"
                    :active-index
                    :active-key
                    :value="slide.value"
                />
            </swiper-slide>
        </swiper>

        <div class="relative">
            <van-progress
                class="mb-20px w-full"
                :show-pivot="false"
                stroke-width="25"
                track-color="white"
                :color="cards[activeIndex]?.color"
                :percentage
            />
            <div class="absolute top-0 left-0 w-full h-full flex justify-between items-center px-16px">
                <div class="text-t-5 text-13px leading-none">
                    {{ cards[activeIndex]?.leftText(cards[activeIndex]?.value as number) }}
                </div>

                <div v-if="cards[activeIndex]?.rightText" class="text-t-5 text-13px leading-none">
                    {{ cards[activeIndex]?.rightText }}
                </div>
            </div>
        </div>

        <user-checkin-exercise
            v-model="showCheckInExerciseActionSheet"
            :check-in-date
            :sport
            @success="handleCheckInSuccess"
        />

        <user-checkin-water
            v-model="showCheckInWaterActionSheet"
            :check-in-date
            @success="handleCheckInSuccess"
        />

        <user-checkin-weight
            v-model="showCheckInWeightActionSheet"
            :check-in-date
            :weight-and-height
            @success="handleCheckInSuccess"
        />

        <user-checkin-nutrition
            v-model="showCheckInNutritionActionSheet"
            :check-in-date
            @success="handleCheckInSuccess"
        />

        <user-checkin-waist
            v-model="showCheckInWaistActionSheet"
            :check-in-date
            :waist
            @success="handleCheckInSuccess"
        />

        <!-- <user-checkin-meal
            v-model="showCheckInMealActionSheet"
            :check-in-date
            :meals
            @success="handleCheckInSuccess('meal')"
        /> -->
    </div>
</template>

<style scoped>
.swiper {
    width: 100%;
}

.swiper-slide {
    position: relative;
    width: 148px;
    height: 211px;
}

.swiper-slide img {
    display: block;
    width: 100%;
    height: 100%;
    object-fit: none;
    border-radius: inherit;
}
</style>
