<script setup lang="ts">
const { value, activeKey } = defineProps<{
    value: number
    activeKey: string
}>()

const boxShadow = computed(() => {
    return activeKey === 'waistCircumference' ? '0px 0px 10px 0px rgba(0, 0, 0, 0.2)' : 'none'
})
</script>

<template>
    <div class="bg-mask">
        <div class="flex items-center gap-4px">
            <div class="i-custom-checkin-card-top-waist w-28px h-28px"></div>

            <div text="14px t-5" font-600>
                腰围
            </div>
        </div>

        <div class="flex justify-center flex-col items-center">
            <div class="i-custom-checkin-card-center-waist w-78px h-78px"></div>

            <div text="14px t-5" relative bottom-8px font-600>
                {{ value ? `${value} cm` : '未记录' }}
            </div>
        </div>

        <div class="flex justify-center">
            <div class="w-82px flex items-center justify-center h-24px rd-20px text-10px w-82px h-24px bg-#FFA271" border="2px solid t-5">
                记录腰围
            </div>
        </div>
    </div>
</template>

<style scoped>
.bg-mask {
    background: url('@/assets/images/checkin/cards/waist-bg.png') no-repeat center center / 100% 100%;
    backdrop-filter: blur(50px);
    --uno: w-148px h-211px rd-10px px-12px py-12px pb-15px flex flex-col justify-between;
    box-shadow: v-bind(boxShadow);
}
</style>
