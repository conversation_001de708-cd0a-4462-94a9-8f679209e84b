<script setup lang="ts">
const props = defineProps<{
    mealData?: CheckInCustomerMeal
}>()

const emit = defineEmits<{
    (e: 'refresh'): void
}>()

const dayjs = useDayjs()

const mealCount = computed(() => {
    return {
        kindList: props.mealData?.kindList || [],
        updateTime: props.mealData?.updateTime ? dayjs(props.mealData.updateTime).format('HH:mm') : '',
    }
})

const menuList = [
    {
        name: '早餐',
        key: 'breakfast',
        path: '/user/checkin/food?tab=breakfast',
        icon: 'i-custom:checkin-meals-breakfast',
        fillIcon: 'i-custom:checkin-meals-breakfast-fill',
    },
    {
        name: '午餐',
        key: 'lunch',
        path: '/user/checkin/food?tab=lunch',
        icon: 'i-custom:checkin-meals-lunch',
        fillIcon: 'i-custom:checkin-meals-lunch-fill',
    },
    {
        name: '晚餐',
        key: 'dinner',
        path: '/user/checkin/food?tab=dinner',
        icon: 'i-custom:checkin-meals-dinner',
        fillIcon: 'i-custom:checkin-meals-dinner-fill',
    },
    {
        name: '加餐',
        key: 'snack',
        path: '/user/checkin/food?tab=snack',
        icon: 'i-custom:checkin-meals-snack',
        fillIcon: 'i-custom:checkin-meals-snack-fill',
    },
]

function handleClickMenu(menu: typeof menuList[number]) {
    if (menu.path) {
        navigateTo(menu.path)
    } else {
        showToast('功能开发中')
    }
}

async function handlePictureFoodAdd(food: FoodItem[], dietType: DietType, mealPicture: string) {
    await saveDietRecord(food, dietType, mealPicture)
    showSuccessToast('保存成功')
    emit('refresh')
}
</script>

<template>
    <div class="w-full">
        <div class="relative h-132px">
            <div class="food-bg absolute">
                <div class="text-t-5 font-600 text-15px pl-16px pt-12px">
                    饮食记录
                </div>

                <div v-if="mealCount.updateTime" class="text-t-3 text-11px absolute left-16px top-35px">
                    {{ mealCount.updateTime }} 更新
                </div>

                <div class="absolute left-0 right-0 z-1 top-58px">
                    <div class="flex justify-between px-16px">
                        <div
                            v-for="(menu, index) in menuList"
                            :key="index"
                            class="flex flex-col items-center gap-2px"
                        >
                            <div
                                :class="[mealCount.kindList.includes(menu.key as DietType) ? menu.fillIcon : menu.icon]"
                                class="w-48px h-48px"
                                @click="handleClickMenu(menu)"
                            ></div>

                            <div class="text-t-5 text-12px">
                                {{ menu.name }}
                            </div>
                        </div>

                        <user-checkin-food-take-photo @add="handlePictureFoodAdd">
                            <div class="flex flex-col items-center gap-2px">
                                <div class="i-custom:checkin-meals-photo w-48px h-48px"></div>

                                <div class="text-t-5 text-12px">
                                    拍照
                                </div>
                            </div>
                        </user-checkin-food-take-photo>
                    </div>
                </div>
            </div>

            <div class="absolute w-182px h-38px bg-warning-1 rd-10px top-0 right-0 pt-6px px-12px flex justify-between" @click="navigateTo('/user/recommend/diet')">
                <div class="h-25px flex items-center justify-between w-full text-warning-6">
                    <div class="flex items-center gap-8px ">
                        <div class="i-custom:checkin-food-summary w-15px h-15px"></div>
                        <div class=" font-800 text-14px">
                            饮食推荐
                        </div>
                    </div>

                    <div class="i-radix-icons:chevron-right w-15px h-15px font-600"></div>
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.food-bg {
    background: url('@/assets/images/checkin/food-bg.svg') no-repeat;
    background-size: 100% 100%;  // 添加这一行
    width: 100%;  // 修改固定宽度为100%
    height: 132px;  // 保持原有高度
}
</style>
