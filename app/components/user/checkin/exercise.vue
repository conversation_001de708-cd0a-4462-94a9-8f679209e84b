<script setup lang="ts">
import dayjs from 'dayjs'

const {
    checkInDate = dayjs().format('YYYY-MM-DD'),
    sport = 0,
} = defineProps<{
    checkInDate?: string
    sport?: number
}>()

const emit = defineEmits<{
    (e: 'success'): void
}>()

const showAddActionSheet = defineModel({
    default: false,
})

const sliderRuleRef = useTemplateRef('sliderRuleRef')
const sportValue = ref()

const { wxRunData } = storeToRefs(useWxRunData())

const isDataFromWx = ref(false)

watch([wxRunData, () => sport], ([wxRun, sportVal]) => {
    const value1 = Number(wxRun.step || 0)
    const value2 = Number(sportVal || 0)
    const maxValue = Math.max(value1, value2)

    sportValue.value = getCeilSportValue(maxValue)

    isDataFromWx.value = value1 > value2
}, { immediate: true })

whenever(showAddActionSheet, async () => {
    await nextTick()
    sliderRuleRef.value?.renderRule()
})

async function handleSave() {
    try {
        await useWrapFetch('/api/checkInCustomerSport/save/foot', { method: 'post', body: {
            steps: sportValue.value,
            checkInDate,
        } })
        emit('success')
    } catch {
        showFailToast('保存失败')
    }
}
</script>

<template>
    <van-action-sheet class="linear-gradient-content" :show="showAddActionSheet" @close="showAddActionSheet = false">
        <div>
            <div class="flex justify-between items-center p-16px">
                <div class="w-14px"></div>
                <div class="text-center font-600 text-16px text-t-5">运动打卡</div>
                <div class="text-right text-16px text-t-5 i-radix-icons:cross-2 w-16px h-16px" @click="showAddActionSheet = false"></div>
            </div>

            <user-checkin-slider-rule
                ref="sliderRuleRef"
                v-model="sportValue"
                dialog-title="设置步数"
                :unit="isDataFromWx ? '步' : '步'"
                :rule-props="{
                    maxValue: 20000,
                    minValue: 0,
                    precision: 50,
                    divide: 5,
                }"
                @save="handleSave"
            />
        </div>
    </van-action-sheet>
</template>
