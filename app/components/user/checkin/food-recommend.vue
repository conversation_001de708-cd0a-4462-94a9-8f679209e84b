<script setup lang="ts">
import { ref } from 'vue'
import { Swiper, SwiperSlide } from 'swiper/vue'
import { EffectCards } from 'swiper/modules'

import 'swiper/css'
import 'swiper/css/effect-cards'
import mock from './mock.json'

const { title, activeTab } = defineProps<{
    title: string
    activeTab: DietType
}>()

const emit = defineEmits<{
    (e: 'add', food: RecipeGroup): void
}>()

const modules = [EffectCards]

const dayjs = useDayjs()
const date = ref(dayjs().format('YYYY-MM-DD'))

const recommendMeal = ref<RecommendMeal>({
    breakfast: [],
    lunch: [],
    dinner: [],
    snack: [],
})

const isMealCheckin = ref<Record<DietType, boolean>>({
    breakfast: false,
    lunch: false,
    dinner: false,
    snack: false,
})

const activeMeals = computed(() => {
    switch (activeTab) {
        case 'breakfast':
            return recommendMeal.value?.breakfast
        case 'lunch':
            return recommendMeal.value?.lunch
        case 'dinner':
            return recommendMeal.value?.dinner
        case 'snack':
            return recommendMeal.value?.snack
        default:
            return []
    }
})

const isLoading = ref(false)
async function getRecommendMeal() {
    isLoading.value = true

    try {
        recommendMeal.value = {
            breakfast: [],
            lunch: [],
            dinner: [],
            snack: [],
        }

        isMealCheckin.value = {
            breakfast: false,
            lunch: false,
            dinner: false,
            snack: false,
        }

        const { results: list } = await useWrapFetch<BaseResponse<any[]>>('/checkInCustomerMeal/list', {
            method: 'POST',
            body: {
                checkInDate: date.value,
            },
        })

        if (list?.length > 0) {
            list.forEach((item) => {
                const { kind, mealContent } = item

                const parsedMealContent = JSON.parse(mealContent)

                if (kind === 'breakfast' && parsedMealContent?.item > 0) {
                    recommendMeal.value.breakfast = JSON.parse(mealContent)
                    isMealCheckin.value.breakfast = true
                } else if (kind === 'lunch' && parsedMealContent?.item > 0) {
                    recommendMeal.value.lunch = JSON.parse(mealContent)
                    isMealCheckin.value.lunch = true
                } else if (kind === 'dinner' && parsedMealContent?.item > 0) {
                    recommendMeal.value.dinner = JSON.parse(mealContent)
                    isMealCheckin.value.dinner = true
                } else if (kind === 'snack' && parsedMealContent?.item > 0) {
                    recommendMeal.value.snack = JSON.parse(mealContent)
                    isMealCheckin.value.snack = true
                }
            })
        }

        if (dayjs(date.value).isSameOrAfter(dayjs(), 'day')) {
            let trueResults: DietPlan
            const { results } = await useWrapFetch<BaseResponse<DietPlan>>('/checkInCustomerMeal/recommend', { method: 'POST' })

            if (!results) {
                trueResults = mock as DietPlan
            } else {
                trueResults = results
            }

            if (recommendMeal.value.breakfast?.length === 0) {
                recommendMeal.value.breakfast = trueResults.breakfast_values
            }

            if (recommendMeal.value.lunch?.length === 0) {
                recommendMeal.value.lunch = trueResults.lunch_values
            }

            if (recommendMeal.value.dinner?.length === 0) {
                recommendMeal.value.dinner = trueResults.dinner_values
            }

            if (recommendMeal.value.snack?.length === 0) {
                recommendMeal.value.snack = trueResults.snack_values
            }
        }
    } catch (error) {
        console.log(error)
    } finally {
        isLoading.value = false
    }
}

whenever(date, () => {
    getRecommendMeal()
})

const swiperInstance = ref<any>()

watch(() => activeTab, () => {
    swiperInstance.value?.slideTo(0)
})

function onSwiper(swiper: any) {
    swiperInstance.value = swiper
}

onMounted(() => {
    getRecommendMeal()
})

const isFoodTipsOverlayShow = ref(false)

function calculateCalories(weight: string, caloriesPer100g: string) {
    const _weight = extractNumber(weight)
    const _caloriesPer100g = extractNumber(caloriesPer100g)

    return (Math.round(_weight * (_caloriesPer100g / 100))).toFixed(0)
}

function handleAdd() {
    const activeMeal = activeMeals.value[swiperInstance.value.activeIndex]
    activeMeal?.recipe_details_value.forEach((item) => {
        item.realCalorie = calculateCalories(item?.熟重 ?? '', item?.每百克卡路里 ?? '')
    })
    emit('add', activeMeal!)
}
</script>

<template>
    <div>
        <div class="p-16px overflow-hidden">
            <div v-if="isLoading" class="h-266px flex justify-center items-center">
                <shared-unified-loading
                    size="medium"
                    :rainbow="false"
                    text="正在为您个性化定制..."
                    vertical
                />
            </div>

            <template v-else>
                <template v-if="activeMeals?.length > 0">
                    <div class="text-t-5 text-24px font-500 mb-8px">
                        {{ title }}
                    </div>
                    <swiper
                        effect="cards"
                        :modules="modules"
                        class="mySwiper"
                        :cards-effect="{
                            perSlideOffset: 8,
                            perSlideRotate: 0,
                            slideShadows: false,
                        }"
                        @swiper="onSwiper"
                    >
                        <swiper-slide v-for="(item, index) in activeMeals" :key="item.type + index">
                            <div
                                style="background: linear-gradient(0deg, #FFFFFF 68.74%, #FFF5EF 100%);"
                                class="h-full w-full p-16px flex flex-col justify-between"
                            >
                                <div>
                                    <div
                                        style="border-radius: 10px 10px 0 0;"
                                        class="bg-#FDEBD5 px-16px py-8px"
                                    >
                                        <div class="flex items-center justify-between">
                                            <div class="flex items-center gap-4px">
                                                <div class="text-13px text-#8C3E12" font-600>
                                                    方案{{ ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'][index] }}
                                                </div>

                                                <div class="flex items-center gap-2px">
                                                    <div class="i-custom-tips w-11px h-11px">
                                                    </div>

                                                    <div class="text-#F98804 text-10px">
                                                        主食使用代餐，效果更加
                                                    </div>
                                                </div>
                                            </div>

                                            <div class="i-radix-icons:question-mark-circled w-16px h-16px text-t-4" @click="isFoodTipsOverlayShow = true">
                                            </div>
                                        </div>
                                    </div>

                                    <div class="px-16px overflow-y-auto py-16px flex flex-col gap-12px bg-#F2F4F7">
                                        <div v-for="(food, foodIndex) in item.recipe_details_value" :key="food.食物名 + foodIndex">
                                            <div class="flex justify-between">
                                                <div class="text-t-4 text-13px">
                                                    {{ food.食物名 }} <span v-if="food?.熟重" class="text-12px">({{ food?.熟重 }})</span>
                                                </div>

                                                <div class="text-t-4 text-13px">
                                                    {{ calculateCalories(food?.熟重 || '', food?.每百克卡路里 || '') }}千卡
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- <div
                                class="flex justify-center items-center bg-#FFF7E8 px-16px py-8px"
                                style="border-radius: 0 0 10px 10px;"
                            >
                                <div
                                    class="text-t-4 text-13px"
                                >
                                    全部食谱
                                </div>
                                <div class="i-radix-icons:chevron-right   text-12px text-t-4"></div>
                            </div> -->
                                </div>
                            </div>
                        </swiper-slide>
                    </swiper>

                    <div flex justify-center flex-col items-center>
                        <van-button type="primary" round class="!w-240px !h-50px !mt-16px text-15px" @click="handleAdd">
                            打卡当前方案
                        </van-button>
                    </div>
                </template>

                <van-empty v-else description="暂无已打卡食谱" />
            </template>
        </div>

        <user-checkin-cards-food-tips-overlay v-model="isFoodTipsOverlayShow" />
    </div>
</template>

<style scoped>
.mySwiper {
    height: 180px;
}

.swiper-slide {
    border-radius: 8px;
    background: #fff;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.05);
    transform: translateY(0);
    transition: transform 0.3s;
}

.swiper-slide-active {
    transform: translateY(-4px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
}
</style>
