<script setup lang="ts">
const { checkInDate } = defineProps<{
    checkInDate: string
}>()

const emit = defineEmits<{
    (e: 'success'): void
}>()

const showAddActionSheet = defineModel({
    default: false,
})

async function handleCheckin() {
    await useWrapFetch('/checkInCustomerNutrition/save', {
        method: 'post',
        body: {
            checkInDate,
        },
    })

    emit('success')
}
</script>

<template>
    <van-action-sheet class="linear-gradient-content" :show="showAddActionSheet" @close="showAddActionSheet = false">
        <div>
            <div class="flex justify-between items-center p-16px">
                <div class="w-14px"></div>
                <div class="text-center font-600 text-16px text-t-5">营养打卡</div>
                <div class="text-right text-16px text-t-5 i-radix-icons:cross-2 w-16px h-16px" @click="showAddActionSheet = false"></div>
            </div>

            <div class="p-16px">
                <img src="@/assets/images/checkin/nutrition.svg" alt="nutrition" class="w-full" />
            </div>

            <div class="flex justify-center my-40px">
                <van-button type="primary" round class="!w-160px" @click="handleCheckin">
                    立即打卡
                </van-button>
            </div>
        </div>
    </van-action-sheet>
</template>
