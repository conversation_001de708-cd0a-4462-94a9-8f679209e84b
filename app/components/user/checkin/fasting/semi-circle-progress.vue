<script setup lang="ts">
const props = defineProps<{
    size?: number
    strokeWidth?: number
    color?: string
    backgroundColor?: string
    startAt?: string
    endAt?: string
    type: 0 | 1
}>()

const { remainingTime, progress: progressValue, setTime, isReachEndTime } = useTimer()

function setTimeHandler() {
    setTime(props.startAt || '0', props.endAt || '')
}

setTimeHandler()

defineExpose({
    setTimeHandler,
    isReachEndTime,
})

const defaultProps = {
    size: 155,
    strokeWidth: 12,
    color: '#00AC97',
    backgroundColor: '#E4FAF9',
}

// Computed values for SVG path
const radius = computed(() => (props.size ?? defaultProps.size) / 2)
const normalizedRadius = computed(() => radius.value - (props.strokeWidth ?? defaultProps.strokeWidth) / 2)
const circumference = computed(() => normalizedRadius.value * Math.PI)

// Calculate progress path
const strokeDashoffset = computed(() => {
    const progress = Math.min(Math.max((progressValue as Ref<number>).value, 0), 100)
    return circumference.value * (1 - progress / 100)
})

// SVG viewBox and path coordinates
const center = computed(() => props.size ?? defaultProps.size)

// Calculate the path
const arcPath = computed(() => {
    const startX = (props.strokeWidth ?? defaultProps.strokeWidth) / 2
    const endX = center.value - (props.strokeWidth ?? defaultProps.strokeWidth) / 2
    const y = center.value / 2
    return `M ${startX},${y} A ${normalizedRadius.value},${normalizedRadius.value} 0 0 1 ${endX},${y}`
})

// 添加新的计算属性，用于内部填充半圆
const innerRadius = computed(() => normalizedRadius.value - (props.strokeWidth ?? defaultProps.strokeWidth) * 1)
const innerArcPath = computed(() => {
    const startX = (props.size ?? defaultProps.size) / 2 - innerRadius.value
    const endX = (props.size ?? defaultProps.size) / 2 + innerRadius.value
    const y = center.value / 2
    return `M ${startX},${y} A ${innerRadius.value},${innerRadius.value} 0 0 1 ${endX},${y} L ${center.value / 2},${center.value / 2} Z`
})
</script>

<template>
    <div class="semi-circle-progress" @click="navigateTo('/user/checkin/fasting')">
        <svg
            width="100%"
            height="100%"
            :viewBox="`0 0 ${center} ${center / 2}`"
            preserveAspectRatio="xMidYMid meet"
        >
            <!-- 添加填充的半圆（使用较小的半径） -->
            <path
                :d="innerArcPath"
                :fill="props.backgroundColor ?? defaultProps.backgroundColor"
            />

            <!-- Background arc -->
            <path
                :d="arcPath"
                stroke="#F2F4F7"
                fill="none"
                stroke-linecap="round"
                :style="{
                    strokeWidth: '12PX',
                }"
            />

            <!-- Progress arc -->
            <path
                :d="arcPath"
                :stroke="props.color ?? defaultProps.color"
                fill="none"
                stroke-linecap="round"
                :style="{
                    strokeWidth: '12PX',
                    strokeDasharray: circumference,
                    strokeDashoffset,
                    transformOrigin: 'center',
                    transition: 'stroke-dashoffset 0.3s ease',
                }"
            />
        </svg>

        <div class="absolute bottom-0 left-0 flex flex-col w-full justify-center items-center">
            <div class="text-13px text-t-4">
                剩余时间
            </div>

            <div class=" text-t-5 font-ddinpro font-800 text-20px">
                {{ remainingTime.hour }}:{{ remainingTime.minute }}:{{ remainingTime.second }}
            </div>
        </div>
    </div>
</template>

<style scoped>
.semi-circle-progress {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  width: 155px;
  height: 77px;
}

.semi-circle-scale {
  background: url('@/assets/images/checkin/semi-circle-scale.svg') no-repeat center center;
  width: 135px;
  height: 67px;
  position: absolute;
  top: 20px;
  left: 16px;
}
</style>
