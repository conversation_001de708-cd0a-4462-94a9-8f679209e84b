<script setup lang="ts">
const dayjs = useDayjs()
const {
    fastingState,
    getLatestLightFastingRecord,
    fastingText,
    handleChangeFastingStatus,
    inverseFastingText,
} = useFasting()

const { remainingTime, progress: progressValue, setTime, isReachEndTime } = useTimer()

watch(fastingState, (val) => {
    if (val.isChallenge) {
        setTime(val.startAt, val.endAt)
    }
}, { immediate: true, deep: true })

// const semiCircleProgressRef = useTemplateRef('semiCircleProgressRef')

const refreshFastingCard = inject<Ref<boolean>>('refreshFastingCard')!

async function endChallenge() {
    showConfirmDialog({
        title: '结束轻断食挑战',
        message: '轻断食挑战进行中，确定结束轻断食吗？',
    })
        .then(async () => {
            await useWrapFetch('/light-fasting-record/endTheChallenge')
            getLatestLightFastingRecord()
            showToast('结束挑战成功')
            refreshFastingCard.value = true
        })
        .catch(() => {
            // on cancel
        })
}

async function handleEnd() {
    await handleChangeFastingStatus()

    // semiCircleProgressRef.value?.setTimeHandler()
    refreshFastingCard.value = true
}

const modifyStartTimeRef = useTemplateRef('modifyStartTimeRef')

async function handleSuccess() {
    await getLatestLightFastingRecord()
    // semiCircleProgressRef.value?.setTimeHandler()
    refreshFastingCard.value = true
}
</script>

<template>
    <div class="bg-#fff rd-10px items-center justify-between px-16px py-12px">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-4px" @click="navigateTo('/user/checkin/fasting/tips')">
                <div class="text-15px font-600 text-t-5">
                    <span class="text-15px text-t-5">轻断食</span> <span class="text-14px text-t-4">（16-8）</span>
                </div>

                <div class="i-custom:information w-12px h-12px"></div>
            </div>

            <div class="flex items-center gap-x-8px">
                <img v-if="fastingState.isChallenge === false" src="@/assets/images/checkin/fasting-start.svg" alt="" srcset="" @click="navigateTo('/user/checkin/fasting/start')" />

                <img v-else-if="fastingState.isChallenge === true" src="@/assets/images/checkin/fasting-end.svg" alt="" srcset="" @click="endChallenge" />

                <van-button v-if="fastingState.isChallenge" class="!text-white !bg-primary-6 !border-none !rounded-full !h-28px" @click="handleEnd">
                    {{ isReachEndTime ? '' : '提前' }}开始{{ inverseFastingText }}
                </van-button>
            </div>
        </div>

        <div v-if="fastingState.isChallenge">
            <div class="flex justify-between mt-8px">
                <div class="flex items-center justify-between font-600" :class="fastingState.type === 0 ? 'text-primary-6' : 'text-warning-6'">
                    你正在{{ fastingText }}中
                </div>

                <div class="flex items-center gap-4px">
                    <div class="text-13px text-t-4">
                        剩余时间
                    </div>

                    <div class="text-t-5 font-800 text-20px font-ddinpro">
                        {{ remainingTime.hour }}:{{ remainingTime.minute }}:{{ remainingTime.second }}
                    </div>
                </div>
            </div>

            <van-progress
                :color="fastingState.type === 0 ? 'linear-gradient(90deg, #00AC97 0%, #52D2C2 100%)' : 'linear-gradient(90deg, #FF951A 0%, #FFBD71 100%)'"
                stroke-width="10px"
                :show-pivot="false"
                :percentage="progressValue"
            />

            <div class="rd-10px bg-#F4F5F7 p-10px mt-10px justify-between flex">
                <div class="flex items-center gap-8px">
                    <div class="text-#868F9C text-12px">
                        {{ fastingText }}开始时间
                    </div>

                    <div class="flex items-center" @click="modifyStartTimeRef?.handleChangeStartTime">
                        <div class="text-t-4 text-15px font-600">
                            <template v-if="dayjs(fastingState.startAt).isSame(dayjs(), 'day')">
                                {{ dayjs(fastingState.startAt).format('HH:mm') }}
                            </template>
                            <template v-else>
                                {{ dayjs(fastingState.startAt).format('HH:mm') }} <sup>-1</sup>
                            </template>
                        </div>
                        <div class="i-custom:pen relative bottom-2px left-3px w-12px h-12px"></div>
                    </div>
                </div>

                <div class="flex items-center gap-8px">
                    <div class="text-#868F9C text-12px">
                        {{ fastingText }}结束时间
                    </div>

                    <div class="text-t-4 text-16px font-600">
                        <template v-if="dayjs(fastingState.endAt).isSame(dayjs(), 'day')">
                            {{ dayjs(fastingState.endAt).format('HH:mm') }}
                        </template>
                        <template v-else>
                            {{ dayjs(fastingState.endAt).format('HH:mm') }} <sup>+1</sup>
                        </template>
                    </div>
                </div>
            </div>

            <!-- <div flex items-end gap-x-16px justify-between mt-8px>
                <div class="w-60px flex flex-col justify-between">
                    <div class="text-t-3 text-12px">
                        开始时间
                    </div>

                    <div class="flex items-end" @click="modifyStartTimeRef?.handleChangeStartTime">
                        <div class="text-t-4 text-16px font-800 font-ddinpro">
                            <template v-if="dayjs(fastingState.startAt).isSame(dayjs(), 'day')">
                                {{ dayjs(fastingState.startAt).format('HH:mm') }}
                            </template>
                            <template v-else>
                                {{ dayjs(fastingState.startAt).format('HH:mm') }} <sup>-1</sup>
                            </template>
                        </div>
                        <div class="i-custom:pen relative bottom-5px left-3px w-12px h-12px"></div>
                    </div>
                </div>

                <user-checkin-fasting-semi-circle-progress
                    ref="semiCircleProgressRef"
                    :start-at="fastingState.startAt"
                    :end-at="fastingState.endAt"
                    :type="fastingState.type"
                    :color="fastingState.type === 0 ? '#00AC97' : '#F98804'"
                    :background-color="fastingState.type === 0 ? '#E4FAF9' : '#FFF7E8'"
                />
                <div class="w-60px flex flex-col justify-between items-center">
                    <div class="text-t-3 text-12px">
                        结束时间
                    </div>

                    <div class="text-t-4 text-16px font-800 font-ddinpro">
                        <template v-if="dayjs(fastingState.endAt).isSame(dayjs(), 'day')">
                            {{ dayjs(fastingState.endAt).format('HH:mm') }}
                        </template>
                        <template v-else>
                            {{ dayjs(fastingState.endAt).format('HH:mm') }} <sup>+1</sup>
                        </template>
                    </div>
                </div>
            </div> -->
        </div>

        <user-checkin-fasting-modify-start-time
            ref="modifyStartTimeRef"
            :fasting-text="fastingText"
            @success="handleSuccess"
        />
    </div>
</template>

<style scoped>
path {
    transition: stroke-dashoffset 0.5s ease;
}

:deep(.custom-time-picker .van-picker__toolbar) {
    height: auto;
}
</style>

<style>
.van-dialog__header {
    font-size: 18px !important;
}

.van-dialog__message {
    font-size: 16px !important;
}
</style>
