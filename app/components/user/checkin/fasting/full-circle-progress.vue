<script setup lang="ts">
const dayjs = useDayjs()

const { fastingState, fastingText, inverseFastingText, handleChangeFastingStatus, getLatestLightFastingRecord } = useFasting()

const switchColor = computed(() => fastingState.value.type === 0 ? '#00AC97' : '#F98804')

defineExpose({
    fastingState,
})

const { passedTime, progress: progressValue, setTime, remainingTime, isReachEndTime } = useTimer()

const circumference = computed(() => 2 * Math.PI * 44)

const strokeDashoffset = computed(() => {
    return circumference.value * (1 - progressValue.value / 100)
})

const router = useRouter()

watch(fastingState, (val) => {
    if (val.isChallenge === true) {
        setTime(val.startAt, val.endAt)
    } else if (val.isChallenge === false) {
        router.replace('/user/checkin/fasting/start')
    }
}, { immediate: true, deep: true })

function formatTime(time: string) {
    const _hour = dayjs(time).hour().toString().padStart(2, '0')
    const _minute = dayjs(time).minute().toString().padStart(2, '0')
    const isTomorrow = dayjs(time).isSame(dayjs().add(1, 'day'), 'second')
    const isYesterday = dayjs(time).isSame(dayjs().subtract(1, 'day'), 'second')
    const isOver2Days = dayjs(time).isBefore(dayjs().subtract(2, 'day'), 'second')

    if (isOver2Days) {
        return `${dayjs(time).format('MM-DD')} ${_hour}:${_minute}`
    }
    if (isTomorrow) {
        return `明天 ${_hour}:${_minute}`
    }
    if (isYesterday) {
        return `昨天 ${_hour}:${_minute}`
    }

    return `${_hour}:${_minute}`
}

async function handleEnd() {
    await handleChangeFastingStatus()
}

const modifyStartTimeRef = useTemplateRef('modifyStartTimeRef')

async function handleSuccess() {
    await getLatestLightFastingRecord()
}

const isAutoCheckin = ref(0)

const { data: autoCheckin, refresh } = useAPI<number>('/light-fasting-record/getPattern')

watch(autoCheckin, (val) => {
    isAutoCheckin.value = val?.results || 0
})

async function handleSwitchChange(val: number) {
    try {
        const { results } = await useWrapFetch<BaseResponse<boolean>>('/light-fasting-record/setUpPattern', {
            params: {
                type: val,
            },
        })
        if (results) {
            showSuccessToast('设置成功')
            await refresh()
        }
    } catch (error) {
        console.log(error)
    }
}

async function endChallenge() {
    showConfirmDialog({
        title: '结束轻断食挑战',
        message: '轻断食挑战进行中，确定结束轻断食吗？',
    })
        .then(async () => {
            await useWrapFetch('/light-fasting-record/endTheChallenge')
            showToast('结束挑战成功')
            router.replace('/user/checkin')
        })
        .catch(() => {
            // on cancel
        })
}
</script>

<template>
    <div class="flex items-center flex-col">
        <div class="w-full flex items-center justify-between">
            <div class="text-t-5 text-15px font-600">
                轻断食 16-8 模式
            </div>

            <van-button round class="bg-#E4FAF9! text-#00AC97! border-none! h-33px! !w-90px" @click="navigateTo('/user/checkin/fasting/records')">
                断食记录
            </van-button>
        </div>

        <div
            class="bg-#F4F5F7 mt-16px w-full py-8px px-16px rd-10px flex items-center justify-around"
            @click="() => {
                modifyStartTimeRef?.handleChangeStartTime()
            }"
        >
            <div class="flex flex-col items-center">
                <div class="text-t-3 text-13px">
                    {{ fastingText }}开始时间
                </div>

                <div class="flex items-center gap-x-4px">
                    <div class="text-15px text-t-4 font-600">
                        {{ formatTime(fastingState.startAt) }}
                    </div>
                    <div class="i-custom:pen w-11px h-11px"></div>
                </div>
            </div>

            <div class="w-1px h-28px bg-fill-3"></div>

            <div class="flex flex-col items-center">
                <div class="text-t-3 text-13px">
                    {{ fastingText }}结束时间
                </div>

                <div class="text-15px text-t-4 font-600">
                    {{ formatTime(fastingState.endAt) }}
                </div>
            </div>
        </div>

        <div class="w-200px h-200px relative mt-16px">
            <svg
                width="100%"
                height="100%"
                viewBox="0 0 100 100"
            >
                <!-- 渐变定义 -->
                <defs>
                    <linearGradient id="gradient-primary" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#52D2C2" />
                        <stop offset="100%" style="stop-color:#00AC97" />
                    </linearGradient>
                    <linearGradient id="gradient-warning" x1="0%" y1="0%" x2="100%" y2="0%">
                        <stop offset="0%" style="stop-color:#FFBD71" />
                        <stop offset="100%" style="stop-color:#F98804" />
                    </linearGradient>
                </defs>
                <circle cx="50" cy="50" r="44" fill="none" stroke="#F2F4F7" style="stroke-width: 5px;" />

                <circle cx="50" cy="50" r="38" :fill="fastingState.type === 0 ? '#E4FAF9' : '#FFF7E8'" />
                <circle
                    cx="50"
                    cy="50"
                    r="44"
                    fill="none"
                    :stroke="fastingState.type === 0 ? 'url(#gradient-primary)' : 'url(#gradient-warning)'"
                    stroke-linecap="round"
                    :style="{
                        strokeWidth: '5px',
                        strokeDasharray: circumference,
                        strokeDashoffset,
                        transform: 'rotate(-90deg)',
                        transformOrigin: 'center',
                        transition: 'stroke-dashoffset 0.3s ease',
                    }"
                />
            </svg>

            <div class="absolute top-0 left-0 w-full h-full flex flex-col items-center justify-center">
                <div class="font-600 text-15px leading-none" :class="fastingState.type === 0 ? 'text-primary-6' : 'text-warning-6'">
                    {{ fastingText }}期
                </div>

                <div class="font-600 font-ddinpro text-32px leading-none my-2px text-t-5">
                    {{ passedTime.hour }}:{{ passedTime.minute }}:{{ passedTime.second }}
                </div>

                <div v-if="!isReachEndTime" class="text-t-4 text-13px">
                    距离{{ inverseFastingText }} {{ remainingTime.hour }}:{{ remainingTime.minute }}:{{ remainingTime.second }}
                </div>

                <div v-else class="text-t-4 text-13px">
                    已超时{{ remainingTime.hour }}:{{ remainingTime.minute }}:{{ remainingTime.second }}
                </div>

                <div class="text-t-3 text-12px mt-4px">
                    自动打卡
                </div>
                <van-switch
                    :model-value="isAutoCheckin"
                    size="18px"
                    :active-value="1"
                    :inactive-value="0"
                    @change="handleSwitchChange"
                />
            </div>
        </div>

        <van-button class="!w-233px !h-50px !mt-16px !text-16px" round type="primary" @click="handleEnd">
            {{ isReachEndTime ? '' : '提前' }}开始{{ inverseFastingText }}
        </van-button>

        <div class="text-gray-500 text-14px mt-24px" @click="endChallenge">
            结束挑战
        </div>

        <user-checkin-fasting-modify-start-time
            ref="modifyStartTimeRef"
            :fasting-text="fastingText"
            @success="handleSuccess"
        />
    </div>
</template>

<style scoped>
:deep(.van-switch--on) {
    background-color: v-bind(switchColor);
}
</style>
