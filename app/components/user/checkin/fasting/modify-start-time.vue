<script setup lang="ts">
const { fastingText } = defineProps<{
    fastingText: string
}>()

const emit = defineEmits<{
    (e: 'success'): void
}>()

const isStartTimePickerShow = ref(false)
const timePickerStartTime = ref<string[]>(['9', '00'])

const dayjs = useDayjs()

function handleChangeStartTime() {
    isStartTimePickerShow.value = true
    timePickerStartTime.value = [dayjs().hour().toString(), dayjs().minute().toString()]
}

defineExpose({
    handleChangeStartTime,
})

const isWarningShow = ref(false)
const warningTime = ref('')

async function handleConfirmStartTimePicker() {
    try {
        const { state, msg } = await useWrapFetch<BaseResponse<any>>('/light-fasting-record/updateLightFastingRecordStartTime', {
            params: {
                startTime: dayjs().format(`YYYY-MM-DD ${timePickerStartTime.value[0]}:${timePickerStartTime.value[1]}:00`),
            },
        })

        if (state === 302) {
            isWarningShow.value = true
            warningTime.value = dayjs(msg).format('HH:mm')
        } else {
            isStartTimePickerShow.value = false
            emit('success')
        }
    } catch (error) {
        console.error(error)
    }
}
</script>

<template>
    <van-popup v-model:show="isStartTimePickerShow" position="bottom">
        <van-time-picker
            v-model="timePickerStartTime"
            :max-time="dayjs().format('HH:mm:ss')"
            class="custom-time-picker"
            @cancel="isStartTimePickerShow = false"
        >
            <template #toolbar>
                <div w-full h-full py-10px>
                    <div flex items-center px-16px justify-between w-full>
                        <div class="text-15px text-t-4" @click="isStartTimePickerShow = false">
                            取消
                        </div>
                        <div class="text-16px font-600">
                            {{ `设置${fastingText}期开始时间` }}
                        </div>
                        <div class="text-15px text-primary-6" @click="handleConfirmStartTimePicker">
                            确认
                        </div>
                    </div>

                    <div v-if="isWarningShow" class="px-16px">
                        <div class="px-16px mt-10px bg-warning-1 flex items-center gap-x-8px py-8px px-16px rd-10px">
                            <div class="i-custom:tips w-16px h-16px"></div>
                            <div class="text-warning-6 ">
                                当前已有记录，请设置{{ warningTime }}后开始{{ fastingText }}！
                            </div>
                        </div>
                    </div>
                </div>
            </template>
        </van-time-picker>
    </van-popup>
</template>
