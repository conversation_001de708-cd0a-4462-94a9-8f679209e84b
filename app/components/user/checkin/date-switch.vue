<script setup lang="ts">
type SwitchType = 'day' | 'week' | 'month'

const { switchType = 'week', featureDisabled = true } = defineProps<{
    switchType?: SwitchType
    featureDisabled?: boolean
}>()

const dayjs = useDayjs()

const currentDate = defineModel<string>('date', {
    required: true,
})

defineExpose({
    currentDate,
})

const isCurrentYear = computed(() => {
    return dayjs(currentDate.value).isSame(dayjs(), 'year')
})

const formatWeek = computed(() => {
    const startOfWeek = isCurrentYear.value ? dayjs(currentDate.value).startOf('isoWeek').format('M月D日') : dayjs(currentDate.value).startOf('isoWeek').format('YYYY年M月D日')
    const endOfWeek = dayjs(currentDate.value).endOf('isoWeek').format('M月D日')

    return [
        startOfWeek,
        endOfWeek,
    ]
})

function handlePrev() {
    if (switchType === 'day') {
        currentDate.value = dayjs(currentDate.value).subtract(1, 'day').format('YYYY-MM-DD')
    }
    else if (switchType === 'week') {
        currentDate.value = dayjs(currentDate.value).subtract(1, 'week').format('YYYY-MM-DD')
    }
    else if (switchType === 'month') {
        currentDate.value = dayjs(currentDate.value).subtract(1, 'month').format('YYYY-MM-DD')
    }
}

function handleNext() {
    if (switchType === 'day') {
        currentDate.value = dayjs(currentDate.value).add(1, 'day').format('YYYY-MM-DD')
    }
    else if (switchType === 'week') {
        currentDate.value = dayjs(currentDate.value).add(1, 'week').format('YYYY-MM-DD')
    }
    else if (switchType === 'month') {
        currentDate.value = dayjs(currentDate.value).add(1, 'month').format('YYYY-MM-DD')
    }
}

const isNextDisabled = computed(() => {
    if (featureDisabled === false) {
        return false
    }

    const today = dayjs()
    if (switchType === 'day') {
        return dayjs(currentDate.value).isSame(today, 'day') || dayjs(currentDate.value).isAfter(today, 'day')
    }
    else if (switchType === 'week') {
        return dayjs(currentDate.value).isSame(today, 'week') || dayjs(currentDate.value).isAfter(today, 'week')
    }
    else {
        return dayjs(currentDate.value).isSame(today, 'month') || dayjs(currentDate.value).isAfter(today, 'month')
    }
})
</script>

<template>
    <div flex justify-center>
        <div flex items-center gap-8px>
            <div class="i-radix-icons:chevron-left font-600" @click="handlePrev"></div>

            <div v-if="switchType === 'week'">
                <div>
                    {{ formatWeek[0] }} - {{ formatWeek[1] }}
                </div>
            </div>

            <div v-else-if="switchType === 'month'">
                <span v-if="isCurrentYear">
                    {{ dayjs(currentDate).format('M月') }}
                </span>
                <span v-else>
                    {{ dayjs(currentDate).format('YYYY年M月') }}
                </span>
            </div>

            <div v-else>
                <span v-if="isCurrentYear">{{ dayjs(currentDate).format('M月D日') }}</span>
                <span v-else>{{ dayjs(currentDate).format('YYYY年M月D日') }}</span>
            </div>

            <div
                class="i-radix-icons:chevron-right font-600 i-radix-icons:chevron-right font-600"
                :class="[
                    isNextDisabled ? 'text-gray-300 pointer-events-none' : '',
                ]"
                @click="handleNext"
            ></div>
        </div>
    </div>
</template>
