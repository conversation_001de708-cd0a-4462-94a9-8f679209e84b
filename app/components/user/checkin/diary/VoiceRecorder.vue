<script setup lang="ts">
import type WXSDK from 'weixin-js-sdk'

// 工具实例
const props = defineProps<{
    wx: typeof WXSDK | null
}>()

const emit = defineEmits<{
    translate: [value: string]
}>()
// 响应式状态
const isVoiceRecording = ref(false)
const isVoiceCancelling = ref(false)
const recordLocalId = ref('')

function resetVoiceState() {
    isVoiceRecording.value = false
    isVoiceCancelling.value = false
    // 只在iOS上恢复滚动行为
    if (isIOS()) {
        document.body.style.overflow = ''
        document.body.style.position = ''
        document.body.style.width = ''
    }
}

// 开始录音
function startVoiceRecord() {
    if (!props.wx) {
        showToast('微信SDK未初始化')
        return
    }
    isVoiceRecording.value = true
    isVoiceCancelling.value = false
    props.wx.startRecord()
    // 只在iOS上阻止默认滚动行为
    if (isIOS()) {
        document.body.style.overflow = 'hidden'
        document.body.style.position = 'fixed'
        document.body.style.width = '100%'
    }
}

// 停止录音
function stopVoiceRecord(isTranslate: boolean) {
    if (!props.wx) {
        showToast('微信SDK未初始化')
        return
    }

    props.wx.stopRecord({
        success: (res) => {
            if (isTranslate && !isVoiceCancelling.value) {
                recordLocalId.value = res.localId
                // 转换语音为文字
                props.wx?.translateVoice({
                    localId: recordLocalId.value,
                    isShowProgressTips: 1,
                    success: (res2) => {
                        if (isTranslate && res2.translateResult) {
                            emit('translate', res2.translateResult)
                        }
                    },
                    fail: () => {
                        showToast('语音转换失败')
                    },
                })
            }
            resetVoiceState()
        },
        fail: () => {
            showToast('录音失败')
            resetVoiceState()
        },
    })
}

function handleTouchMove(event: TouchEvent) {
    if (!isVoiceRecording.value) return

    const touch = event.touches[0]
    if (!touch) return

    // 使用rem单位计算底部区域高度
    const bottomHeight = 8.6875 * 16 // 139px = 8.6875rem
    const isOutOfArea = touch.clientY < (window.innerHeight - bottomHeight)

    isVoiceCancelling.value = isOutOfArea
}

function handleCancelTouchStart() {
    isVoiceCancelling.value = true
}

function handleCancelTouchEnd() {
    stopVoiceRecord(false)
}
</script>

<template>
    <div>
        <div
            class="w-42px h-42px rd-100px border-1px border-#E4FAF9 bg-#E4FAF9 flex items-center justify-center cursor-pointer select-none"
            :class="isVoiceRecording ? 'border-#6AD9CB bg-#6AD9CB' : 'border-#E4FAF9 bg-#E4FAF9'"
            @touchstart="startVoiceRecord"
            @touchmove="handleTouchMove"
            @touchend="stopVoiceRecord(true)"
        >
            <div class="w-16px h-16px bg-[url('~/assets/icons/checkin/microphone.svg')] bg-no-repeat bg-center bg-contain"></div>
        </div>

        <transition name="fade">
            <div v-if="isVoiceRecording" class="voice-recording-overlay">
                <div class="voice-recording-content">
                    <div class="voice-recording-gif"></div>
                    <span class="voice-recording-text">松手后自动识别成文本</span>
                </div>
                <div class="voice-recording-bottom">
                    <div
                        v-show="!isVoiceCancelling"
                        class="voice-recording-cancel select-none bg-[url('@/assets/icons/checkin/voice-success-cancel.svg')] bg-no-repeat bg-center bg-contain"
                        @touchstart="handleCancelTouchStart"
                        @touchmove="handleTouchMove"
                        @touchend="handleCancelTouchEnd"
                    ></div>
                    <div
                        v-show="isVoiceCancelling"
                        class="voice-recording-cancel select-none bg-[url('@/assets/icons/checkin/voice-error-cancel.svg')] bg-no-repeat bg-center bg-contain"
                        @touchstart="handleCancelTouchStart"
                        @touchmove="handleTouchMove"
                        @touchend="handleCancelTouchEnd"
                    ></div>
                    <div
                        v-show="!isVoiceCancelling"
                        class="voice-recording-bottom-icon select-none bg-[url('@/assets/icons/checkin/voice-success.svg')] bg-no-repeat bg-center bg-contain"
                    ></div>
                    <div
                        v-show="isVoiceCancelling"
                        class="voice-recording-bottom-icon select-none bg-[url('@/assets/icons/checkin/voice-error.svg')] bg-no-repeat bg-center bg-contain"
                    ></div>
                    <div class="voice-recording-wifi select-none bg-[url('@/assets/icons/checkin/voice-wifi.svg')] bg-no-repeat bg-center bg-contain"></div>
                </div>
            </div>
        </transition>
    </div>
</template>

<style scoped>
.select-none {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.fade-enter-active,
.fade-leave-active {
    transition: opacity var(--transition-duration) ease;
}

.fade-enter-from,
.fade-leave-to {
    opacity: 0;
}

.fade-enter-to,
.fade-leave-from {
    opacity: 1;
}

.voice-recording-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100vh;
    background: #00000099;
    display: flex;
    flex-direction: column;
    align-items: center;
    z-index: 999;
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.voice-recording-overlay * {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.voice-recording-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 16px;
    flex: 1;
    justify-content: center;
}

.voice-recording-gif {
    width: 160px;
    height: 160px;
    background: url('@/assets/icons/checkin/microphone-animated.svg') no-repeat center center;
    background-size: contain;
}

.voice-recording-text {
    color: white;
    font-size: 16px;
    font-weight: 600;
}

.voice-recording-bottom {
    width: 100%;
    height: 139px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.voice-recording-bottom-icon {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.voice-recording-wifi {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 32px;
    height: 32px;
}

.voice-recording-cancel {
    position: absolute;
    left: 44px;
    top: -80px;
    width: 44px;
    height: 44px;
    z-index: 1;
}
</style>
