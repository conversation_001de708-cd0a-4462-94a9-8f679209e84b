<script setup lang="ts">
import Chart from 'chart.js/auto'
import { computed, onMounted, ref, watch } from 'vue'

interface WeightData {
    historyWeight: number | null
    currentWeight: number | null
    targetWeight: number | null
}

const props = withDefaults(defineProps<{
    historyWeight?: number | string | null
    currentWeight?: number | string | null
    targetWeight?: number | string | null
}>(), {
    historyWeight: null,
    currentWeight: null,
    targetWeight: null,
})

const chartRef = ref<HTMLCanvasElement>()
const chartInstance = ref<Chart | null>(null)

// 验证体重数据是否有效
function isValidWeight(weight: number | string | null): weight is number {
    if (weight === null) return false
    const num = typeof weight === 'string' ? Number(weight) : weight
    return !Number.isNaN(num) && num > 0
}

// 获取有效的体重数据
const validWeights = computed<WeightData>(() => ({
    historyWeight: isValidWeight(props.historyWeight) ? Number(props.historyWeight) : null,
    currentWeight: isValidWeight(props.currentWeight) ? Number(props.currentWeight) : null,
    targetWeight: isValidWeight(props.targetWeight) ? Number(props.targetWeight) : null,
}))

// 判断是否有足够的数据显示图表
const hasEnoughData = computed(() => {
    const weights = validWeights.value
    return [weights.historyWeight, weights.currentWeight, weights.targetWeight].filter(isValidWeight).length >= 2
})

const pointColor = computed(() => {
    if (!isValidWeight(validWeights.value.currentWeight)) return '#00BFA0'

    const weights = validWeights.value
    const isOutOfRange = isValidWeight(weights.currentWeight) && (
        Math.max(weights.historyWeight ?? 0, weights.targetWeight ?? 0) < weights.currentWeight
        || Math.min(weights.historyWeight ?? Infinity, weights.targetWeight ?? Infinity) > weights.currentWeight
    )

    return isOutOfRange ? '#FF9500' : '#00BFA0'
})

// 定义虚线网格插件
const dashedGridPlugin = {
    id: 'dashedGrid',
    beforeDraw(chart: any) {
        const { ctx: gridCtx, chartArea, scales } = chart
        const { top, bottom, left, right } = chartArea
        const yScale = scales.y

        // 绘制横向虚线网格
        gridCtx.save()
        gridCtx.beginPath()
        gridCtx.lineWidth = 1
        gridCtx.strokeStyle = '#E5E7EB'
        gridCtx.setLineDash([5, 5])

        // 获取所有Y轴刻度点并绘制横向虚线
        const tickCount = yScale.ticks.length
        for (let i = 0; i < tickCount; i++) {
            const yPixel = yScale.getPixelForTick(i)

            // 确保线条在图表区域内
            if (yPixel >= top && yPixel <= bottom) {
                gridCtx.moveTo(left, yPixel)
                gridCtx.lineTo(right, yPixel)
            }
        }

        gridCtx.stroke()
        gridCtx.restore()
    },
}

// 定义权重标签插件
const weightLabelsPlugin = {
    id: 'weightLabels',
    afterDraw: (chart: any) => {
        const chartCtx = chart.ctx
        const meta = chart.getDatasetMeta(0)
        const canvasHeight = chart.height

        const { historyWeight, currentWeight, targetWeight } = validWeights.value

        // 绘制历史体重标签
        if (isValidWeight(historyWeight)) {
            const historyPoint = meta.data[0]
            if (historyPoint) {
                chartCtx.save()
                chartCtx.font = 'bold 14px "PingFang SC", "Helvetica Neue", Arial, sans-serif'
                chartCtx.fillStyle = '#333'
                chartCtx.textAlign = 'center'
                chartCtx.fillText(`${historyWeight}kg`, historyPoint.x + 10, historyPoint.y - 15)
                chartCtx.restore()
            }
        }

        // 绘制目标体重标签
        if (isValidWeight(targetWeight)) {
            const targetPoint = meta.data[2]
            if (targetPoint) {
                chartCtx.save()
                // 绘制目标体重数值
                chartCtx.font = 'bold 14px "PingFang SC", "Helvetica Neue", Arial, sans-serif'
                chartCtx.fillStyle = '#333'
                chartCtx.textAlign = 'center'
                chartCtx.fillText(`${targetWeight}kg`, targetPoint.x - 20, targetPoint.y - 15)

                // 绘制"目标体重"文字
                chartCtx.font = '400 12px "PingFang SC", "Helvetica Neue", Arial, sans-serif'
                chartCtx.fillStyle = '#868F9C'
                chartCtx.fillText('目标体重', targetPoint.x - 20, targetPoint.y - 35)

                chartCtx.restore()
            }
        }

        // 绘制当前体重标签
        if (isValidWeight(currentWeight)) {
            const currentPoint = meta.data[1]
            if (!currentPoint) return
            chartCtx.save()

            const tooltipWidth = 120
            const tooltipHeight = 40
            const tooltipRadius = 20
            const tooltipX = currentPoint.x - tooltipWidth / 2

            // 检查是否靠近顶部或底部，并确保提示框不会超出canvas边界
            let isNearTop = currentPoint.y < tooltipHeight + 40
            const isNearBottom = currentPoint.y + tooltipHeight + 25 > canvasHeight - 10

            let tooltipY
            if (isNearBottom) {
                tooltipY = currentPoint.y - tooltipHeight - 25
                isNearTop = false
            } else if (isNearTop) {
                tooltipY = currentPoint.y + 25
            } else {
                tooltipY = currentPoint.y - tooltipHeight - 25
            }

            chartCtx.beginPath()
            chartCtx.moveTo(tooltipX + tooltipRadius, tooltipY)
            chartCtx.lineTo(tooltipX + tooltipWidth - tooltipRadius, tooltipY)
            chartCtx.quadraticCurveTo(tooltipX + tooltipWidth, tooltipY, tooltipX + tooltipWidth, tooltipY + tooltipRadius)
            chartCtx.lineTo(tooltipX + tooltipWidth, tooltipY + tooltipHeight - tooltipRadius)
            chartCtx.quadraticCurveTo(tooltipX + tooltipWidth, tooltipY + tooltipHeight, tooltipX + tooltipWidth - tooltipRadius, tooltipY + tooltipHeight)
            chartCtx.lineTo(tooltipX + tooltipRadius, tooltipY + tooltipHeight)
            chartCtx.quadraticCurveTo(tooltipX, tooltipY + tooltipHeight, tooltipX, tooltipY + tooltipHeight - tooltipRadius)
            chartCtx.lineTo(tooltipX, tooltipY + tooltipRadius)
            chartCtx.quadraticCurveTo(tooltipX, tooltipY, tooltipX + tooltipRadius, tooltipY)
            chartCtx.closePath()
            chartCtx.fillStyle = pointColor.value
            chartCtx.fill()

            chartCtx.beginPath()
            if (isNearTop) {
                chartCtx.moveTo(currentPoint.x - 10, tooltipY)
                chartCtx.lineTo(currentPoint.x + 10, tooltipY)
                chartCtx.lineTo(currentPoint.x, currentPoint.y + 15)
            } else {
                chartCtx.moveTo(currentPoint.x - 10, tooltipY + tooltipHeight)
                chartCtx.lineTo(currentPoint.x + 10, tooltipY + tooltipHeight)
                chartCtx.lineTo(currentPoint.x, currentPoint.y - 15)
            }
            chartCtx.closePath()
            chartCtx.fillStyle = pointColor.value
            chartCtx.fill()

            chartCtx.font = 'bold 16px "PingFang SC", "Helvetica Neue", Arial, sans-serif'
            chartCtx.fillStyle = 'white'
            chartCtx.textAlign = 'center'
            chartCtx.textBaseline = 'middle'
            chartCtx.fillText(`当前 ${currentWeight}kg`, currentPoint.x, tooltipY + tooltipHeight / 2)
            chartCtx.restore()
        }
    },
}

function render() {
    if (!chartRef.value || !hasEnoughData.value) return

    if (chartInstance.value) {
        chartInstance.value.destroy()
    }

    const canvas = chartRef.value
    const ctx = canvas.getContext('2d')!
    const { historyWeight, currentWeight, targetWeight } = validWeights.value

    // 计算有效数据的范围
    const validData = [historyWeight, currentWeight, targetWeight].filter(isValidWeight)
    const minWeight = Math.min(...validData) - 1
    const maxWeight = Math.max(...validData) + 1

    // 准备图表数据
    const labels = ['历史', '当前', '目标']
    const data = [historyWeight, currentWeight, targetWeight]

    // 判断当前体重是否超出历史体重和目标体重的区间
    const isOutOfRange = isValidWeight(currentWeight) && (
        Math.max(historyWeight ?? 0, targetWeight ?? 0) < currentWeight
        || Math.min(historyWeight ?? Infinity, targetWeight ?? Infinity) > currentWeight
    )

    const lineData = isValidWeight(currentWeight)
        ? isOutOfRange
            ? (() => {
                // 计算历史体重和目标体重的区间
                    const historyNum = Number(historyWeight)
                    const targetNum = Number(targetWeight)
                    const maxValue = Math.max(historyNum, targetNum)
                    const currentNum = Number(currentWeight)

                    // 判断当前体重是高于最大值还是低于最小值
                    const isAboveRange = currentNum > maxValue
                    // 如果高于区间，则在下方构造点；如果低于区间，则在上方构造点
                    const adjustedFakePoint = isAboveRange ? currentNum - 2 : currentNum + 2

                    return [historyWeight, adjustedFakePoint, targetWeight]
                })()
            : [historyWeight, currentWeight, targetWeight]
        : [historyWeight, null, targetWeight]

    chartInstance.value = new Chart(ctx, {
        type: 'line',
        data: {
            labels,
            datasets: [
                {
                    label: '体重',
                    data,
                    fill: false,
                    borderColor: 'transparent',
                    borderWidth: 0,
                    tension: 0.4,
                    pointBackgroundColor(context: { dataIndex: number }) {
                        const index = context.dataIndex
                        if (index === 0) {
                            return '#999'
                        }
                        if (index === 2) {
                            return '#999'
                        }
                        return pointColor.value
                    },
                    pointBorderColor: 'white',
                    pointBorderWidth: 2,
                    pointRadius: 6,
                    pointHoverRadius: 8,
                },
                {
                    label: '连线',
                    data: lineData,
                    borderColor: '#00BFA0',
                    borderWidth: 3,
                    tension: 0.4,
                    spanGaps: true,
                    pointRadius: 0,
                    pointHoverRadius: 0,
                },
            ],
        },
        options: {
            plugins: {
                legend: {
                    display: false,
                },
                tooltip: {
                    enabled: false,
                },
            },
            scales: {
                x: {
                    grid: {
                        display: true,
                        color: '#E4FAF9',
                        drawTicks: false,
                        drawOnChartArea: true,
                        lineWidth: 1,
                    },
                    border: {
                        display: true,
                    },
                    ticks: {
                        display: false,
                    },
                    offset: false,
                    position: 'left',
                },
                y: {
                    min: minWeight,
                    max: maxWeight,
                    grid: {
                        display: false,
                    },
                    border: {
                        display: true,
                    },
                    ticks: {
                        display: false,
                        count: 5,
                    },
                    position: 'bottom',
                    offset: true,
                },
            },
            responsive: true,
            maintainAspectRatio: false,
            layout: {
                padding: {
                    top: 10,
                    right: 10,
                    bottom: 16,
                    left: 10,
                },
            },
        },
        plugins: [dashedGridPlugin, weightLabelsPlugin],
    })

    return chartInstance.value
}

watch(() => [props.historyWeight, props.currentWeight, props.targetWeight], () => {
    render()
})

onMounted(() => {
    render()
})
</script>

<template>
    <canvas ref="chartRef" class="w-100% h-100%"></canvas>
</template>
