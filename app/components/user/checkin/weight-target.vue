<script setup lang="ts">
const emit = defineEmits<{
    (e: 'success'): void
}>()

const showAddActionSheet = defineModel({
    default: false,
})

const targetValue = ref()
const confirmValue = ref()

defineExpose({
    confirmValue,
})

async function getTarget() {
    const { results } = await useWrapFetch<BaseResponse<CustomerIndex>>('/checkInCustomerIndex/get', { method: 'post', body: {
        kind: '5',
    } })

    targetValue.value = results.weightIndex || 60
    confirmValue.value = results.weightIndex || 60
}

getTarget()

async function handleSave() {
    try {
        await useWrapFetch('/checkInCustomerIndex/save', { method: 'post', body: {
            weightIndex: targetValue.value,
        } })

        confirmValue.value = targetValue.value
        emit('success')
        showAddActionSheet.value = false
    } catch {
        showFailToast('保存失败')
    }
}

const sliderRuleRef = useTemplateRef('sliderRuleRef')
whenever(showAddActionSheet, async () => {
    await nextTick()
    sliderRuleRef.value?.renderRule()
})
</script>

<template>
    <van-action-sheet class="linear-gradient-content" :show="showAddActionSheet" @close="showAddActionSheet = false">
        <div>
            <div class="flex justify-between items-center p-16px">
                <div class="text-center font-600 text-16px text-t-5">设置目标体重</div>
                <div class="text-right text-16px text-t-5 i-radix-icons:cross-2 w-16px h-16px" @click="showAddActionSheet = false"></div>
            </div>

            <user-checkin-slider-rule
                ref="sliderRuleRef"
                v-model="targetValue"
                unit="kg"
                dialog-title="设置目标体重"
                :rule-props="{
                    maxValue: 200,
                    minValue: 30,
                    divide: 5,
                    precision: 0.1,
                }"
                btn-text="确定"
                @save="handleSave"
            />

            <div h-16px></div>
        </div>
    </van-action-sheet>
</template>
