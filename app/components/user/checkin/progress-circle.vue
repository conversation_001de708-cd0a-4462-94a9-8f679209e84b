<script setup lang="ts">
const { checkInDate } = defineProps<{
    checkInDate: string
}>()

const showCheckInExerciseActionSheet = ref(false)
const showCheckInWaterActionSheet = ref(false)
const showCheckInWeightActionSheet = ref(false)

const route = useRoute()
const dayjs = useDayjs()

onMounted(() => {
    if (route.hash.includes('sportDialogOpen')) {
        showCheckInExerciseActionSheet.value = true

        // 清除 hash 中的 sportDialogOpen
        history.replaceState(null, document.title, window.location.pathname + window.location.search)
    }
})

const balls = ref([
    {
        key: 'sport',
        name: '步数',
        value: 0,
        ballStyle: {
            background: 'radial-gradient(83.54% 83.54% at 24.39% 7.93%, #1DF0CD 0%, #09C7A7 100%)',
        },
        ballClass: 'top-109px left-95px w-82px h-82px',
        valueStyle: {
            fontSize: '18px',
            fontWeight: '600',
        },
        valueClass: 'text-18px font-600',
        nameClass: 'text-13px',
        onClick: async () => {
            if (!dayjs(checkInDate).isSame(dayjs(), 'day')) {
                showToast('只允许当天打卡')
                return
            }

            const { wxRunData } = useWxRunData()
            if (!wxRunData?.step) {
                const wx = await useWxBridge({})

                wx?.miniProgram.redirectTo({
                    url: `/pages/run/index`,
                })
            } else {
                showCheckInExerciseActionSheet.value = true
            }
        },
    },
    {
        key: 'weight',
        name: '体重(kg)',
        value: 0,
        ballStyle: {
            background: 'radial-gradient(59.7% 59.7% at 35.82% 32.09%, #A78CFB 0%, #A565E5 100%)',
        },
        ballClass: 'top-174px left-137px w-67px h-67px',
        valueClass: 'text-14px font-600',
        nameClass: 'text-10px',
        onClick: () => {
            if (!dayjs(checkInDate).isSame(dayjs(), 'day')) {
                showToast('只允许当天打卡')
                return
            }

            showCheckInWeightActionSheet.value = true
        },
    },
    {
        key: 'waterIntake',
        name: '喝水(ml)',
        value: 0,
        ballStyle: {
            background: 'radial-gradient(68% 68% at 20% 20%, #9AE2FF 0%, #17AEFF 100%)',
        },
        ballClass: 'top-125px left-182px w-75px h-75px',
        valueClass: 'text-16px font-600',
        nameClass: 'text-11px',
        onClick: () => {
            if (!dayjs(checkInDate).isSame(dayjs(), 'day')) {
                showToast('只允许当天打卡')
                return
            }

            showCheckInWaterActionSheet.value = true
        },
    },
])

defineExpose({
    balls,
})

interface CheckInCustomerData {
    data: {
        checkInCount: number
        days: {
            checkInCount: number
            day: string
            sport: number
            waterIntake: number
            weight: number
        }[]
        start: string
    }
    starCount: number
}

const checkInCustomerData = ref<CheckInCustomerData>()

async function getCheckInDataByDate(date: string) {
    const { results } = await useWrapFetch<BaseResponse<CheckInCustomerData>>('/checkInCustomerData/getByDate', {
        method: 'POST',
        body: {
            checkInDate: date,
        },
    })

    const findDay = results.data.days.find(day => day.day === date)

    balls.value[0]!.value = findDay?.sport || 0
    balls.value[1]!.value = findDay?.weight || 0
    balls.value[2]!.value = findDay?.waterIntake || 0

    checkInCustomerData.value = results
}

whenever(() => checkInDate, async (val) => {
    await getCheckInDataByDate(val)
}, {
    immediate: true,
})

const outerCircleOffset = computed(() => {
    return 270 - ((checkInCustomerData.value?.data.checkInCount || 0) / 7) * 270
})

const innerCircleOffset = computed(() => {
    const findDay = checkInCustomerData.value?.data.days.find(day => day.day === checkInDate)
    return 220.5 - ((findDay?.checkInCount || 0) / CHECKIN_ITEM_COUNT) * 220.5
})

function handleCheckInSuccess(type: CheckInType) {
    switch (type) {
        case 'exercise':
            showCheckInExerciseActionSheet.value = false
            showSuccessToast('打卡成功')
            getCheckInDataByDate(checkInDate)
            break
        case 'water':
            showCheckInWaterActionSheet.value = false
            showSuccessToast('打卡成功')
            getCheckInDataByDate(checkInDate)
            break
        case 'weight':
            showCheckInWeightActionSheet.value = false
            showSuccessToast('打卡成功')
            getCheckInDataByDate(checkInDate)
            break
        default:
            break
    }
}
</script>

<template>
    <div class="progress-circle">
        <svg viewBox="0 0 100 100">
            <!-- 外层圆环 -->
            <circle cx="50" cy="50" r="43" fill="none" opacity="0.04" stroke="#00AC97" stroke-width="7PX" />
            <circle
                cx="50" cy="50" r="43"
                class="progress-outer"
                :style="{ strokeDashoffset: outerCircleOffset }"
                stroke="#14E9CF" stroke-linecap="round"
            />

            <!-- 内层圆环 -->
            <circle cx="50" cy="50" r="35" fill="none" opacity="0.04" stroke="#F98804" stroke-width="7PX" />
            <circle
                cx="50" cy="50" r="35"
                class="progress-inner"
                :style="{ strokeDashoffset: innerCircleOffset }"
                fill="none" stroke="#FFCE3B" stroke-linecap="round"
            />

            <text x="50" y="8.5" text-anchor="middle" font-size="4PX" font-weight="bold" fill="#fff">W</text>
            <!-- 内层圆环起始位置的文字 -->
            <text x="50" y="16.5" text-anchor="middle" font-size="4PX" font-weight="bold" fill="#fff">D</text>
        </svg>

        <div
            v-for="ball in balls" :key="ball.name"
            :style="ball.ballStyle"
            class="text-white rd-full absolute flex flex-col items-center justify-center"
            :class="ball.ballClass"
            @click="ball.onClick"
        >
            <div :class="ball.valueClass">
                {{ ball.value }}
            </div>

            <div :class="ball.nameClass">
                {{ ball.name }}
            </div>
        </div>

        <user-checkin-exercise
            v-model="showCheckInExerciseActionSheet"
            :check-in-date
            @success="handleCheckInSuccess('exercise')"
        />

        <user-checkin-water
            v-model="showCheckInWaterActionSheet"
            :check-in-date
            @success="handleCheckInSuccess('water')"
        />

        <user-checkin-weight
            v-model="showCheckInWeightActionSheet"
            :check-in-date
            @success="handleCheckInSuccess('weight')"
        />
    </div>
</template>

<style scoped>
.progress-circle {
  position: relative;
  width: 100%;
}

svg {
  display: block;
}

.progress-outer {
  fill: none;
  stroke-dasharray: 270;
  stroke-width: 7PX;
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
  transition: stroke-dashoffset 0.6s ease;
}

.progress-inner {
  fill: none;
  stroke-dasharray: 220;
  stroke-width: 7PX;
  transform: rotate(-90deg);
  transform-origin: 50% 50%;
  transition: stroke-dashoffset 0.6s ease;
}
</style>
