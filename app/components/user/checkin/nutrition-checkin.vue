<script setup lang="ts">
import { v4 as uuidv4 } from 'uuid'

const props = defineProps<{
    nutritionCheckinData: NutritionCheckinData
    type: 'probioticsPictures' | 'prebioticsPictures'
    isLoading: boolean
    checkInDate: string
}>()

const emit = defineEmits<{
    (e: 'add', type: 'probioticsPictures' | 'prebioticsPictures', picture: NutritionPicture): void
    (e: 'delete', type: 'probioticsPictures' | 'prebioticsPictures', id: string): void
}>()

const dayjs = useDayjs()

const isToday = computed(() => {
    return dayjs(props.checkInDate).isSame(dayjs(), 'day')
})

async function uploadFile(file: File, type: 'probioticsPictures' | 'prebioticsPictures') {
    const { closeLoading } = useLoading({
        message: '上传中...',
    })
    try {
        const formData = new FormData()
        formData.append('file', file)
        const { results } = await useWrapFetch<BaseResponse<string>>('/v1/file/upload', {
            body: formData,
            method: 'post',
        })

        const metaData = {
            id: uuidv4().split('-')[0]!,
            pictureUrl: formatResource(results),
            createdAt: dayjs().format('YYYY-MM-DD HH:mm:ss'),
        }

        emit('add', type, metaData)
    } catch (error) {
        console.log(error)
    } finally {
        closeLoading()
    }
}

async function handleImageRead(file: File, type: 'probioticsPictures' | 'prebioticsPictures') {
    try {
        const fileMbSize = file.size / 1024 / 1024

        if (fileMbSize > 100) {
            showFailToast('图片大小不能超过100MB')
            return false
        }

        if (fileMbSize > 5) {
            const compressedFile = await compressImage(file)
            uploadFile(compressedFile, type)
        } else {
            uploadFile(file, type)
        }
    } catch (error) {
        console.log(error)
        showFailToast('上传图片失败')
    }
}

function handleDeleteImage(id: string) {
    emit('delete', props.type, id)
}
</script>

<template>
    <div class="rd-10px bg-white p-16px">
        <div class="flex items-center gap-8px">
            <div class="i-custom:checkin-food-dot w-22px h-22px"></div>

            <div class="text-15px text-t-5 font-600">
                {{ props.type === 'probioticsPictures' ? '益生菌' : '益生元' }}
            </div>
        </div>

        <div>
            <van-swipe-cell
                v-for="item in nutritionCheckinData[props.type] || []"
                :key="item.id!"
                class="rd-10px py-8px"
                :class="{ 'active:bg-gray-1': nutritionCheckinData.id === null }"
                :disabled="!isToday"
            >
                <div class="h-42px flex justify-between items-center">
                    <div class="flex items-center gap-8px">
                        <van-image :src="item.pictureUrl" class="w-42px h-42px rd-10px overflow-hidden" />

                        <div class="flex flex-col justify-between">
                            <div class="text-13px text-t-5 font-600">
                                {{ props.type === 'probioticsPictures' ? '益生菌' : '益生元' }}
                            </div>
                            <div class="text-12px text-t-3">
                                {{ dayjs(item.createdAt).format('hh:mm') }}
                            </div>
                        </div>
                    </div>
                </div>
                <template #right>
                    <div class="flex">
                        <div class="w-9px"></div>
                        <van-button class="!w-42px" square type="danger" icon="delete-o" @click="handleDeleteImage(item.id!)" />
                    </div>
                </template>
            </van-swipe-cell>
        </div>

        <van-empty v-if="nutritionCheckinData[props.type].length === 0 && !isToday" description="暂无打卡记录" />
        <div v-if="nutritionCheckinData[props.type].length === 0 && isToday" class="h-16px"></div>

        <van-uploader
            v-if="nutritionCheckinData[props.type].length < 3 && isToday"
            :before-read="(event) => handleImageRead(event as File, props.type) as any"
        >
            <van-button block round class="!bg-#E4FAF9 !w-311px !border-none gap-4px">
                <div class="flex items-center justify-center gap-4px">
                    <div class="i-custom:checkin-camera w-15px h-15px text-#00AC97"></div>
                    <div class="text-14px text-#00AC97 font-600">
                        添加照片
                    </div>
                </div>
            </van-button>
        </van-uploader>
    </div>
</template>
