<script setup lang="ts">
import dayjs from 'dayjs'

const {
    checkInDate = dayjs().format('YYYY-MM-DD'),
    waterIntake = 0,
} = defineProps<{
    checkInDate?: string
    waterIntake?: number
}>()

const emit = defineEmits<{
    (e: 'success', newVal?: number): void
}>()

const showAddActionSheet = defineModel({
    default: false,
})

const waterValue = ref(waterIntake)

watch(() => waterIntake, (val) => {
    waterValue.value = val
}, { immediate: true })

async function handleSave() {
    try {
        await useWrapFetch('/checkInCustomerDrinkWater/save', { method: 'post', body: {
            waterIntake: String(waterValue.value),
            checkInDate,
        } })
        emit('success', waterValue.value)
    } catch {
        showFailToast('保存失败')
    }
}

const sliderRuleRef = useTemplateRef('sliderRuleRef')

whenever(showAddActionSheet, async () => {
    await nextTick()
    sliderRuleRef.value?.renderRule()
})
</script>

<template>
    <van-action-sheet class="linear-gradient-content" :show="showAddActionSheet" @close="showAddActionSheet = false">
        <div>
            <div class="flex justify-between items-center p-16px">
                <div class="w-14px"></div>
                <div class="text-center font-600 text-16px text-t-5">饮水打卡</div>
                <div class="text-right text-16px text-t-5 i-radix-icons:cross-2 w-16px h-16px" @click="showAddActionSheet = false"></div>
            </div>

            <user-checkin-slider-rule
                ref="sliderRuleRef"
                v-model="waterValue"
                unit="ml"
                dialog-title="设置饮水量"
                :rule-props="{
                    maxValue: 5000,
                    minValue: 100,
                    precision: 50,
                    divide: 15,
                }"
                @save="handleSave"
            />
        </div>
    </van-action-sheet>
</template>
