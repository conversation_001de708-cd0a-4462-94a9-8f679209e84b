<script setup lang="ts">
interface CalendarData {
    date: string
    count: number
}

const checkInDate = defineModel<string>('checkInDate')

const dayjs = useDayjs()

const show = defineModel<boolean>('show')

const calendarRef = useTemplateRef('calendarRef')

whenever(show, () => {
    const date = (calendarRef.value as any)?.getSelectedDate() || new Date()
    getCalendarData(date)
})

const calendarData = ref<CalendarData[]>([])

async function getCalendarData(date: Date) {
    try {
        const year = date.getFullYear()
        const month = date.getMonth() + 1
        const { results } = await useWrapFetch<BaseResponse<CalendarData[]>>(`/api/checkInCustomerData/calendar/${year}/${month}`)
        calendarData.value = results
    } catch (error) {
        console.log(error)
    }
}

function handleDateChange(date: Date) {
    checkInDate.value = dayjs(date).format('YYYY-MM-DD')
    show.value = false
}
</script>

<template>
    <van-calendar
        ref="calendarRef"
        v-model:show="show"
        class="custom-top-calendar"
        switch-mode="month"
        :round="false"
        :max-date="dayjs().toDate()"
        position="top"
        @panel-change="(e) => getCalendarData(e.date)"
        @select="handleDateChange"
    >
        <!-- <template #text="{ date, type }">
            <div flex flex-col items-center gap-5px>
                <div class="calendar-text" :class="[type === 'disabled' && 'calendar-text-disabled']" relative top-7px font-500 text="16px">
                    {{ date.getDate() }}
                </div>
                <user-checkin-calender-circle
                    v-if="dayjs(date).isSameOrBefore(dayjs(), 'day')"
                    :count="calendarData.find(item => item.date === dayjs(date).format('YYYY-MM-DD'))?.count || 0"
                />
                <div v-else class="h-18px"></div>
            </div>
        </template> -->

        <template #footer>
            <div flex justify-center items-center relative bottom-16px @click="show = false">
                <div bg-primary-1 flex justify-center items-center gap-5px w-105px h-40px rd-10px text-15px text-primary-6>
                    <div class="i-radix-icons:caret-up w-20px h-20px"></div>
                    收起
                </div>
            </div>
        </template>
    </van-calendar>
</template>

<style>
.custom-top-calendar .van-calendar__popup.van-popup--top {
  height: 65% !important;
}

/* .custom-top-calendar .van-calendar__selected-day {
  background: unset !important;
} */

/* .calendar-text {
  color: #4E5969;
}

.calendar-text-disabled {
  color: #96A0B5;
}

.van-calendar__selected-day .calendar-text {
  color: rgb(0, 172, 151);
} */
</style>
