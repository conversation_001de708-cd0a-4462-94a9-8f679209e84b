<script setup lang="ts">
import dayjs from 'dayjs'

const {
    initialWeight = undefined,
} = defineProps<{
    initialWeight?: number
}>()

const emit = defineEmits<{
    (e: 'success', newVal?: number): void
}>()

const showAddActionSheet = defineModel({
    default: false,
})

const weightValue = ref()

watch(() => initialWeight, (val) => {
    weightValue.value = Number(val)
}, { immediate: true })

async function handleSave() {
    try {
        await useWrapFetch('/user/updateInitialWeight', {
            method: 'put',
            params: {
                initialWeight: weightValue.value,
            },
        })

        emit('success')
    } catch (error) {
        console.log(error)
        showFailToast('保存失败')
    }
}

const sliderRuleRef = useTemplateRef('sliderRuleRef')

whenever(showAddActionSheet, async () => {
    await nextTick()
    sliderRuleRef.value?.renderRule()
})
</script>

<template>
    <van-action-sheet class="linear-gradient-content" :show="showAddActionSheet" @close="showAddActionSheet = false">
        <div>
            <div class="flex justify-between items-center p-16px">
                <div class="w-14px"></div>
                <div class="text-center font-600 text-16px text-t-5">设置初始体重</div>
                <div class="text-right text-16px text-t-5 i-radix-icons:cross-2 w-16px h-16px" @click="showAddActionSheet = false"></div>
            </div>

            <user-checkin-slider-rule
                ref="sliderRuleRef"
                v-model="weightValue"
                unit="kg"
                dialog-title="设置初始体重"
                btn-text="确定"
                :rule-props="{
                    maxValue: 200,
                    minValue: 30,
                    divide: 5,
                    precision: 0.1,
                }"
                @save="handleSave"
            />
        </div>
    </van-action-sheet>
</template>
