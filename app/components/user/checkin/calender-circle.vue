<script setup lang="ts">
const { count = 0 } = defineProps<{
    count: number
}>()

const circumference = 25

const offset = computed(() => {
    return circumference - (count / CHECKIN_ITEM_COUNT) * circumference
})
</script>

<template>
    <svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="100%" y2="0%">
                <stop offset="0%" stop-color="#FFA53A" />
                <stop offset="100%" stop-color="#F98804" />
            </linearGradient>
        </defs>
        <circle class="circle-bg" cx="10" cy="10" r="4" />
        <circle class="circle-progress" cx="10" cy="10" r="4" />
    </svg>
</template>

<style>
.circle-bg {
  fill: none; /* 不填充 */
  stroke: #d3d3d3; /* 灰色背景 */
  stroke-width: 2; /* 外环宽度 */
}

.circle-progress {
  fill: none; /* 不填充 */
  stroke: url(#gradient); /* 进度条颜色使用渐变 */
  stroke-width: 2; /* 外环宽度 */
  stroke-dasharray: 25; /* 圆环的周长 (2 * π * r，其中 r=4) */
  stroke-dashoffset: v-bind('offset'); /* 起始进度为 0% */
  stroke-linecap: round; /* 圆环两端为圆弧 */
  transform: rotate(-90deg); /* 起点从顶部开始 */
  transform-origin: 50% 50%; /* 以圆心为旋转中心 */
  transition: stroke-dashoffset 0.35s ease; /* 动画过渡 */
}
</style>
