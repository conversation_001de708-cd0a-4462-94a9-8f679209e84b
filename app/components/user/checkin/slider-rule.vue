<script setup lang="ts">
import { cloneDeep } from 'lodash-es'

import SlideRuler from '@/utils/slide-rule'

const { unit, ruleProps = {}, btnText = '立即打卡', dialogTitle = '请修改' } = defineProps<{
    unit: string
    ruleProps?: Record<string, any>
    btnText?: string
    extraClass?: string
    dialogTitle?: string
}>()

const emit = defineEmits<{
    (e: 'save'): void
}>()

const heightRulerRef = useTemplateRef('heightRulerRef')

const modelValue = defineModel<number>('modelValue')

let slider: any

const lastVibrationTime = ref(0)
const VIBRATION_THRESHOLD = 100

async function renderRule() {
    await nextTick()
    slider?.destroy()
    slider = new SlideRuler({
        canvasWidth: 325,
        fontSize: 13,
        heightDecimal: 28,
        heightDigit: 18,
        fontMarginTop: 45,
        el: heightRulerRef.value,
        currentValue: modelValue.value,
        ...ruleProps,
        handleValue: (v: number) => {
            modelValue.value = v || 0

            if (navigator?.vibrate) {
                const now = Date.now()
                if (now - lastVibrationTime.value > VIBRATION_THRESHOLD) {
                    navigator?.vibrate([10, 10])
                    lastVibrationTime.value = now
                }
            }
        },
    })
}

defineExpose({
    renderRule,
})

function handleSave() {
    emit('save')
}

if (import.meta.hot) {
    renderRule()
}

const isDialogShow = ref(false)
const dialogInputValue = ref(0)

function handleDialogOpen() {
    dialogInputValue.value = cloneDeep(modelValue.value) || 0
    isDialogShow.value = true
}

function handleDialogConfirm() {
    slider?.setValue(dialogInputValue.value)
    isDialogShow.value = false
}

// const debouncedLog = useDebounceFn((v) => {
//     slider?.setValue(v)
// }, 200)

// watch(modelValue, (v) => {
//     debouncedLog(v)
// })
</script>

<template>
    <div class="relative my-20px">
        <div v-if="!$slots.custom" text-center left-15px mb-10px flex items-center justify-center @click="handleDialogOpen">
            <span text="t-5 24px" mr-4px font-600>
                {{ modelValue }}
            </span>
            <span text="t-5 10px" relative top-3px>
                {{ unit }}
            </span>

            <div class="i-custom:pen w-12px h-12px ml-4px relative top-3px"></div>
        </div>
        <div v-else class="flex items-end justify-center">
            <slot name="custom"></slot>
            <div class="i-custom:pen w-12px h-12px ml-4px relative bottom-17px" @click="handleDialogOpen"></div>
        </div>
        <div ref="heightRulerRef"></div>

        <slot name="footer"></slot>
        <div v-if="!$slots.footer" class="flex justify-center my-40px" :class="extraClass">
            <van-button type="primary" round class="!w-160px" @click="handleSave">
                {{ btnText }}
            </van-button>
        </div>

        <van-dialog v-model:show="isDialogShow" teleport="body" :title="dialogTitle" show-cancel-button @confirm="handleDialogConfirm">
            <div class="flex items-end gap-4px justify-center my-10px">
                <input v-model="dialogInputValue" type="number" class="px-20px py-6px font-500 border-2px border-#E5E7EB rd-10px" />
                <span class="text-12px text-#4E5969">{{ unit }}</span>
            </div>
        </van-dialog>
    </div>
</template>
