<script setup lang="ts">
import dayjs from 'dayjs'

const {
    checkInDate = dayjs().format('YYYY-MM-DD'),
    weightAndHeight = undefined,
} = defineProps<{
    checkInDate?: string
    weightAndHeight?: {
        weight: string
        height: string
    }
}>()

const emit = defineEmits<{
    (e: 'success', newVal?: number): void
}>()

const showAddActionSheet = defineModel({
    default: false,
})

const weightValue = ref()
const heightValue = ref()

watch(() => weightAndHeight, (val) => {
    weightValue.value = Number(val?.weight)
    heightValue.value = val?.height
}, { immediate: true })

async function handleSave() {
    try {
        await useWrapFetch('/checkInCustomerWeight/save', {
            method: 'post',
            body: {
                weight: weightValue.value,
                checkInDate,
            },
        })

        const height = heightValue.value
        const newBmi = calcBmi(weightValue.value, height)

        await useWrapFetch('/user/preliminaryArchive', {
            method: 'post',
            body: {
                archiveWeight: weightValue.value,
                archiveBmi: newBmi,
            },
        })

        emit('success', weightValue.value)
    } catch (error) {
        console.log(error)
        showFailToast('保存失败')
    }
}

const sliderRuleRef = useTemplateRef('sliderRuleRef')

whenever(showAddActionSheet, async () => {
    await nextTick()
    sliderRuleRef.value?.renderRule()
})
</script>

<template>
    <van-action-sheet class="linear-gradient-content" :show="showAddActionSheet" @close="showAddActionSheet = false">
        <div>
            <div class="flex justify-between items-center p-16px">
                <div class="w-14px"></div>
                <div class="text-center font-600 text-16px text-t-5">体重打卡</div>
                <div class="text-right text-16px text-t-5 i-radix-icons:cross-2 w-16px h-16px" @click="showAddActionSheet = false"></div>
            </div>

            <user-checkin-slider-rule
                ref="sliderRuleRef"
                v-model="weightValue"
                unit="kg"
                dialog-title="设置体重"
                :rule-props="{
                    maxValue: 200,
                    minValue: 30,
                    divide: 5,
                    precision: 0.1,
                }"
                @save="handleSave"
            />
        </div>
    </van-action-sheet>
</template>
