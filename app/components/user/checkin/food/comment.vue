<script setup lang="ts">
const props = defineProps<{
    food: CheckinFoodRoot
}>()

const isOpen = ref(false)

function handleOpen() {
    isOpen.value = true
    getFoodComment()
}

function handleClose() {
    isOpen.value = false
}

const isLoading = ref(false)
const comment = ref('')
const score = ref(0)

async function getFoodComment() {
    try {
        isLoading.value = true
        comment.value = ''
        score.value = 0

        const foods = props.food.mealContent.items.map(item => ({
            name: item.name,
            weight: item.weight,
            calories: item.calories,
            foodType: item.foodType,
            rawMaterial: item.rawMaterial,
            carbohydrates: item.carbohydrates,
            protein: item.protein,
            fat: item.fat,
            dietaryFiber: item.dietaryFiber,
        }))
        const mealEvaluationList = props.food?.mealContent?.mealEvaluationList || []

        const { results } = await useWrapFetch<BaseResponse<any>>('/checkInCustomerMeal/diningReview', {
            method: 'post',
            body: {
                checkInDate: props.food.checkInDate,
                mealType: props.food.kind,
                foods,
                comments: mealEvaluationList,
            },
        })

        comment.value = results.foodReviews
        score.value = results.score
    } catch (error) {
        console.error(error)
        comment.value = '获取餐食点评失败，请稍后再试'
    } finally {
        isLoading.value = false
    }
}
</script>

<template>
    <div class="mt-10px">
        <div :class="isOpen ? 'opacity-0 h-0' : 'opacity-100 h-auto'" class="transition-all duration-500">
            <div class="flex justify-center items-center gap-3px" @click="handleOpen">
                <div class="text-t-5 text-15px">
                    查看餐食点评
                </div>

                <div class="i-radix-icons:chevron-down w-15px h-15px text-t-5"></div>
            </div>
        </div>

        <div class="bg-#F2F4F7 rd-10px overflow-hidden transition-all duration-300" :style="{ maxHeight: isOpen ? '620px' : '0' }">
            <div class="p-12px flex flex-col justify-between relative">
                <div class="break-all max-h-240px overflow-auto">
                    <shared-unified-loading v-if="isLoading" size="small" :rainbow="false" text="餐食分析中..." vertical />

                    <template v-else>
                        <van-rate :size="15" :model-value="score" color="#00AC97" void-color="#00AC97" allow-half />
                        <span block class="text-t-4 text-13px">
                            {{ comment }}
                        </span>
                    </template>
                </div>

                <div v-if="!isLoading" class="flex justify-center items-center pt-5px gap-3px" @click="handleClose">
                    <div class="text-t-5 text-15px">
                        收起餐食点评
                    </div>

                    <div class="i-radix-icons:chevron-up w-15px h-15px text-t-5"></div>
                </div>
            </div>
        </div>
    </div>
</template>
