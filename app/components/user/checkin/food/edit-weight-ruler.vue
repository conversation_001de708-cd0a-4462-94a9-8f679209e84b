<script setup lang="ts">
import SlideRuler from '@/utils/slide-rule'

const modelValue = defineModel<FoodItem>('modelValue')
let caloriePer1g = 0
let carbohydratesPer1g = 0
let proteinPer1g = 0
let fatPer1g = 0
let dietaryFiberPer1g = 0
watch(
    () => modelValue.value!.uuid,
    (val) => {
        if (val) {
            const v = modelValue.value!
            caloriePer1g = extractNumber(v.calories) / extractNumber(v.weight)
            carbohydratesPer1g = extractNumber(v.carbohydrates) / extractNumber(v.weight)
            proteinPer1g = extractNumber(v.protein) / extractNumber(v.weight)
            fatPer1g = extractNumber(v.fat) / extractNumber(v.weight)
            dietaryFiberPer1g = extractNumber(v.dietaryFiber) / extractNumber(v.weight)
            renderRule()
        }
    },
    {
        immediate: true,
    },
)

watch(
    () => modelValue.value!.weight,
    (v) => {
        modelValue.value!.calories = Math.round(caloriePer1g * v)
        modelValue.value!.carbohydrates = Math.round(carbohydratesPer1g * v)
        modelValue.value!.protein = Math.round(proteinPer1g * v)
        modelValue.value!.fat = Math.round(fatPer1g * v)
        modelValue.value!.dietaryFiber = Math.round(dietaryFiberPer1g * v)
    },
)

const weightRulerRef = useTemplateRef('weightRulerRef')

let slider: any

const lastVibrationTime = ref(0)
const VIBRATION_THRESHOLD = 100

async function renderRule() {
    await nextTick()
    slider?.destroy()
    slider = new SlideRuler({
        fontSize: 13,
        heightDecimal: 28,
        heightDigit: 18,
        fontMarginTop: 45,
        el: weightRulerRef.value,
        currentValue: modelValue.value!.weight,
        maxValue: 10000,
        minValue: 1,
        precision: 1,
        divide: 5,
        handleValue: (v: number) => {
            modelValue.value!.weight = v

            if (navigator?.vibrate) {
                const now = Date.now()
                if (now - lastVibrationTime.value > VIBRATION_THRESHOLD) {
                    navigator?.vibrate([10, 10])
                    lastVibrationTime.value = now
                }
            }
        },
    })
}

defineExpose({
    renderRule,
})

// if (import.meta.hot) {
//     renderRule()
// }
</script>

<template>
    <div class="relative p-20px">
        <div text-center left-15px mb-10px>
            <div class="text-t-4 text-14px">
                {{ modelValue?.name }}
            </div>
            <div text="t-5 17px" font-600>
                {{ modelValue!.weight }}g/{{ modelValue!.calories }}千卡
            </div>
        </div>

        <div ref="weightRulerRef"></div>
    </div>
</template>
