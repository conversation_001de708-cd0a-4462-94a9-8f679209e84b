<script setup lang="ts">
import dayjs from 'dayjs'

const {
    checkInDate = dayjs().format('YYYY-MM-DD'),
    waist = undefined,
} = defineProps<{
    checkInDate?: string
    waist?: number
}>()

const emit = defineEmits<{
    (e: 'success', newVal?: number): void
}>()

const showAddActionSheet = defineModel({
    default: false,
})

const waistValue = ref()

watch(() => waist, (val) => {
    waistValue.value = val || 60
}, { immediate: true })

async function handleSave() {
    try {
        await useWrapFetch('/checkInCustomerWaistCircumference/save', {
            method: 'post',
            body: {
                waistCircumference: waistValue.value,
                checkInDate,
            },
        })

        await useWrapFetch('/user/preliminaryArchive', {
            method: 'post',
            body: {
                waist: waistValue.value,
            },
        })

        emit('success', waistValue.value)
    } catch {
        showFailToast('保存失败')
    }
}

const sliderRuleRef = useTemplateRef('sliderRuleRef')

whenever(showAddActionSheet, async () => {
    await nextTick()
    sliderRuleRef.value?.renderRule()
})
</script>

<template>
    <van-action-sheet class="linear-gradient-content" :show="showAddActionSheet" @close="showAddActionSheet = false">
        <div>
            <div class="flex justify-between items-center p-16px">
                <div class="w-14px"></div>
                <div class="text-center font-600 text-16px text-t-5">腰围打卡</div>
                <div class="text-right text-16px text-t-5 i-radix-icons:cross-2 w-16px h-16px"
                    @click="showAddActionSheet = false"></div>
            </div>

            <user-checkin-slider-rule ref="sliderRuleRef" v-model="waistValue" dialog-title="设置腰围" unit="cm"
                :rule-props="{
                    maxValue: 200,
                    minValue: 40,
                    divide: 5,
                    precision: 0.1,
                }" @save="handleSave" />
        </div>
    </van-action-sheet>
</template>
