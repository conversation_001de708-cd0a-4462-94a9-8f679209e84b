<script setup lang="ts">
import A from '@/assets/images/report.png'
import B from '@/assets/images/plan.svg'

const { title, data, type } = defineProps<{
    title: string
    data: any
    type: 'report' | 'plan'
}>()

const dayjs = useDayjs()
</script>

<template>
    <div
        style="box-shadow: 0px 4px 12px 0px rgba(2, 78, 68, 0.08)"
        class="px-13px py-16px rd-10px flex gap-8px relative"
    >
        <div v-if="data?.readStatus === 0" class="custom-badge-list"></div>
        <van-image class="w-80px h-80px flex-shrink-0" :src="type === 'report' ? A : B" alt="" srcset="" />

        <div flex flex-col justify-between w-full>
            <div class="text-15px font-600">
                {{ title }}
            </div>

            <div flex justify-between w-full>
                <div class="text-12px text-t-3">
                    {{ dayjs(data.createTime).format('YYYY-MM-DD HH:mm:ss') }}
                </div>

                <van-tag v-if="type === 'plan'" :type="data.payStatus === 'SUCCESS' ? 'success' : 'warning'">
                    {{ data.payStatus === 'SUCCESS' ? '已支付' : '未支付' }}
                </van-tag>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.custom-badge-list {
    position: absolute;
    top: 50%;
    left: -8px;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background-color: #00AC97;
    border-radius: 50%;
}
</style>
