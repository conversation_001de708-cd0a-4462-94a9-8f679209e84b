<script setup lang="ts">
const emit = defineEmits(['refresh'])

const reportDetail = defineModel<ReportInterpretation>('reportDetail')
</script>

<template>
    <div p-16px class="h-[calc(100vh-48px)]">
        <div bg-white p-16px rd-4px h-full flex="~ col" justify-between>
            <div v-if="reportDetail?.content && reportDetail.status !== 'init'">
                <!-- {{ reportDetail.content }} -->

                <van-image src="/foo.png" />

                <p text-gray-500>
                    ①高血压：高血压可能存在多年却毫无症状。即使没有症状，对血管和心脏的损害却一直在持续，但这种损害可被发现。未经控制的高血压很可能引发严重健康问题，包括心脏病发作和卒中。建议低脂低盐饮食，适度运动，连续3天同一时间测量血压，如仍高请心内科就诊。
                </p>
            </div>

            <template v-else>
                <van-empty description="等待医生解读报告" />

                <div flex gap-10px>
                    <van-button type="primary" class="!h-44px" round block>智能生成报告</van-button>
                    <van-button type="primary" class="!h-44px" round block @click="emit('refresh')">获取最新报告</van-button>
                </div>
            </template>
        </div>
    </div>
</template>
