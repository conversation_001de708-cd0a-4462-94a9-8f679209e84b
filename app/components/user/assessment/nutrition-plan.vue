<script setup lang="ts">
const { reportDetail } = defineProps<{
    reportDetail: ReportInterpretation
}>()

const { data, status, refresh } = useAPI<NutritionProgram>(`/nutrition-program/getByQuestionResultId/${reportDetail.questionResultId}`)

const orderStatus = ref<OrderStatus>()

watch(data, async (val) => {
    if (val?.results?.nutritionProgramId) {
        const { results } = await useWrapFetch<BaseResponse<OrderStatus>>(`/pay/${val.results.nutritionProgramId}`)

        if (results) {
            orderStatus.value = results
        }
    }
})
</script>

<template>
    <base-suspense :status>
        <div v-if="data?.results?.status === 'init' || !data?.results?.status" p-16px class="h-[calc(100vh-48px)]">
            <div flex="~ col" bg-white h-full items-center justify-between p-16px>
                <van-empty description="等待医生生成健康方案" />
                <div flex gap-16px w-full>
                    <van-button type="primary" round class="!h-44px" block>智能生成健康方案</van-button>
                    <van-button type="primary" round class="!h-44px" block @click="refresh">获取最新健康方案</van-button>
                </div>
            </div>
        </div>

        <template v-else>
            <div pt-16px px-16px class="h-[calc(100vh-100px)]">
                <user-assessment-nutrition-detail :data="data!.results" />
            </div>

            <div bg-white py-10px px-16px flex justify-between items-center>
                <div text-primary-6 text-24px font-500>
                    {{ formatPrice(data!.results.price) }}
                </div>

                <nuxt-link v-if="!orderStatus" :to="`/user/confirm-order?nutritionProgramId=${data?.results.nutritionProgramId}`">
                    <van-button class="!h-36px" type="primary" round>
                        立即下单
                    </van-button>
                </nuxt-link>

                <van-button v-else class="!h-36px" round>
                    已下单
                </van-button>
            </div>
        </template>
    </base-suspense>
</template>
