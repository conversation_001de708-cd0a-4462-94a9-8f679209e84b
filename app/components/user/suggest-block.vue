<script setup lang="ts">
const { content, title } = defineProps<{
    content: string
    title: string
}>()

const showAll = ref(false)

const contentRef = useTemplateRef('contentRef')

const isShowTogglebtn = computed(() => {
    if (contentRef.value) {
        return contentRef.value?.scrollHeight >= 90
    }
    return false
})
</script>

<template>
    <div flex flex-col>
        <div
            v-if="title" rd-10px px-16px w-90px h-48px text-14px text-white text-center py-4px
            leading-25px
            style="background: linear-gradient(90deg, #00AC97 0%, #069684 100%);background: linear-gradient(90deg, #B3EFE8 0%, #51C9BA 100%);"
        >
            {{ title }}
        </div>

        <div class="border-gradient">
            <div p-16px relative>
                <div mt-8px>
                    <p ref="contentRef" :class="!showAll ? 'max-h-70px line-clamp-3' : 'max-h-1000px'" transition-all duration-300 text-t-4 tracking-0.04em leading-25px overflow-y-hidden break-all v-html="content">
                    </p>
                    <div v-if="isShowTogglebtn" mt-8px flex items-center justify-center gap-4px @click="showAll = !showAll">
                        <div text="12px primary-6">
                            {{ showAll ? '收起' : '展开' }}
                        </div>

                        <div v-show="!showAll" class="i-radix-icons:chevron-down w-14px h-14px text-primary-6"></div>
                        <div v-show="showAll" class="i-radix-icons:chevron-up w-14px h-14px text-primary-6"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
