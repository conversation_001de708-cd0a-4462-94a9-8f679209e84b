<script setup lang="ts">
const modelValue = defineModel<'male' | 'female'>('modelValue', { default: 'male' })
</script>

<template>
    <div class="flex items-center gap-12px">
        <img
            src="@/assets/images/common/male-2.png" class="w-64px h-64px" :class="{ grayscale: modelValue === 'female' }"
            alt="" srcset="" @click="modelValue = 'male'"
        />
        <img
            src="@/assets/images/common/female-2.png" class="w-64px h-64px" :class="{ grayscale: modelValue === 'male' }"
            alt="" srcset="" @click="modelValue = 'female'"
        />
    </div>
</template>
