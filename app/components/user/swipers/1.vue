<script setup lang="ts">
import { getSurveyMeta } from '@/utils/survey/getMeta'

defineProps<{
    survey: QuestionList
}>()
</script>

<template>
    <div style="background: linear-gradient(180deg, #DDFFFB 0%, #F4F5F7 100%);" class="h-300px p-16px">
        <img src="@/assets/images/background/arrow-top.svg" class="absolute w-84px h-84px top-28px left-133px" alt="" srcset="" />

        <img absolute bottom-0 top-39px right-16px src="@/assets/images/background/doctor.png" w-146px h-225px alt="" srcset="" />

        <div class="pl-16px pt-26px">
            <div class="text-24px font-600" :class="Number(survey.questionId) === EXTERNAL_PROCESS_ID ? 'w-110px' : 'w-180px'">
                {{ getSurveyMeta(survey.content).title }}
            </div>
        </div>

        <div class="bg-white mt-4px rd-20px py-12px px-16px h-146px flex flex-col justify-between" style="box-shadow: 0px 4px 12px 0px rgba(2, 78, 68, 0.08);">
            <span class="w-180px text-t-3 text-12px">
                {{ getSurveyMeta(survey.content).description }}
            </span>

            <div v-if="survey.reportInterpretationId">
                <nuxt-link :to="`/user/survey/consent?surveyId=${survey.questionId}&interpretationId=${survey.reportInterpretationId}&resultId=${survey.questionResultId}&type=PRELIMINARY_EVALUATION`">
                    <van-button round type="primary" class="w-94px! h-36px!">
                        重新评估
                    </van-button>
                </nuxt-link>

                <nuxt-link v-if="Number(survey.questionId) !== EXTERNAL_PROCESS_ID" :to="`/user/assessment/${survey.reportInterpretationId}/evaluate`">
                    <van-button round plain type="primary" class="w-94px! ml-8px! h-36px!">
                        评估结果
                    </van-button>
                </nuxt-link>
            </div>

            <div v-else>
                <nuxt-link :to="`/user/survey/consent?surveyId=${survey.questionId}&interpretationId=${survey.reportInterpretationId}&resultId=${survey.questionResultId}&type=PRELIMINARY_EVALUATION`">
                    <van-button round type="primary" class="w-94px! h-36px!">
                        开始评估
                    </van-button>
                </nuxt-link>
            </div>
        </div>
    </div>
</template>
