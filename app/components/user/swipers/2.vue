<script setup lang="ts">
import { getSurveyMeta } from '@/utils/survey/getMeta'

defineProps<{
    survey: QuestionList
}>()
</script>

<template>
    <div pl-32px pt-40px relative class="dynamic-height">
        <div font-700 text-36px class="dinpro-font" text="t-5">
            <span block leading-43px>Fatty Liver</span>
            <span block leading-43px>Assessment</span>
            <span block leading-43px>Questionnaire</span>
        </div>

        <div text-24px font-500 mt-4px text="#1D2229">
            {{ getSurveyMeta(survey.content).title }}
        </div>

        <div h-5px w-40px my-16px rd-20px bg="primary-6"></div>

        <div text="12px t-4">
            {{ getSurveyMeta(survey.content).completionTime }}分钟{{ getSurveyMeta(survey.content).questionCount }}道题，评估肝脏状态
        </div>

        <img src="@/assets/images/common/bottle-1.png" alt="" srcset="" class="absolute top-120px right-0" />

        <img src="@/assets/images/common/bottle-2.png" alt="" srcset="" class="absolute top-300px right-0" />

        <img src="@/assets/images/common/pill.png" alt="" srcset="" class="absolute top-316px left-75px" />
    </div>
</template>
