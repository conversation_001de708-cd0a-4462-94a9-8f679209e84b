<script setup lang="ts">
const { type = 'finished', label } = defineProps<{
    type: 'finished' | 'unfinished'
    label: string
}>()

const classes = computed(() => {
    return type === 'finished' ? 'bg-primary-1 text-primary-6' : 'bg-warning-1 text-warning-6'
})
</script>

<template>
    <div :class="classes" flex items-center w-64px h-20px text-10px pl-4px leading-20px>
        <div mr-3px>
            {{ label }}
        </div>

        <span w-10px h-10px :class="type === 'unfinished' ? 'i-custom-clock' : 'i-custom-finished-3'"></span>
    </div>
</template>
