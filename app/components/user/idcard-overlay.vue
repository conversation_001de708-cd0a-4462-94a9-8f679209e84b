<script setup lang="ts">
const emit = defineEmits(['confirm'])
const isOverlayShow = defineModel('modelValue', { type: Boolean, default: true })
const idCard = defineModel('idCard', { type: String, default: '' })
</script>

<template>
    <van-overlay :show="isOverlayShow">
        <div flex="~ col" w-full h-full gap-16px items-center justify-center>
            <div flex="~ col" items-center relative>
                <div class="background-survey-invite"></div>
                <div
                    class="w-303px h-249px rd-16px -translate-y-40px flex flex-col items-center pt-62px"
                    style="background: linear-gradient(180deg, #D8EEFF 0%, #FFFFFF 70.14%);"
                >
                    <div justify-between flex items-center>
                        <div text="primary-6 18px" font-500 class="tracking-0.1em">
                            请完善身份证号
                        </div>
                    </div>

                    <div w-full px-21px>
                        <input v-model="idCard" class="custom-input" placeholder="请输入身份证号" />
                    </div>

                    <van-button type="primary" class="w-145px! h-50px! mt-24px! text-16px!" round size="large" @click="emit('confirm')">
                        确认
                    </van-button>
                </div>

                <div class="i-custom:close absolute h-32px w-32px z-1000 top-330px left-136px" @click="isOverlayShow = false"></div>
            </div>
        </div>
    </van-overlay>
</template>

<style lang="scss" scoped>
.background-survey-invite {
    background: url('@/assets/images/background/sign.png') no-repeat center/cover;
    --uno: w-111px h-92px relative z-1000;
}

.custom-input {
  border: 1px solid rgba(78, 89, 105, 1);
  --uno: bg-transparent text-16px rd-56px h-48px w-full indent-16px mt-16px;
}

.custom-input::placeholder {
  --uno: text-t-3;
  font-size: 16px;
}
</style>
