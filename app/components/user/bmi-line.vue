<script setup lang="ts">
const { onlyShowLine = false, rootBmi } = defineProps<{ onlyShowLine?: boolean, rootBmi?: string }>()

const offsetLeft = computed(() => {
    const _bmi = Number.parseFloat(rootBmi || '0')
    let percentage

    if (_bmi && _bmi < 18.5) {
        percentage = (_bmi / 18.5) * 25
    } else if (_bmi && _bmi < 24) {
        percentage = 25 + ((_bmi - 18.5) / 5.5) * 25
    } else if (_bmi && _bmi < 28) {
        percentage = 50 + ((_bmi - 24) / 4) * 25
    } else if (_bmi) {
        percentage = 75 + Math.min((_bmi - 28) / 7, 1) * 25
    }

    return `${percentage}%`
})
</script>

<template>
    <div>
        <div>
            <div v-if="!onlyShowLine" flex items-center gap-8px mt-16px mb-16px>
                <div class="i-custom-three-bar h-16px w-16px"></div>
                <div text="14px t-5" font-600>
                    BMI数据
                </div>
            </div>

            <div v-if="parseFloat(rootBmi?.toString() || '0') > 0" class="pop-bg" :style="{ transform: 'translateX(-50%)', left: offsetLeft }">
                <span class="pop-text">
                    {{ parseFloat(rootBmi?.toString() || '0').toFixed(1) }}
                </span>
            </div>

            <div class="sd-rainbow-line">
            </div>
        </div>

        <div class="sd-rainbow-line-label text-t-4">
            <span>偏低</span>
            <span>正常</span>
            <span>偏高</span>
        </div>
    </div>
</template>

<style scoped>
.sd-rainbow-line {
  background: linear-gradient(90deg, #3694EC 0%, #07D4B6 49.5%, #FEA602 100%);
  height: 10px;
  border-radius: 8px;
}

.sd-rainbow-line-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 2px;
  margin-bottom: 8px;
  color: '#4E5969';
  font-size: 10px;
}

.pop-bg {
  background-image: url('@/assets/images/background/pop.svg');
  width: 43px;
  height: 26px;
  display: flex;
  justify-content: center;
  margin-bottom: 8px;
  background-size: cover;
  position: relative;
}

.pop-text {
  font-size: 12px;
  color: #fff;
  position: relative;
  top: 2px;
}
</style>
