<script setup lang="ts">
const { type } = defineProps<{
    type: string
}>()

const inspectionMeta: Record<string, { icon: string, title: string, description: string }> = {
    bmi: {
        icon: 'i-custom-inspection-bmi',
        title: 'BMI指数',
        description: '正常 18.5~23.9',
    },
    acid: {
        icon: 'i-custom-inspection-acid',
        title: '尿酸指数',
        description: '正常≤420μmol/L',
    },
    bloodFat: {
        icon: 'i-custom-inspection-blood-fat',
        title: '血脂指数',
        description: 'TC正常<5.2mmol/L',
    },
    bloodPressure: {
        icon: 'i-custom-inspection-blood-pressure',
        title: '血压指数',
        description: '收缩压90~139mmHg<br/>舒张压60~89mmHg',
    },
    artery: {
        icon: 'i-custom-inspection-artery',
        title: '动脉硬化指数',
        description: '正常 ＜4',
    },
    bloodSugar: {
        icon: 'i-custom-inspection-blood-sugar',
        title: '空腹血糖',
        description: '正常4.4~6.1mmol/L',
    },
    bloodAcid: {
        icon: 'i-custom-inspection-blood-acid',
        title: '血尿酸',
        description: '成人180~420mmol/L',
    },
    cap: {
        icon: 'i-custom-inspection-cap',
        title: '肝弹性检测',
        description: '(CAP及LSM)',
    },
}
</script>

<template>
    <div class="result-card">
        <div flex items-start gap-8px>
            <div :class="inspectionMeta[type]!.icon" h-30px w-30px></div>
            <div>
                <div flex gap-4px>
                    <div text="12px t-4" font-600>
                        {{ inspectionMeta[type]!.title }}
                    </div>

                    <slot name="tag"></slot>
                </div>

                <div text="10px #C2C9D6" v-html="inspectionMeta[type]!.description">
                </div>
            </div>
        </div>

        <slot name="result">
            <div flex flex-col items-center justify-center gap-4px>
                <div class="i-custom-inspection-empty" w-24px h-24px></div>
                <div text="10px t-3">
                    检查结果还未出哦~
                </div>
            </div>
        </slot>
    </div>
</template>

<style scoped>
.result-card {
    background: linear-gradient(131.89deg, rgba(194, 201, 214, 0.1) 8.47%, rgba(194, 201, 214, 0.05) 88.34%);
    --uno: w-165px h-130px rd-15px p-13px flex flex-col justify-between;
    box-sizing: border-box;
}
</style>
