<script setup lang="ts">
defineProps<{
    bmiResult: {
        value: string
        tag: string
        offset: string
    }
}>()
</script>

<template>
    <div mb-12px>
        <div font-500 font-dinpro text="#333 24px right">
            {{ bmiResult.value }}
        </div>
        <div w-140px rd-8px bg="#F0F1F5" h-4px relative>
            <div
                absolute top-0 left-0 h-4px rd-8px
                :style="{
                    background: `linear-gradient(270deg, #F98804 0%, #00AC97 ${bmiResult.offset}%, #007777 100%)`,
                    width: `${bmiResult.offset}%`,
                }"
            >
            </div>
        </div>
    </div>
</template>
