<script setup lang="ts">
const emit = defineEmits(['confirm'])
const isOverlayShow = defineModel('modelValue', { type: Boolean, default: true })
const idCard = defineModel('idCard', { type: String, default: '' })

function handleCheckPlan() {
    isOverlayShow.value = false
    navigateTo('/user/archives/nutrition-plan')
}
</script>

<template>
    <van-overlay :show="isOverlayShow">
        <div flex="~ col" w-full h-full gap-16px items-center justify-center>
            <div flex="~ col" items-center relative>
                <div
                    class="w-303px h-313px rd-16px flex flex-col items-center p-24px"
                    style="background: linear-gradient(180deg, #D8EEFF 0%, #FFFFFF 70.14%);"
                >
                    <div justify-between flex items-center gap-4px>
                        <div class="i-custom:leaf-left w-12px h-12px"></div>
                        <div text="primary-6 18px" font-500 class="tracking-0.1em">
                            营养干预方案
                        </div>
                        <div class="i-custom:leaf-right w-12px h-12px"></div>
                    </div>

                    <div text="14px t-5">
                        根据您的检查检验结果，医生已开具针对性营养干预方案
                    </div>

                    <img mt-8px src="@/assets/images/assessment/plan-pos.svg" alt="" srcset="" />

                    <van-button type="primary" class="h-50px! mt-24px! text-16px!" round size="large" @click="handleCheckPlan">
                        立即查看
                    </van-button>

                    <div text="t-3 14px" mt-16px @click="isOverlayShow = false">
                        稍后再看
                    </div>
                </div>

                <!-- <div class="i-custom:close absolute h-32px w-32px z-1000 top-330px left-136px" @click="isOverlayShow = false"></div> -->
            </div>
        </div>
    </van-overlay>
</template>

<style lang="scss" scoped>
.background-survey-invite {
    background: url('@/assets/images/background/sign.png') no-repeat center/cover;
    --uno: w-111px h-92px relative z-1000;
}

.custom-input {
  border: 1px solid rgba(78, 89, 105, 1);
  --uno: bg-transparent text-16px rd-56px h-48px w-full indent-16px mt-16px;
}

.custom-input::placeholder {
  --uno: text-t-3;
  font-size: 16px;
}
</style>
