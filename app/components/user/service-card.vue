<script setup lang="ts">
const { data } = defineProps<{
    data: QuestionList & { backgroundColor?: string }
}>()

const backgroundColor = computed(() => {
    switch (data.questionId) {
        case EXTERNAL_PROCESS_ID:
            return 'rgba(0, 172, 151, 0.56)'
        default:
            return 'rgba(8, 121, 202, 0.56)'
    }
})
</script>

<template>
    <div class="w-full h-170px rd-10px relative" style="box-shadow: 0px 4px 12px 0px rgba(2, 78, 68, 0.08);">
        <div
            :style="{ backgroundImage: `url(${data.coverPicture})` }"
            class="rd-10px h-170px w-343px bg-cover bg-center absolute inset-0"
        >
        </div>

        <div class="absolute inset-0 rd-10px" :style="{ background: backgroundColor }"></div>

        <div absolute top-20px px-24px>
            <div text="20px #fff" font-600>
                {{ data.title }}
            </div>

            <div
                class="rd-24px mt-10px w-76px h-26px text-t-4 bg-white text-10px text-center leading-26px"
            >
                {{ data.questionId ? '99+人参与' : '问卷未开启' }}
            </div>
        </div>
        <div bg-white absolute bottom-0 w-full h-48px gap-8px rd-b-10px flex justify-end items-center px-16px>
            <nuxt-link v-if="data.reportInterpretationId" :to="`/user/assessment/${data.reportInterpretationId}/records`">
                <van-button :disabled="!data.questionId" class="!w-82px !h-30px !border-none !text-primary-6 !bg-#E4FAF9" type="primary" size="small" round>填写记录</van-button>
            </nuxt-link>

            <nuxt-link v-if="data.reportInterpretationId && data.questionId !== EXTERNAL_PROCESS_ID" :to="`/user/assessment/${data.reportInterpretationId}/evaluate`">
                <van-button :disabled="!data.questionId" class="!w-82px !h-30px !border-none !text-primary-6 !bg-#E4FAF9" type="primary" size="small" round>评估结果</van-button>
            </nuxt-link>

            <nuxt-link v-if="data.reportInterpretationId" :to="`/user/survey/consent?surveyId=${data.questionId}&interpretationId=${data.reportInterpretationId}&resultId=${data.questionResultId}&type=PRELIMINARY_EVALUATION`">
                <van-button :disabled="!data.questionId" class="!w-82px !h-30px !border-none !text-primary-6 !bg-#E4FAF9" type="primary" size="small" round>重新评估</van-button>
            </nuxt-link>

            <nuxt-link v-else :to="`/user/survey/consent?surveyId=${data.questionId}&interpretationId=${data.reportInterpretationId}&resultId=${data.questionResultId}&type=PRELIMINARY_EVALUATION`">
                <van-button class="!w-82px !h-30px" type="primary" size="small" round>开始评估</van-button>
            </nuxt-link>
        </div>
    </div>
</template>
