<script setup lang="ts">
const { path, currentPath } = defineProps<{
    icons: string[]
    label: string
    path: string
    currentPath: string
}>()

const badgeStore = useBadgeStore()
const badgeInfo = computed(() => badgeStore.badges[path] || { show: false })

const isActive = computed(() => {
    return path === currentPath
})
</script>

<template>
    <nuxt-link :to="path" replace w-75px h-50px flex flex-col items-center gap-2px justify-center>
        <div class="flex flex-col items-center justify-center relative">
            <div v-if="badgeInfo.show" class="custom-badge-tabbar"></div>
            <div class="w-26px h-26px" :class="isActive ? `${icons[1]} text-primary-6` : `${icons[0]} text-t-4`"></div>
            <div text-10px font-500 :class="isActive ? 'text-primary-6' : 'text-t-4'">
                {{ label }}
            </div>
        </div>
    </nuxt-link>
</template>

<style lang="scss" scoped>
.custom-badge-tabbar {
    position: absolute;
    top: 2px;
    left: -4px;
    width: 8px;
    height: 8px;
    background-color: #00AC97;
    border-radius: 50%;
}
</style>
