<script setup lang="ts">
const route = useRoute()
</script>

<template>
    <div class="safe-area fixed left-0 right-0">
        <div class="items-center justify-between flex">
            <user-tabbar-item :current-path="route.path" path="/user/checkin" :icons="['i-custom-tabbar-checkin', 'i-custom-tabbar-checkin-fill']" label="记录" />

            <user-tabbar-item :current-path="route.path" path="/user/tool" :icons="['i-custom-tabbar-knowledge', 'i-custom-tabbar-knowledge-fill']" label="工具" />

            <user-tabbar-cross-btn />

            <user-tabbar-item :current-path="route.path" path="/user/service" :icons="['i-custom-tabbar-service', 'i-custom-tabbar-service-fill']" label="服务" />

            <!-- <user-tabbar-item v-if="noPay" :current-path="route.path" path="/user/knowledge" :icons="['i-custom-tabbar-knowledge', 'i-custom-tabbar-knowledge-fill']" label="知识" /> -->

            <!-- <user-tabbar-item v-else :current-path="route.path" path="/user/handbook" :icons="['i-custom-tabbar-knowledge', 'i-custom-tabbar-knowledge-fill']" label="宝典" /> -->

            <user-tabbar-item :current-path="route.path" path="/user/archives" :icons="['i-custom-tabbar-archives', 'i-custom-tabbar-archives-fill']" label="我的" />
        </div>

        <div class="safe-area-block"></div>
    </div>
</template>

<style lang="scss" scoped>
.safe-area {
   height: 56px;
   bottom: calc(constant(safe-area-inset-bottom));
   bottom: calc(env(safe-area-inset-bottom));
   box-shadow: 0px 2px 8px 0px #0000001A;
   background: url('@/assets/images/tabbar/background.svg') no-repeat center center;
   background-size: 100% 100%;
}

.safe-area-block {
    height: calc(constant(safe-area-inset-bottom) + 4px);
    height: calc(env(safe-area-inset-bottom) + 4px);
    background: white;
}
</style>
