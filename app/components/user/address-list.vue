<script setup lang="ts">
const checkedAddressId = defineModel<string>()

const { data, refresh } = useAPI<Address[]>('/v1/address/list')

whenever(data, () => {
    if (!sessionStorage.getItem('checkedAddressId') && data.value!.results.length) {
        checkedAddressId.value = String(data.value!.results[0]!.id)
    }
})

const currentAddress = computed(() => {
    return data.value?.results.find(addr => addr.id === Number(checkedAddressId.value))
})

defineExpose({
    currentAddress,
})

function handleDelete(id: number) {
    showConfirmDialog({
        title: '确认删除',
        message: '确定要删除该地址吗？',
    })
        .then(async () => {
            showLoadingToast({
                message: '删除中...',
                forbidClick: true,
            })
            await useAPI(`/v1/address/${id}`, {
                method: 'DELETE',
            })
            closeToast()
            showSuccessToast('删除成功')
        })
}

function handleUpdateDefault(id: number) {
    showConfirmDialog({
        title: '设为默认',
        message: '确定要设置该地址为默认地址吗？',
    })
        .then(async () => {
            showLoadingToast({
                message: '修改中...',
                forbidClick: true,
            })
            await useAPI(`/v1/address/default/${id}`, { method: 'PUT' })
            closeToast()
            showSuccessToast('修改成功')
            await refresh()
            checkedAddressId.value = String(data.value!.results[0]!.id)
        })
}

const router = useRouter()

function handleEdit(id: number) {
    const item = data.value?.results.find(addr => addr.id === id)
    router.push({
        path: '/user/address/new',
        query: {
            ...item,
        },
    })
}
</script>

<template>
    <div v-if="data?.results.length" space-y-16px>
        <div v-for="item in data?.results" :key="item.id" bg-white rd-4px>
            <div flex items-center @click="checkedAddressId = String(item.id)">
                <div>
                    <span :class="checkedAddressId === String(item.id) ? 'i-custom-check' : ''" mr-8px w-20px h-20px inline-block text-primary-6></span>
                </div>
                <div w-full py-16px>
                    <p flex items-center gap-8px>
                        <span text="16px t-5" font-500>{{ item.name }}</span>
                        <span text="16px t-5" font-500>{{ item.phone }}</span>

                        <span v-if="item.defaultFlag" bg="primary-1" text="12px primary-6" px-4px py-1px rd-2px>
                            默认
                        </span>
                    </p>

                    <p>
                        <span text="13px t-4">
                            {{ `${item.province + item.city + item.district} ${item.detail}` }}
                        </span>
                    </p>

                    <div mt-16px flex justify-between>
                        <van-checkbox
                            :model-value="!!item.defaultFlag"
                            checked:
                            @update:model-value="!item.defaultFlag && handleUpdateDefault(item.id)"
                            @click.stop
                        >
                            <span>设为默认</span>
                        </van-checkbox>

                        <div flex gap-12px>
                            <div flex items-center @click.stop>
                                <div class="i-custom-del w-10px h-10px mr-4px"></div>
                                <span text="12px t-4" @click="handleDelete(item.id)">
                                    删除
                                </span>
                            </div>

                            <div flex items-center @click.stop>
                                <div class="i-custom-pen w-10px h-10px mr-4px"></div>
                                <span text="12px t-4" @click="handleEdit(item.id)">
                                    编辑
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <van-divider style="padding: 0px;" />
        </div>
    </div>

    <van-empty v-else description="暂无地址" />
</template>
