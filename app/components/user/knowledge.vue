<script setup lang="ts">
const { data } = useAPI<any>('/education/list', {
    method: 'post',
    body: {
        start: 1,
        size: 100,
    },
})

const keyWord = ref('')

const filteredLists = computed(() => {
    return data.value?.results.records.filter((item: any) => {
        return item.title?.includes(keyWord.value)
    })
})

function handleClick(url: string) {
    showLoadingToast({
        message: '加载中...',
    })
    navigateTo(url, { external: true })
}
</script>

<template>
    <div>
        <div style="background: linear-gradient(180deg, #DDF2FF 0%, #F4F5F7 100%);" class="h-277px absolute p-16px top-0 left-0 w-full">
            <div z-100>
                <base-search v-model="keyWord" placeholder="输入文章标题检索" />

                <div v-if="filteredLists?.length > 0" class="flex mt-16px flex-col gap-y-16px">
                    <div v-for="item in filteredLists" :key="item.id" @click="handleClick(item.articleUrl)">
                        <div bg-white p-16px rd-10px>
                            <p text-16px font-500>
                                {{ item.title }}
                            </p>

                            <div flex justify-between gap-10px mt-10px>
                                <div flex="~ col" items-start justify-between>
                                    <span text="12px t-3" line-clamp-2>
                                        {{ item.introduction }}
                                    </span>

                                    <!-- <div px-4px rd-2px bg="warning-1" text="12px warning-6">健康知识</div> -->
                                </div>

                                <van-image flex-shrink-0 w-70px h-70px rd-4px overflow-hidden fit="cover" :src="item.coverPicture" />
                            </div>
                        </div>
                    </div>
                </div>

                <van-empty v-else description="暂无文章" />
            </div>

            <shared-safe-buttom />
        </div>
    </div>
</template>
