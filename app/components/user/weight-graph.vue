<script setup lang="ts">
const { bmi = 192, showDescription = true } = defineProps<{
    bmi: number
    showDescription?: boolean
}>()

const graphs = [
    {
        key: 'level-1',
        min: -Infinity,
        max: 18.5,
        iconWidth: '33px',
        icon: 'i-custom-weight-level-1',
        fillIcon: 'i-custom-weight-level-1-fill',
        text: '体重过轻',
        description: '在我国20~69岁人群中，超重率为34.26%，肥胖率为10.98%。在全国范围内，肥胖症患病率呈现出城市高于农村的趋势，而在东、中、西部地区，也呈现依次降低的趋势。',
    },
    {
        key: 'level-2',
        min: 18.5,
        max: 24,
        iconWidth: '34px',
        icon: 'i-custom-weight-level-2',
        fillIcon: 'i-custom-weight-level-2-fill',
        text: '体重正常',
        description: '正常体重意味着身体代谢等机能良好，能降低心血管疾病、糖尿病等慢性疾病风险，从长远看能延缓衰老，是健康生活的重要指标。所以，对于处于正常体重范围的我们来说，一定要继续保持合理饮食、适量运动、规律作息等健康的生活方式，让身体长期维持在良好状态，持续享受正常体重带来的健康益处。',
    },
    {
        key: 'level-3',
        min: 24,
        max: 28,
        iconWidth: '35px',
        icon: 'i-custom-weight-level-3',
        fillIcon: 'i-custom-weight-level-3-fill',
        text: '超重',
        description: '虽然这时的身体可能还没有出现明显的疾病症状，但超重已经增加了患多种慢性疾病的潜在风险，如心血管疾病、高血压、糖尿病等。如果长期处于超重状态，会使身体代谢负担逐渐加重，脂肪开始在血管等部位堆积，可能导致血脂异常等问题。',
    },
    {
        key: 'level-4',
        min: 28,
        max: 32.5,
        iconWidth: '40px',
        icon: 'i-custom-weight-level-4',
        fillIcon: 'i-custom-weight-level-4-fill',
        text: '轻度肥胖症',
        description: '轻度肥胖症会使我们身体的代谢功能受到较明显影响，血糖、血压、血脂等指标可能出现异常波动。肥胖还可能对关节造成一定压力，引发关节疼痛等问题，同时也会影响我们的呼吸系统，可能出现睡眠呼吸暂停等轻度症状，进而影响睡眠质量和白天的精神状态。',
    },
    {
        key: 'level-5',
        min: 32.5,
        max: 37.5,
        iconWidth: '45px',
        icon: 'i-custom-weight-level-5',
        fillIcon: 'i-custom-weight-level-5-fill',
        text: '中度肥胖症',
        description: '中度肥胖症会使身体患心血管疾病的风险显著增加，如冠心病、心肌梗死等。高血压、糖尿病等慢性疾病往往已经伴随出现，且病情可能很难通过单纯的生活方式调整得到控制。身体的内分泌系统、免疫系统也会受到较大影响，容易出现内分泌失调、免疫力下降等情况，增加感染疾病的概率。',
    },
    {
        key: 'level-6',
        min: 37.5,
        max: 50,
        iconWidth: '50px',
        icon: 'i-custom-weight-level-6',
        fillIcon: 'i-custom-weight-level-6-fill',
        text: '重度肥胖症',
        description: '重度肥胖症会对身体多个器官和系统造成严重损害，心肺功能会受到极大影响，可能出现心肺功能衰竭等严重疾病。还会引发严重的关节磨损，导致行动困难，甚至可能丧失部分生活自理能力。此外，重度肥胖还与多种癌症的发病风险增加相关，如结直肠癌和女性的子宫内膜癌、乳腺癌等。',
    },
    {
        key: 'level-7',
        min: 50,
        max: Infinity,
        iconWidth: '58px',
        icon: 'i-custom-weight-level-7',
        fillIcon: 'i-custom-weight-level-7-fill',
        text: '极重度肥胖症',
        description: '极重度肥胖症会使身体处于极度危险状态，随时可能因肥胖引发的各种并发症危及生命。多器官功能可能出现严重衰竭，如肾功能衰竭、呼吸衰竭等，需要依靠医疗设备维持生命。心理健康也会受到极大冲击，可能出现严重的抑郁、焦虑等心理问题，进一步影响生活质量和康复进程。',
    },
]

const graphRefs = ref<HTMLElement[]>([])

const activeGraph = computed(() => {
    const graphIndex = graphs.findIndex(i => i.min <= bmi && i.max >= bmi)
    const result = {
        graph: graphs[graphIndex],
    }

    nextTick(() => {
        graphRefs.value[graphIndex]?.scrollIntoView({
            behavior: 'smooth',
            block: 'nearest', // 改为 nearest，避免影响页面垂直滚动
            inline: 'center', // 保持水平居中
        })
    })

    return result
})
</script>

<template>
    <div flex gap-30px overflow-x-auto>
        <div v-for="i in graphs" :ref="el => graphRefs.push(el as HTMLElement)" :key="i.text" flex flex-col items-center gap-5px h-154px w-58px>
            <v-balancer class="text-10px text-t-4 text-center w-31px h-28px">
                {{ i.text }}
            </v-balancer>
            <div :class="activeGraph?.graph?.text === i.text ? activeGraph?.graph?.fillIcon : i.icon" class="h-92px" :style="{ width: i.iconWidth }"></div>
            <div flex flex-col justify-between h-10px items-center>
                <div text="10px" :class="activeGraph!.graph!.text === i.text ? 'text-t-5' : 'text-t-3'" style="white-space: nowrap;">
                    <template v-if="i.min === -Infinity">
                        <span>
                            &lt; {{ i.max }}
                        </span>
                    </template>
                    <template v-else-if="i.max === Infinity">
                        <span>
                            ≥ {{ i.min }}
                        </span>
                    </template>
                    <template v-else>
                        <span>
                            {{ i.min }}~{{ i.max }}
                        </span>
                    </template>
                </div>
            </div>
        </div>
    </div>

    <div v-if="showDescription" class="bg-fill-1 text-t-3 text-12px rd-10px p-8px mt-16px">
        {{ activeGraph?.graph?.description }}
    </div>
</template>

<style scoped>
.writing-vertical {
    writing-mode: vertical-rl;
    text-orientation: upright;
    letter-spacing: 4px;
    --uno: text-t-4 text-120x;
}
</style>
