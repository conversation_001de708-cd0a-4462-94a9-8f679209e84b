<script setup lang="ts">
import { areaList } from '@vant/area-data'

const props = defineProps<{ question: any }>()
const show = ref(false)

function handleConfirm(value: any) {
    show.value = false
    props.question.value = value.selectedOptions.map((item: any) => item.text).join('')
}
</script>

<template>
    <div class="w-full">
        <div class="sd-selectbase items-end!">
            <div class="sv-dropdown_select-wrapper" @click="show = true">
                <div class="sd-input sd-dropdown sd-dropdown--empty">
                    <div class="sd-dropdown__value">
                        <input type="text" class="sd-dropdown__filter-string-input" :value="props.question.value" placeholder="请选择籍贯" />
                    </div>
                </div>
                <div>
                    <div class="sd-dropdown_chevron-button" aria-hidden="true">
                        <svg class="sv-svg-icon sd-dropdown_chevron-button-svg" role="img">
                            <use xlink:href="#icon-chevron" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <van-popup v-model:show="show" position="bottom">
            <van-area :area-list="areaList" @confirm="handleConfirm" />
        </van-popup>
    </div>
</template>
