import { ElementFactory, Question, Serializer } from 'survey-core'

import AddonPainJoint from './index.vue'

const CUSTOM_TYPE = 'addon-pain-joint'

// Create a question model
export class QuestionPainJointModel extends Question {
    override getType() {
        return CUSTOM_TYPE
    }
}

ElementFactory.Instance.registerElement(CUSTOM_TYPE, (name) => {
    return new QuestionPainJointModel(name)
})

// Add question type metadata for further serialization into JSON
Serializer.addClass(
    CUSTOM_TYPE,
    [],
    () => {
        return new QuestionPainJointModel('')
    },
    'question',
)

useNuxtApp().vueApp.component('survey-addon-pain-joint', AddonPainJoint)
