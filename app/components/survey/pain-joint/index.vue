<script setup lang="ts">
import { jointMeta } from './meta'

const props = defineProps<{ question: any }>()

const selectedJoint = ref(props.question.value || [])

const jointList = ref([
    {
        id: '1',
        name: '肩关节',
        class: 'top-80px -left-50px',
    },
    {
        id: '2',
        name: '肘关节',
        class: 'top-140px -right-55px',
    },
    {
        id: '3',
        name: '髋关节',
        class: 'top-190px -left-55px',
    },
    {
        id: '4',
        name: '腕关节',
        class: 'top-200px -right-55px',
    },
    {
        id: '5',
        name: '膝关节',
        class: 'top-290px -left-40px',
    },
    {
        id: '6',
        name: '踝关节',
        class: 'top-400px -right-40px',
    },
])

function handleSelectJoint(id: string) {
    if (selectedJoint.value.includes(id)) {
        selectedJoint.value = selectedJoint.value.filter((item: string) => item !== id)
    }
    else {
        selectedJoint.value.push(id)
    }

    // 确保更新后的值同步到 question.value
    props.question.value = [...selectedJoint.value]
}
</script>

<template>
    <div flex justify-center relative items-center>
        <div class="full-body-bg">
            <div
                v-for="item in jointList"
                :key="item.id"
                absolute
                :class="[item.class, selectedJoint.includes(item.id) ? 'bg-[#00BEA7E8] text-white' : 'bg-[#9CEEE93D] text-t-5']"
                backdrop-blur-4px
                w-89px
                h-40px
                gap-4px
                text-12px
                rd-25px
                flex
                justify-center
                items-center @click="handleSelectJoint(item.id)"
            >
                <div w-16px h-16px :class="selectedJoint.includes(item.id) ? 'i-custom-checked' : 'i-custom-unchecked'"></div>
                <div>
                    {{ item.name }}
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.full-body-bg {
    background-image: url('@/assets/images/background/fullbody.png');
    background-size: 100% 100%;
    height: 458px;
    width: 153px;
}
</style>
