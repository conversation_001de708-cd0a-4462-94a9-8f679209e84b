<script setup lang="ts">
import SlideRuler from '@/utils/slide-rule'

const props = defineProps<{ question: any }>()

const heightRulerRef = useTemplateRef('heightRulerRef')

const rulerGeneralProps = {
    fontSize: 13,
    precision: 0.1,
    divide: 5,
    heightDecimal: 28,
    heightDigit: 18,
    fontMarginTop: 45,
}

const lastVibrationTime = ref(0)
const VIBRATION_THRESHOLD = 100

onMounted(() => {
    new SlideRuler({
        ...rulerGeneralProps,
        el: heightRulerRef.value,
        maxValue: 60,
        minValue: 1,
        currentValue: props.question.value || 20,
        handleValue: (v: number) => {
            props.question.value = v

            if (navigator?.vibrate) {
                const now = Date.now()
                if (now - lastVibrationTime.value > VIBRATION_THRESHOLD) {
                    navigator?.vibrate([10, 10])
                    lastVibrationTime.value = now
                }
            }
        },
    })

    if (!props.question.value) {
        props.question.value = 160
    }
})
</script>

<template>
    <div class="relative of-hidden w-327px h-123px">
        <div flex gap-5px items-end justify-center w-full relative>
            <span text="t-5 10px">
                体脂率
            </span>
            <span text="t-5 24px" leading-none font-600>
                {{ `${props.question.value}` }}
            </span>
            <span text="t-5 10px">
                %
            </span>
        </div>

        <div ref="heightRulerRef" class="absolute -left-40px mt-10px"></div>
    </div>
</template>
