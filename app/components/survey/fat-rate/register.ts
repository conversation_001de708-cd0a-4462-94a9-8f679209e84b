import { ElementFactory, Question, Serializer } from 'survey-core'

import AddonFatRate from './index.vue'

const CUSTOM_TYPE = 'addon-fat-rate'

// Create a question model
export class QuestionWaistModel extends Question {
    override getType() {
        return CUSTOM_TYPE
    }
}

ElementFactory.Instance.registerElement(CUSTOM_TYPE, (name) => {
    return new QuestionWaistModel(name)
})

// Add question type metadata for further serialization into JSON
Serializer.addClass(
    CUSTOM_TYPE,
    [],
    () => {
        return new QuestionWaistModel('')
    },
    'question',
)

useNuxtApp().vueApp.component('survey-addon-fat-rate', AddonFatRate)
