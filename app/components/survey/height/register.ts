import { ElementFactory, Question, Serializer } from 'survey-core'

import AddonHeight from './index.vue'

const CUSTOM_TYPE = 'addon-height'

// Create a question model
export class QuestionHeightModel extends Question {
    override getType() {
        return CUSTOM_TYPE
    }
}

ElementFactory.Instance.registerElement(CUSTOM_TYPE, (name) => {
    return new QuestionHeightModel(name)
})

// Add question type metadata for further serialization into JSON
Serializer.addClass(
    CUSTOM_TYPE,
    [],
    () => {
        return new QuestionHeightModel('')
    },
    'question',
)

useNuxtApp().vueApp.component('survey-addon-height', AddonHeight)
