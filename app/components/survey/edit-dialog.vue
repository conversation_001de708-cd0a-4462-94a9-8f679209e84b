<script setup lang="ts">
const props = defineProps<{
  title: string
  value: number
  unit: string
}>()

const emit = defineEmits<{
  (e: 'confirm', value: number): void
}>()

const show = defineModel<boolean>({
  default: false,
})

const dialogInputValue = ref(props.value)

watch(() => props.value, (newVal) => {
  dialogInputValue.value = newVal
})

function handleConfirm() {
  emit('confirm', dialogInputValue.value)
  show.value = false
}
</script>

<template>
  <van-dialog v-model:show="show" :title="title" show-cancel-button @confirm="handleConfirm">
    <div class="flex items-center gap-4px justify-center my-10px">
      <input v-model="dialogInputValue" type="number"
        class="px-20px py-6px font-500 border-2px border-#E5E7EB rd-10px" />
      <span class="text-12px text-#4E5969">{{ unit }}</span>
    </div>
  </van-dialog>
</template>
