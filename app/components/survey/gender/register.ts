import { ElementFactory, Question, Serializer } from 'survey-core'

import AddonGender from './index.vue'

const CUSTOM_TYPE = 'addon-gender'

// Create a question model
export class QuestionGenderModel extends Question {
    override getType() {
        return CUSTOM_TYPE
    }
}

ElementFactory.Instance.registerElement(CUSTOM_TYPE, (name) => {
    return new QuestionGenderModel(name)
})

// Add question type metadata for further serialization into JSON
Serializer.addClass(
    CUSTOM_TYPE,
    [],
    () => {
        return new QuestionGenderModel('')
    },
    'question',
)

useNuxtApp().vueApp.component('survey-addon-gender', AddonGender)
