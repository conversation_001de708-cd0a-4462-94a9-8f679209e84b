<script setup lang="ts">
const props = defineProps<{ question: any }>()
const show = ref(false)

function handleConfirm(value: any) {
    show.value = false
    props.question.value = value.selectedValues.join('-')
}

const minDate = computed(() => {
    return new Date(1900, 0, 1)
})

const maxDate = computed(() => {
    return new Date(new Date().getFullYear() + 1, 11, 31)
})
</script>

<template>
    <div class="w-full">
        <div class="sd-selectbase items-end!">
            <div class="sv-dropdown_select-wrapper" @click="show = true">
                <div class="sd-input sd-dropdown sd-dropdown--empty">
                    <div class="sd-dropdown__value">
                        <input type="text" class="sd-dropdown__filter-string-input" :value="props.question.value" placeholder="请选择出生年月" />
                    </div>
                </div>
                <div>
                    <div class="sd-dropdown_chevron-button" aria-hidden="true">
                        <svg class="sv-svg-icon sd-dropdown_chevron-button-svg" role="img">
                            <use xlink:href="#icon-chevron" />
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        <van-popup v-model:show="show" position="bottom">
            <van-date-picker
                :model-value="['1990', '01', '01']"
                title="选择出生年月"
                :min-date="minDate"
                :max-date="maxDate"
                @confirm="handleConfirm"
                @cancel="show = false"
            />
        </van-popup>
    </div>
</template>
