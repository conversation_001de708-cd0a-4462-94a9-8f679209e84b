import { ElementFactory, Question, Serializer } from 'survey-core'

import AddonDatePicker from './index.vue'

const CUSTOM_TYPE = 'addon-date-picker'

// Create a question model
export class QuestionDatePickerModel extends Question {
    override getType() {
        return CUSTOM_TYPE
    }
}

ElementFactory.Instance.registerElement(CUSTOM_TYPE, (name) => {
    return new QuestionDatePickerModel(name)
})

// Add question type metadata for further serialization into JSON
Serializer.addClass(
    CUSTOM_TYPE,
    [],
    () => {
        return new QuestionDatePickerModel('')
    },
    'question',
)

useNuxtApp().vueApp.component('survey-addon-date-picker', AddonDatePicker)
