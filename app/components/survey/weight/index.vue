<script setup lang="ts">
import SlideRuler from '@/utils/slide-rule'
import EditDialog from '../edit-dialog.vue'

const props = defineProps<{ question: any }>()

const heightRulerRef = useTemplateRef('heightRulerRef')

const rulerGeneralProps = {
    fontSize: 13,
    precision: 0.1,
    divide: 5,
    heightDecimal: 28,
    heightDigit: 18,
    fontMarginTop: 45,
}

const lastVibrationTime = ref(0)
const VIBRATION_THRESHOLD = 100

let slider: any

onMounted(() => {
    slider = new SlideRuler({
        ...rulerGeneralProps,
        el: heightRulerRef.value,
        maxValue: 200,
        minValue: 30,
        currentValue: props.question.value || 60,
        handleValue: (v: number) => {
            props.question.value = v
            if (navigator?.vibrate) {
                const now = Date.now()
                if (now - lastVibrationTime.value > VIBRATION_THRESHOLD) {
                    navigator?.vibrate([10, 10])
                    lastVibrationTime.value = now
                }
            }
        },
    })

    if (!props.question.value) {
        props.question.value = 60
    }
})

const isDialogShow = ref(false)

function handleDialogConfirm(value: number) {
    const newValue = slider?.setValue(value)
    props.question.value = newValue
}

function handleDialogOpen() {
    isDialogShow.value = true
}
</script>

<template>
    <div class="relative of-hidden w-327px h-123px">
        <div flex gap-5px items-end justify-center w-full relative>
            <span text="t-5 10px">
                体重
            </span>
            <span text="t-5 24px" leading-none font-600>
                {{ `${props.question.value}` }}
            </span>
            <span text="t-5 10px">
                公斤
            </span>

            <div class="i-custom:pen w-12px h-12px ml-4px relative bottom-3px" @click="handleDialogOpen"></div>
        </div>

        <div ref="heightRulerRef" class="absolute -left-40px mt-10px"></div>

        <edit-dialog
            v-model:show="isDialogShow" title="设置体重" :value="props.question.value || 60" unit="公斤"
            @confirm="handleDialogConfirm"
        />
    </div>
</template>
