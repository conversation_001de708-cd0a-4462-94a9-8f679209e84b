import { ElementFactory, Question, Serializer } from 'survey-core'

import AddonWeight from './index.vue'

const CUSTOM_TYPE = 'addon-weight'

// Create a question model
export class QuestionWeightModel extends Question {
    override getType() {
        return CUSTOM_TYPE
    }
}

ElementFactory.Instance.registerElement(CUSTOM_TYPE, (name) => {
    return new QuestionWeightModel(name)
})

// Add question type metadata for further serialization into JSON
Serializer.addClass(
    CUSTOM_TYPE,
    [],
    () => {
        return new QuestionWeightModel('')
    },
    'question',
)

useNuxtApp().vueApp.component('survey-addon-weight', AddonWeight)
