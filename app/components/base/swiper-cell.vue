<script setup lang="ts">
const props = defineProps<{
    threshold?: number // 触发滑动的阈值
}>()

const emit = defineEmits<{
    delete: []
}>()

const startX = ref(0)
const moveX = ref(0)
const isMoving = ref(false)
const containerRef = ref<HTMLElement>()

// 删除按钮宽度
const DELETE_BTN_WIDTH = 80
// 默认触发阈值
const DEFAULT_THRESHOLD = 0.3

const offset = computed(() => {
    if (!isMoving.value) return moveX.value
    const diff = moveX.value - startX.value
    return Math.min(Math.max(-DELETE_BTN_WIDTH, diff), 0)
})

function touchStart(e: TouchEvent) {
    startX.value = e.touches[0]!.clientX
    isMoving.value = true
}

function touchMove(e: TouchEvent) {
    if (!isMoving.value) return
    moveX.value = e.touches[0]!.clientX
}

function touchEnd() {
    isMoving.value = false
    const threshold = props.threshold || DEFAULT_THRESHOLD

    // 如果滑动距离超过阈值，展开删除按钮
    if (Math.abs(offset.value) > DELETE_BTN_WIDTH * threshold) {
        moveX.value = startX.value - DELETE_BTN_WIDTH
    } else {
        moveX.value = startX.value
    }
}

// 点击删除
function handleDelete() {
    emit('delete')
    // 重置位置
    moveX.value = startX.value
}

// 点击内容区域时收起
function handleContentClick() {
    if (offset.value < 0) {
        moveX.value = startX.value
    }
}
</script>

<template>
    <div
        ref="containerRef"
        class="swiper-cell"
        @touchstart="touchStart"
        @touchmove="touchMove"
        @touchend="touchEnd"
    >
        <div
            class="swiper-cell__content"
            :style="{ transform: `translateX(${offset}px)` }"
            @click="handleContentClick"
        >
            <slot></slot>
        </div>

        <div class="swiper-cell__right">
            <div
                class="delete-btn"
                @click="handleDelete"
            >
                删除
            </div>
        </div>
    </div>
</template>

<style scoped>
.swiper-cell {
  position: relative;
  overflow: hidden;
}

.swiper-cell__content {
  position: relative;
  z-index: 1;
  background: #fff;
  transition: transform 0.2s ease;
}

.swiper-cell__right {
  position: absolute;
  top: 0;
  right: 0;
  height: 100%;
  z-index: 0;
}

.delete-btn {
  height: 100%;
  width: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #ff4444;
  color: #fff;
}
</style>
