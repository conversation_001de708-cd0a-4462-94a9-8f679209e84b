<script setup lang="ts">
const { placeholder = '输入客户姓名检索' } = defineProps<{
    placeholder?: string
}>()

const emit = defineEmits<{
    (e: 'search', v: string): void
}>()

const modelValue = defineModel<string>({
    default: '',
})
</script>

<template>
    <div class="bg-white rd-10px flex items-center of-hidden">
        <van-field
            v-model="modelValue"
            style="--van-cell-background: transparent; --van-cell-vertical-padding: 6px; --van-field-placeholder-text-color: rgb(var(--t-3));"
            :placeholder
            class="rounded-full h-50px"
            @keypress.enter="$emit('search', modelValue)"
        />
        <van-button type="primary" class="bg-transparent! border-none! block! h-30px!" @click="$emit('search', modelValue)">
            <span class="i-custom-search block w-16px h-16px"></span>
        </van-button>
    </div>
</template>

<style scoped>
:deep(.van-field__body) {
    height: 38px;
}
</style>
