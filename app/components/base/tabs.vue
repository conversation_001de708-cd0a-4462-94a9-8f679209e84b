<script setup lang="ts">
const { tabs } = defineProps<{
    tabs: {
        name: string
        title: string
    }[]
}>()

const active = defineModel<string>('active', { required: true })
</script>

<template>
    <div>
        <div class="h-40px bg-fill-1 rd-10px p-4px flex items-center justify-around">
            <div
                v-for="tab in tabs" :key="tab.name" class="w-76px flex h-32px  rd-6px items-center justify-center"
                :class="tab.name === active ? 'bg-#fff font-600' : ''"
                @click="active = tab.name"
            >
                {{ tab.title }}
            </div>
        </div>
    </div>
</template>
