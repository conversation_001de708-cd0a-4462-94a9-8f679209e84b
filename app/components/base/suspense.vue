<script setup lang="ts">
import type { AsyncDataRequestStatus } from '#app'

const { status = 'idle', type = 'circle' } = defineProps<{
    status: AsyncDataRequestStatus
    type?: 'square' | 'circle'
}>()
</script>

<template>
    <template v-if="['pending', 'idle'].includes(status)">
        <shared-full-loading :type />
    </template>
    <template v-else-if="status === 'success'">
        <slot></slot>
    </template>
    <template v-else-if="status === 'error'">
        <slot name="error"></slot>
    </template>
</template>
