<script setup lang="ts">
defineProps<{
    hidden?: boolean
}>()
</script>

<template>
    <div class="bg-white fixed left-0 right-0 safe-area flex items-center justify-center" :class="hidden ? '-bottom-1000px' : 'bottom-0'">
        <slot></slot>
    </div>
</template>

<style lang="scss" scoped>
.safe-area {
   height: calc(80px + constant(safe-area-inset-bottom));
   height: calc(80px + env(safe-area-inset-bottom));
   box-shadow: 0px 2px 8px 0px #0000001A;
   background: white;
   background-size: 100% 100%;
}
</style>
