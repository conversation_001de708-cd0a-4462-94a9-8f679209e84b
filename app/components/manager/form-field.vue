<script setup lang="ts">
import { createCascadeAreaData } from '@/utils/custom-area'

const { inputType = 'text' } = defineProps<{
    label: string
    placeholder: string
    type: 'input' | 'select' | 'area'
    inputType?: 'tel' | 'text'
    options?: {
        text: string
        value: string
    }[]
}>()

const modelValue = defineModel<string>('modelValue')
const multiAreaValue = defineModel<string[]>('multiAreaValue', { default: () => [] })
const isShow = ref(false)
const isShowArea = ref(false)

function onConfirm(value: any) {
    modelValue.value = value.selectedOptions[0].text
    isShow.value = false
}

// 多选地区相关函数
function onMultiAreaConfirm(value: any) {
    if (value.selectedOptions && value.selectedOptions.length > 0) {
        const province = value.selectedOptions[0]
        const city = value.selectedOptions[1]
        if (province && city) {
            let areaText = ''
            if (city.text === '全部') {
                areaText = province.text
            } else {
                areaText = `${province.text}${city.text}`
            }

            // 检查是否已经存在相同的地区
            if (!multiAreaValue.value.includes(areaText)) {
                multiAreaValue.value = [...multiAreaValue.value, areaText]
            }
        }
    }
    isShowArea.value = false
}

function removeArea(area: string) {
    multiAreaValue.value = multiAreaValue.value.filter(item => item !== area)
}
</script>

<template>
    <div>
        <label class="label">{{ label }}</label>
        <input v-if="type === 'input'" v-model="modelValue" class="input" :type="inputType" :placeholder="placeholder" />
        <div
            v-else-if="type === 'select'" class="input" type="text" :placeholder="placeholder" @click="() => {
                isShow = true
            }"
        >
            <div class="flex justify-between items-center relative top-7px">
                <div>
                    <span v-if="!modelValue" class="text-t-3 relative top-7px">
                        {{ placeholder }}
                    </span>
                    <span v-else class="text-t-5">
                        {{ modelValue }}
                    </span>
                </div>

                <div class="i-radix-icons-chevron-down">
                </div>
            </div>
        </div>

        <div
            v-else-if="type === 'area'"
            class="flex justify-between items-center relative top-7px pb-12px"
            style="border-bottom: 1px solid #E5E7EB"

            @click="() => {
                isShowArea = true
            }"
        >
            <div>
                <div v-if="multiAreaValue.length > 0" class="flex gap-8px flex-wrap">
                    <van-tag
                        v-for="area in multiAreaValue"
                        :key="area"
                        :text="area"
                        type="primary"
                        closeable
                        size="medium"
                        class="area-tag"
                        @close="removeArea(area)"
                    >
                        {{ area }}
                    </van-tag>
                </div>
                <span v-else class="text-t-3 relative top-7px">
                    {{ placeholder }}
                </span>
            </div>

            <div class="i-radix-icons-chevron-down flex-shrink-0">
            </div>
        </div>
    </div>

    <van-popup v-model:show="isShow" position="bottom">
        <van-picker :columns="options" @confirm="onConfirm" />
    </van-popup>

    <van-popup v-model:show="isShowArea" position="bottom">
        <van-picker
            title="选择地区"
            :columns="createCascadeAreaData()"
            @confirm="onMultiAreaConfirm"
        />
    </van-popup>
</template>

<style scoped lang="scss">
.label {
  @apply text-15px text-t-5 font-600 block;
}

.input {
  border-bottom: 1px solid #E5E7EB;
  @apply w-full h-40px;
}
</style>
