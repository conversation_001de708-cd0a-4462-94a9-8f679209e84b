<script setup lang="ts">
defineProps<{
    report: ReportInterpretation
}>()
</script>

<template>
    <div bg-white p-16px>
        <div flex gap-8px>
            <van-image :src="formatResource(report.questionCoverPicture)" w-80px h-80px rd-4px fit="cover" overflow-hidden alt="" />
            <div>
                <p text="t-5 16px">{{ report.customerName }}</p>

                <p text="t-3 13px" my-8px>
                    {{ report.questionTitle }}
                </p>
                <p text="t-3 13px">
                    填报时间: {{ formatDate(report.interpretationTime, 'YYYY-MM-DD') || '-' }}
                </p>
            </div>
        </div>
        <div w-full my-16px divider></div>

        <nuxt-link :to="`/manager/report-read/${report.reportInterpretationId}`">
            <van-button block class="!h-32px !bg-primary-1 !border-none !text-primary-6">
                <div flex items-center gap-4px>
                    <div class="i-custom-report-1 w-10px h-12px text-primary-6">
                    </div>
                    解读报告
                </div>
            </van-button>
        </nuxt-link>
    </div>
</template>
