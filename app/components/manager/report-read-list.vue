<script setup lang="ts">
const show = ref(false)
const projectType = ref<'init' | 'publish'>('init')

function handleFilterClick(type: 'init' | 'publish') {
    projectType.value = type
    show.value = false
}

const route = useRoute()

const { data, status } = useAPI<ReportInterpretation[]>('/report-interpretation', {
    params: {
        type: projectType,
        projectId: route.params.projectId,
    },
})

const keyWord = ref('')

const filteredReports = computed(() => {
    return data.value?.results.filter((item) => {
        return item.projectName?.includes(keyWord.value)
    })
})
</script>

<template>
    <base-suspense :status>
        <div h-full overflow-auto flex flex-col justify-between>
            <div>
                <div class="px-16px py-10px bg-white w-full flex items-center space-x-6px">
                    <base-search v-model="keyWord" flex-1 placeholder="请输入报告名称检索" />

                    <van-button
                        type="primary" plain style="--van-button-normal-padding: 0px;"
                        :color="projectType === 'init' ? 'rgb(var(--t-4))' : ''"
                        class="border-none! block! h-full! aspect-square"
                        @click="show = true"
                    >
                        <span class="i-custom-filter block w-16px h-16px"></span>
                    </van-button>
                </div>
                <div v-if="filteredReports?.length" p-16px flex flex-col space-y-12px>
                    <div v-for="report in filteredReports" :key="report.reportInterpretationId">
                        <manager-report-card :report="report" />
                    </div>
                </div>

                <van-empty v-else description="暂无报告" />
            </div>
            <div flex px-16px>
                <span class="i-custom-information-fill shrink-0 w-16px h-16px mr-4px"></span>
                <span class="text-t-3 text-11px">默认展示近7日评估问卷，更多评估问卷请通过检索功能查找</span>
            </div>

            <van-popup v-model:show="show" px-16px position="top">
                <div flex items-center justify-between @click="handleFilterClick('init')">
                    <div text="16px t-5" py-16px>待解读报告</div>
                    <div v-show="projectType === 'init'" class="i-custom-check w-16px h-16px text-primary-6"></div>
                </div>

                <div flex items-center justify-between @click="handleFilterClick('publish')">
                    <div text="16px t-5" py-16px>已解读报告</div>
                    <div v-show="projectType === 'publish'" class="i-custom-check w-16px h-16px text-primary-6"></div>
                </div>
            </van-popup>
        </div>
    </base-suspense>
</template>
