<script setup lang="ts">
const { showInvite = true, survey } = defineProps<{
    showInvite?: boolean
    survey: ReportInterpretation
}>()
</script>

<template>
    <div bg-white p-16px>
        <div flex gap-8px>
            <van-image :src="formatResource(survey.questionCoverPicture)" overflow-hidden fit="cover" w-80px h-80px rd-4px />

            <div>
                <div text="t-5 16px">
                    {{ survey.questionTitle }}
                </div>

                <div text-t-3 mt-8px>
                    发布时间：{{ formatDate(survey.createTime) }}
                </div>
            </div>
        </div>

        <div h-1px bg-fill-3 w-full my-16px></div>

        <div flex justify-between gap-16px>
            <nuxt-link v-if="showInvite" class="w-full bg-primary-1 active:bg-primary-1/70 text-primary-6 rd-2px h-32px rd-2px px-16px py-6px flex items-center gap-5px justify-center">
                <div class="i-custom-email  w-12px h-12px"></div>
                发送邀约
            </nuxt-link>

            <nuxt-link :to="`/manager/report-read/${survey.reportInterpretationId}`" class="bg-primary-1 w-full active:bg-primary-1/70 text-primary-6 rd-2px h-32px rd-2px px-16px py-6px flex items-center gap-5px justify-center">
                <div class="i-custom-information w-12px h-12px text-primary-6"></div>
                查看详情
            </nuxt-link>
        </div>
    </div>
</template>
