<script setup lang="ts">
const datas = ref([
    {
        name: '招募客户',
        count: '10221',
    },
    {
        name: '本周客户',
        count: '10',
    },
    {
        name: '成单例数',
        count: '10',
    },
    {
        name: '本周单数',
        count: '10',
    },
])
</script>

<template>
    <div rd-4px py-12px px-16px style="background: linear-gradient(180deg, #D4E7FE 100%, #E6F4FF 100%);">
        <div text="#1D2129 13px" font-500>
            账号运营数据
        </div>

        <div mt-16px flex justify-around>
            <div v-for="(item, index) in datas" :key="index" flex="~ col" gap-8px items-center>
                <div text-primary-6 text-20px font-500>
                    {{ item.count }}
                </div>

                <div text="t-4 13px">
                    {{ item.name }}
                </div>
            </div>
        </div>
    </div>
</template>
