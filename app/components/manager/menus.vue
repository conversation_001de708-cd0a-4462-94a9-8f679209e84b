<script setup lang="ts">
const props = defineProps<{
    showMenus: <PERSON><PERSON><PERSON><PERSON>[]
    pathPrefix: string
}>()

const allMenus: Menu[] = [
    {
        name: '全部项目',
        key: 'project',
        icon: 'i-custom-project',
    },
    {
        name: '评估问卷',
        key: 'survey',
        icon: 'i-custom-survey',
    },
    {
        name: '干预用户',
        key: 'interfere-user',
        icon: 'i-custom-users',
    },
    {
        name: '用户列表',
        key: 'user-list',
        icon: 'i-custom-users',
    },
    {
        name: '用户数据',
        key: 'operation-data',
        icon: 'i-custom-command',
    },
    {
        name: '邀约响应',
        key: 'invitation-responding',
        icon: 'i-custom-message',
    },
    {
        name: '用户预约',
        key: 'user-booking',
        icon: 'i-custom-user',
    },
    {
        name: '报告解读',
        key: 'report-read',
        icon: 'i-custom-file',
    },
    {
        name: '团队管理',
        key: 'team-management',
        icon: 'i-custom-group',
    },
]

const menus = computed(() => {
    return props.showMenus.map((key) => {
        return {
            ...allMenus.find(item => item.key === key),
        }
    })
})
</script>

<template>
    <div flex="~ wrap" mt-16px bg="#fff" gap-y-16px rd-4px py-16px>
        <nuxt-link v-for="(item, index) in menus" :key="index" :to="`${pathPrefix}/${item.key}?surveyId=${$route.query.surveyId}`" class="w-25%">
            <div flex="~ col" items-center gap-8px>
                <div :class="item.icon" w-32px h-32px></div>
                <div text="t-4 13px">
                    {{ item.name }}
                </div>
            </div>
        </nuxt-link>
    </div>
</template>
