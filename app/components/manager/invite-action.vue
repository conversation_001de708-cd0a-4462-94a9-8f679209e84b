<script setup lang="ts">
const show = ref(false)

const actions = ref<Project[]>([])

async function getProject() {
    const { results } = await useWrapFetch<BaseResponse<Project[]>>('/operator/project')
    actions.value = results
}

async function handleInvitation() {
    const { closeLoading } = useLoading()
    try {
        await getProject()
        const route = useRoute()

        const projectId = route.params.projectId as string
        if (route.params.projectId) {
            const questionId = actions.value.find(item => String(item.projectId) === route.params.projectId)?.questionId

            if (!questionId) {
                showFailToast('未找到对应的问卷')
                return
            }

            onSelect({ projectId: Number(projectId), questionId })
        } else {
            show.value = true
        }
    } catch (error) {
        console.log(error)
    } finally {
        closeLoading()
    }
}

const { userInfo } = useUserStore()

async function onSelect(project: Project) {
    const { results } = await useWrapFetch<BaseResponse<string>>('/operator/createCustomInvite', {
        params: {
            projectId: project.projectId,
            questionId: project.questionId,
        },
    })

    // const wxShareData = {
    //     title: '问卷填写',
    //     desc: `${userInfo?.name}邀请您填写问卷`,
    //     link: `${CLIENT_DOMAIN}/login` + `#${results}`,
    //     imgUrl: WX_SHARE_IMG,
    // }

    // localStorage.setItem('wx_share_data', JSON.stringify(wxShareData))

    // navigateTo('/manager/shared')

    const wx = await useWxBridge({})

    wx?.miniProgram.redirectTo({
        url: `/pages/share/index?state=${results}`,
    })
}
</script>

<template>
    <div>
        <van-button type="primary" class="!h-44px" block @click="handleInvitation">点击邀约客户</van-button>
        <van-action-sheet
            v-model:show="show" cancel-text="取消"
            description="选择一个项目邀约客户"
            :actions="actions" @select="onSelect"
        />
    </div>
</template>
