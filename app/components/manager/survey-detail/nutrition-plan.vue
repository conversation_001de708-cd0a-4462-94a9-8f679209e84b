<script setup lang="ts">
import { cloneDeep } from 'lodash-es'

const { reportDetail } = defineProps<{
    reportDetail: ReportInterpretation
}>()

const nutritionPlans = ref<NutritionProgram>()
let nutritionPlansBackup: NutritionProgram

const isEdit = ref(false)

const isLoading = ref(false)
async function init() {
    isLoading.value = true
    try {
        const { results } = await useWrapFetch<BaseResponse<NutritionProgram>>(`/nutrition-program/getByQuestionResultId/${reportDetail.questionResultId}`)

        if (results.productContent === null) {
            const { results: productResults } = await useWrapFetch<BaseResponse<any[]>>(`/project/product/list`, {
                method: 'POST',
                body: {
                    projectId: reportDetail.projectId,
                    searchContext: '',
                },
            })

            const programs = productResults.map((item) => {
                return {
                    projectProductId: item.id,
                    number: 1,
                    price: item.newPrice,
                    projectProductName: item.name,
                }
            })

            const { results: _results } = await useWrapFetch<BaseResponse<boolean>>('/nutrition-program', {
                method: 'post',
                body: {
                    customerId: reportDetail.customerId,
                    questionId: reportDetail.questionId,
                    questionResultId: reportDetail.questionResultId,
                    projectId: reportDetail.projectId,
                    productContent: programs,
                },
            })

            if (_results)
                init()
        } else {
            nutritionPlans.value = results
            nutritionPlansBackup = cloneDeep(results)
        }
    } catch (error) {

    } finally {
        isLoading.value = false
    }
}

init()

function handleCancel() {
    nutritionPlans.value = cloneDeep(nutritionPlansBackup)
    isEdit.value = false
}

async function handleSave() {
    const { results } = await useWrapFetch<BaseResponse<boolean>>('/nutrition-program/edit', {
        method: 'post',
        body: nutritionPlans.value,
    })

    if (results) {
        isEdit.value = false
        init()
        showSuccessToast('保存成功')
    }
}

async function handlePublish() {
    const { results } = await useWrapFetch<BaseResponse<boolean>>('/nutrition-program/publish', {
        method: 'post',
        body: {
            nutritionProgramId: nutritionPlans.value?.nutritionProgramId,
            productContent: nutritionPlans.value?.productContent,
        },
    })

    if (results) {
        init()
        showSuccessToast('发布成功')
    }
}
</script>

<template>
    <shared-full-loading v-if="isLoading" />

    <div v-else mx-16px mt-16px p-16px rd-4px bg-white>
        <div h-full flex="~ col" justify-between gap-20px>
            <div flex="~ col" gap-16px overflow-auto class="scrollbar-hide">
                <div v-for="(item, index) in nutritionPlans?.productContent" :key="index" flex gap-8px>
                    <van-image flex-shrink-0 :src="formatResource(item.pictureId)" overflow-hidden fit="cover" w-112px h-112px rd-4px />

                    <div flex="~ col" justify-between>
                        <div text="16px t-5" font-500>
                            {{ item.projectProductName }}
                        </div>

                        <div text="12px t-3" w-190px line-clamp-2 break-all>
                            {{ item.describeContext }}
                        </div>

                        <div flex items-center justify-between>
                            <div text="17px primary-6">
                                {{ formatPrice(item.price) }}
                            </div>

                            <shared-counter v-model:count="nutritionPlans!.productContent![index]!.number" :min="0" :mode="isEdit ? 'edit' : 'view'" />
                        </div>
                    </div>
                </div>
            </div>

            <template v-if="nutritionPlans?.status === 'init'">
                <div v-if="!isEdit" flex gap-10px>
                    <van-button type="primary" class="!h-44px" block @click="isEdit = !isEdit">
                        调整
                    </van-button>
                    <van-button type="primary" class="!h-44px" block @click="handlePublish">
                        发布
                    </van-button>
                </div>

                <div v-else flex gap-10px>
                    <van-button type="primary" class="!h-44px" block @click="handleSave">
                        保存
                    </van-button>
                    <van-button type="primary" class="!h-44px" block @click="handleCancel">
                        取消
                    </van-button>
                </div>
            </template>
            <!-- <van-button type="primary" class="!h-44px flex-shrink-0 block" @click="isEdit = !isEdit">
                {{ isEdit ? '保存并发布' : '调整' }}
            </van-button> -->
        </div>
    </div>
</template>
