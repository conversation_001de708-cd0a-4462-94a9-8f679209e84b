<script setup lang="ts">
const reportDetail = defineModel<ReportInterpretation>('reportDetail')

const mode = ref<'view' | 'edit'>('view')
const content = ref(reportDetail.value!.content)
const route = useRoute()
const reportInterpretationId = route.params.interpretationId

function handleCancel() {
    mode.value = 'view'
    content.value = reportDetail.value!.content
}

async function handleSave() {
    const { results, msg } = await useWrapFetch<BaseResponse<boolean>>('/report-interpretation/edit', {
        method: 'post',
        body: {
            reportInterpretationId,
            content: content.value,
        },
    })

    if (results) {
        showSuccessToast('保存成功')

        mode.value = 'view'
    } else {
        showFailToast(msg as string)
    }
}

async function publish() {
    await useWrapFetch('/report-interpretation/publish', {
        method: 'post',
        body: {
            reportInterpretationId,
            content: content.value,
        },
    })

    showSuccessToast('发布成功')
    reportDetail.value!.status = 'publish'
}
</script>

<template>
    <div p-16px class="h-[calc(100vh-48px)]">
        <div v-if="mode === 'view'" bg-white p-16px rd-4px h-full flex="~ col" justify-between>
            <p v-if="content">
                {{ content }}
            </p>

            <van-empty v-else description="暂无解读结果" />

            <div v-if="reportDetail!.status !== 'publish'" flex gap-10px>
                <van-button type="primary" class="!h-44px" block @click="mode = 'edit'">编辑</van-button>
                <van-button v-if="content" type="primary" class="!h-44px" block @click="publish">发布</van-button>
            </div>
        </div>

        <template v-else-if="mode === 'edit'">
            <van-field v-model="content" type="textarea" placeholder="请输入解读结果" rows="15" autosize />

            <div flex gap-10px mt-16px>
                <van-button type="primary" class="!h-44px" block @click="handleSave">保存</van-button>
                <van-button type="primary" class="!h-44px" block @click="handleCancel">取消</van-button>
            </div>
        </template>
    </div>
</template>
