<script setup lang="ts">
import VuePdfEmbed from 'vue-pdf-embed'

const { reportDetail, showLevel = false } = defineProps<{
    reportDetail: ReportInterpretation
    showLevel?: boolean
}>()

const isLoaded = ref(false)

const { data } = useAPI<string>(`/health-report/${reportDetail.questionResultId}`)
</script>

<template>
    <div v-show="isLoaded" v-if="data?.results" class="pt-16px px-16px h-[calc(100vh-60px)] scrollbar-hide overflow-auto">
        <div v-if="showLevel" bg="#ECE8E3" p-16px flex justify-between items-center>
            <div>
                <p text="t-5 12px">
                    您好，和大壮
                </p>

                <p text="15px t-5" font-500>
                    您的健康评估总结为：
                </p>
            </div>

            <div font-500 text="20px primary-6">
                中风险
            </div>
        </div>

        <vue-pdf-embed
            :source="formatResource(data!.results)"
            @loaded="() => {
                isLoaded = true
            }"
        />
    </div>

    <shared-full-loading v-show="!isLoaded" />
</template>
