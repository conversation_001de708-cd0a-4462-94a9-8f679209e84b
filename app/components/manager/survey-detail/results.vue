<script setup lang="ts">
const { reportDetail } = defineProps<{
    reportDetail: ReportInterpretation
}>()
const content = ref('')

const surveyId = reportDetail.questionId
const resultId = reportDetail.questionResultId
const surveyRef = useTemplateRef('surveyRef')
const surveyResult = ref('')

const isLoading = ref(false)
async function init() {
    try {
        isLoading.value = true
        const { results } = await useWrapFetch<BaseResponse<QuestionMeta>>(`/question/get/${surveyId}`)
        content.value = results.content

        if (surveyId) {
            const { results: _surveyResult } = await useWrapFetch<BaseResponse<QuestionList>>(`/question-result/${resultId}`)
            surveyResult.value = _surveyResult.questionResultId
        }

        await nextTick()

        surveyRef.value?.renderSurvey()
    } catch (error) {
        console.log(error)
    } finally {
        isLoading.value = false
    }
}

init()
</script>

<template>
    <div>
        <shared-full-loading v-show="isLoading" />

        <shared-survey v-show="!isLoading" ref="surveyRef" mode="display" :content :result="surveyResult" />
    </div>
</template>
