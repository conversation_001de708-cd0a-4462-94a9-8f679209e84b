<script setup lang="ts">
import destr from 'destr'

const route = useRoute()

const { data, status } = useAPI<User>('/operator/user', {
    params: {
        customerId: route.params.userId,
    },
})

watch(data, (val) => {
    useHead({
        title: val?.results.name,
    })
})

const forms = computed(() => {
    const results = data.value?.results
    const archiveContent = destr<Record<string, string>>(results?.archiveContent)

    return [
        {
            label: 'ID',
            value: results?.id,
        },
        {
            label: '姓名',
            value: results?.name,
        },
        {
            label: '性别',
            value: parseIDCard(results!.idCard).gender === 'male' ? '男' : '女',
        },
        {
            label: '年龄',
            value: parseIDCard(results!.idCard).age,
        },
        {
            label: '手机号',
            value: results?.phone,
        },
        {
            label: '身份证号',
            value: results?.idCard,
        },
        {
            label: '身高(cm)',
            value: archiveContent.archive_height,
        },
        {
            label: '体重(kg)',
            value: archiveContent.archive_weight,
        },
    ]
})
</script>

<template>
    <base-suspense :status>
        <div rd-4px mt-16px mx-16px px-16px py-24px bg-white class="h-[calc(100vh-60px)]">
            <div class="flex flex-col items-center space-y-8px pt-8px pb-24px">
                <van-image :src="data?.results.headImgUrl" fit="cover" w-56px h-56px class="of-hidden rounded-4px" />
                <span text-t-4 text-13px>头</span>
            </div>

            <div v-for="form in forms" :key="form.label">
                <div flex text-t-5>
                    <div w-72px>
                        {{ form.label }}
                    </div>
                    <div>
                        {{ form.value }}
                    </div>
                </div>

                <div h-1px my-16px bg="#E5E6EB" w-full></div>
            </div>
        </div>
    </base-suspense>
</template>
