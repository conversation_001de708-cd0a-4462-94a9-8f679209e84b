<script setup lang="ts">
const allUsers = ref([
    { id: 1, name: '张安旭', idCard: '330624199803031177', headImgUrl: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg' },
    { id: 2, name: '冯爱勇', idCard: '330624199803031177', headImgUrl: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg' },
    { id: 3, name: '何永毅', idCard: '330624199803031177', headImgUrl: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg' },
    { id: 4, name: '冯雪松', idCard: '330624199803031177', headImgUrl: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg' },
])

const show = ref(false)
const projectType = ref<'responded' | 'pending'>('responded')

function handleFilterClick(type: 'responded' | 'pending') {
    projectType.value = type
    show.value = false
}
</script>

<template>
    <div h-full overflow-auto flex flex-col>
        <div class="px-16px py-10px bg-white w-full flex items-center space-x-6px">
            <base-search flex-1 placeholder="请输入客户姓名检索" />

            <van-button
                type="primary" plain style="--van-button-normal-padding: 0px;"
                :color="projectType === 'responded' ? 'rgb(var(--t-4))' : ''"
                class="border-none! block! h-full! aspect-square"
                @click="show = true"
            >
                <span class="i-custom-filter block w-16px h-16px"></span>
            </van-button>
        </div>
        <div p-16px flex flex-col space-y-12px>
            <nuxt-link v-for="user in allUsers" :key="user.id">
                <manager-user-card :member="user" show-invite />
            </nuxt-link>
        </div>

        <van-popup v-model:show="show" px-16px position="top">
            <div flex items-center justify-between @click="handleFilterClick('responded')">
                <div text="16px t-5" py-16px>响应客户</div>
                <div v-show="projectType === 'responded'" class="i-custom-check w-16px h-16px text-primary-6"></div>
            </div>

            <div flex items-center justify-between @click="handleFilterClick('pending')">
                <div text="16px t-5" py-16px>待响应客户</div>
                <div v-show="projectType === 'pending'" class="i-custom-check w-16px h-16px text-primary-6"></div>
            </div>
        </van-popup>
    </div>
</template>
