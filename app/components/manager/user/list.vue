<script setup lang="ts">
import { showToast } from 'vant'

defineProps<{
    showRegisterTime?: boolean
    showPhone?: boolean
}>()

// one year ago
const now = new Date()
const minDate = new Date(now.getFullYear() - 1, now.getMonth(), now.getDate())
const maxDate = now

const showDateRange = ref(false)

function handleSearch(searchContext: string) {
    showToast({
        message: `搜索内容: ${searchContext}`,
        position: 'bottom',
    })
}

const route = useRoute()
const projectId = route.params.projectId || 0

const { data } = useAPI<User[]>('/operator/users', {
    params: {
        projectId: route.params.projectId || '',
    },
})

const keyWord = ref('')

const filteredUsers = computed(() => {
    return data.value?.results.filter((item) => {
        return item.name?.includes(keyWord.value)
    })
})

function handleSelectDataRange(dateRange: [start: Date, end: Date]) {
    showDateRange.value = false
    showToast({
        message: String(dateRange),
        position: 'bottom',
    })
}
</script>

<template>
    <div h-full overflow-auto flex flex-col>
        <div class="px-16px py-10px bg-white w-full flex space-x-6px">
            <base-search v-model="keyWord" class="flex-1" @search="handleSearch" />

            <van-button
                type="primary" style="--van-button-normal-padding: 0px;"
                class="bg-transparent! border-none! block! h-full! aspect-square"
                @click="showDateRange = true"
            >
                <span class="i-custom-time block w-16px h-16px"></span>
            </van-button>

            <teleport to="body">
                <van-calendar
                    v-model:show="showDateRange"
                    type="range" allow-same-day
                    :min-date="minDate"
                    :max-date="maxDate"
                    @confirm="handleSelectDataRange"
                />
            </teleport>
        </div>
        <div v-if="filteredUsers?.length" p-16px flex flex-col space-y-12px>
            <nuxt-link v-for="user in filteredUsers" :key="user.id" :to="`/manager/project/${projectId}/interfere-user/${user.customerId}`">
                <manager-user-card :member="user" :show-register-time="showRegisterTime" :show-phone="showPhone" />
            </nuxt-link>
        </div>

        <van-empty v-else description="暂无用户" />

        <div flex-1></div>
        <div flex px-16px>
            <span class="i-custom-information-fill shrink-0 w-16px h-16px mr-4px"></span>
            <span class="text-t-3 text-11px">默认展示近7日签约客户信息，查看更多签约客户信息请修改签约日期范围或通过检索功能查找</span>
        </div>
    </div>
</template>
