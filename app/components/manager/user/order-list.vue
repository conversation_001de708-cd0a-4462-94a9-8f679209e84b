<script setup lang="ts">
const route = useRoute()

const { data, status } = useAPI<NutritionProgram[]>(`/nutrition-program/manager/getByCustomerId/${route.params.userId}`)
</script>

<template>
    <base-suspense :status>
        <div v-if="data?.results.length" p-16px>
            <div v-for="program in data?.results" :key="program.nutritionProgramId">
                <div flex justify-between>
                    <div text-t-4>
                        {{ program.projectName }}
                    </div>
                    <div text-t-4>
                        {{ formatDate(program.createTime, 'YYYY-MM-DD') }}
                    </div>
                </div>

                <div v-for="order in program.productContent" :key="order.projectProductId" rd-4px bg-white p-16px mt-8px>
                    <div flex gap-8px mt-8px>
                        <van-image flex-shrink-0 h-56px w-56px :src="formatResource(order.pictureId)" fit="cover" class="of-hidden rounded-4px" />

                        <div flex="~ col" justify-between>
                            <div text="16px t-5">
                                {{ order.projectProductName }}
                            </div>

                            <div text="12px t-3" line-clamp-2 break-all>
                                {{ order.describeContext }}
                            </div>

                            <!-- <div flex items-center justify-between>
                                <div text="17px primary-6">
                                    {{ formatPrice(order.price) }}
                                </div>

                                <div text-t-4>
                                    x {{ order.number }}
                                </div>
                            </div> -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <van-empty v-else description="暂无干预记录" />
    </base-suspense>
</template>
