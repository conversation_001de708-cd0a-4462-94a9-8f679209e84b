<!-- eslint-disable no-new -->
<script setup lang="ts">
import Chart from 'chart.js/auto'

const chartRef1 = useTemplateRef('chartRef1')
const chartRef2 = useTemplateRef('chartRef2')
function render() {
    new Chart(chartRef1.value!, {
        type: 'line',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [
                {
                    data: [12, 19, 3, 5, 2, 3, 5],
                    borderWidth: 1,
                },

                {
                    data: [10, 1, 32, 51, 22, 32, 51],
                    borderWidth: 1,
                },
            ],
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false,
                },
            },
        },
    })

    new Chart(chartRef2.value!, {
        type: 'bar',
        data: {
            labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
            datasets: [
                {
                    data: [12, 19, 3, 5, 2, 3, 5],
                    borderWidth: 1,
                },

                {
                    data: [10, 1, 32, 51, 22, 32, 51],
                    borderWidth: 1,
                },
            ],
        },
        options: {
            responsive: true,
            plugins: {
                legend: {
                    display: false,
                },
            },
        },
    })
}

onMounted(() => {
    render()
})

const active = ref(0)
</script>

<template>
    <div p-16px>
        <manager-account-data />

        <manager-waited-actions />

        <div>
            <p mt-16px mb-8px text="14px t-5" font-500>总体趋势</p>

            <canvas ref="chartRef1"></canvas>
        </div>

        <div>
            <p mt-16px mb-8px text="14px t-5" font-500>增量趋势</p>

            <canvas ref="chartRef2"></canvas>
        </div>

        <div>
            <p mt-16px mb-8px text="14px t-5" font-500>账号管理</p>

            <!-- <van-tabs v-model:active="active" animated>
                <van-tab v-for="index in 4" :key="index" :title="`标签 ${index}`">
                    内容 {{ index }}
                </van-tab>
            </van-tabs> -->
        </div>
    </div>
</template>
