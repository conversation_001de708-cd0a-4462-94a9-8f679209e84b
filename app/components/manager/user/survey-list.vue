<script setup lang="ts">
const route = useRoute()

const { data, status } = useAPI<ReportInterpretation[]>('/operator/question', {
    params: {
        customerId: route.params.userId,
    },
})
</script>

<template>
    <base-suspense :status>
        <div p-16px flex="~ col" gap-16px>
            <manager-survey-card v-for="survey in data?.results" :key="survey.reportInterpretationId" :survey="survey" :show-invite="false" />
        </div>
    </base-suspense>
</template>
