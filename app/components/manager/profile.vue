<script setup lang="ts">
import { positionMap } from '@/utils/constants'

import type { UploaderBeforeRead, UploaderFileListItem } from 'vant/es/uploader/types'

const mode = defineModel<'view' | 'edit'>('mode')
const profile = defineModel<HostProfileForm>('profile')

const positionOptions = Object.entries(positionMap).map(([value, text]) => ({ text, value }))

const showPicker = ref(false)

const route = useRoute()

const imgList = ref<UploaderFileListItem[]>([])

function handleBeforeRead(file: File) {
    if (file.size > 1024 * 1024 * 10) {
        showFailToast('文件大小不能超过10MB')
        return false
    }
    return true
}

defineExpose({
    imgList,
})

const { userInfo } = useUserStore()

const ageAndGender = computed(() => {
    return parseIDCard(userInfo!.idCard)
})
</script>

<template>
    <div class="flex flex-col items-center space-y-8px pt-8px pb-24px">
        <van-uploader v-if="mode === 'edit'" v-model="imgList" mt-10px :max-count="1" :before-read="(handleBeforeRead as UploaderBeforeRead)">
            <van-image :src="formatHeadImage(profile!.headImage, ageAndGender?.gender)" fit="cover" w-56px h-56px class="of-hidden rounded-4px" border="2px solid #fff">
                <template #loading>
                    <shared-unified-loading size="small" :rainbow="false" />
                </template>
            </van-image>
        </van-uploader>

        <van-image v-else :src="formatHeadImage(profile!.headImage, ageAndGender?.gender)" fit="cover" w-56px h-56px class="of-hidden rounded-4px" border="2px solid #fff">
            <template #loading>
                <shared-unified-loading size="small" :rainbow="false" />
            </template>
        </van-image>

        <span text-t-4 text-13px>头像</span>
    </div>

    <div class="flex-1 divide-y">
        <!-- <div class="flex space-x-16px text-16px text-t-5 py-16px">
        <p class="w-72px shrink-0">绑定微信</p>
        <p>{{ profile?.nickName }}</p>
        </div> -->

        <div class="flex space-x-16px text-16px text-t-5 py-16px">
            <p class="w-72px shrink-0">姓名</p>
            <p v-if="mode === 'view'">{{ profile!.name }}</p>
            <van-field v-else v-model="profile!.name" placeholder="请输入姓名" class="p-0!" clearable />
        </div>

        <div v-if="route.path.includes('manager')" class="flex space-x-16px text-16px text-t-5 py-16px">
            <p class="w-72px shrink-0">职务</p>
            <p v-if="mode === 'view'">{{ positionMap[profile?.duties as keyof typeof positionMap] }}</p>
            <p v-else class="flex-1 flex items-center pr-8px" @click="showPicker = true">
                <span v-if="!profile?.duties" class="flex-1 text-t-2 text-14px">请选择您的职务</span>
                <span v-else class="flex-1">{{ positionMap[profile?.duties as keyof typeof positionMap] }}</span>
                <span class="i-radix-icons:chevron-down text-t-3"></span>
            </p>
            <teleport to="body">
                <van-popup v-model:show="showPicker" position="bottom">
                    <van-picker
                        :columns="positionOptions"
                        @confirm="profile!.duties = $event.selectedOptions[0]?.value; showPicker = false"
                        @cancel="showPicker = false"
                    />
                </van-popup>
            </teleport>
        </div>

        <div class="flex space-x-16px text-16px text-t-5 py-16px">
            <p class="w-72px shrink-0">身份证号</p>
            <p v-if="mode === 'view'">{{ encodeIdCard(profile?.idCard || '-') }}</p>
            <van-field v-else v-model="profile!.idCard" placeholder="请输入身份证号" class="p-0!" clearable />
        </div>

        <div class="flex space-x-16px text-16px text-t-5 py-16px">
            <p class="w-72px shrink-0">联系方式</p>
            <p>{{ encodePhone(profile?.phone || '-') }}</p>
            <!-- <van-field v-else v-model="profile!.phone" placeholder="请输入联系方式" class="p-0!" clearable /> -->
        </div>
    </div>
</template>

<style scoped>
:deep(.van-uploader__preview-image) {
    width: 56px;
    height: 56px;
    border-radius: 4px;
}

:deep(.van-image__img) {
    border-radius: 4px;
}
</style>
