<script setup lang="ts">
import { Checkbox } from 'vant'
import 'vant/es/checkbox/style'

const props = defineProps<{
    member: Partial<User>
    showRegisterTime?: boolean
    showPhone?: boolean
    showInvite?: boolean
    editable?: boolean
}>()

const parsed = computed(() => {
    return parseIDCard(props.member.idCard!)
})
</script>

<template>
    <div class="user-card bg-white rounded-4px p-16px">
        <component :is="editable ? Checkbox : 'div'" class="w-full!">
            <div flex class="w-full!">
                <van-image
                    :src="member.headImgUrl"
                    fit="cover"
                    w-40px
                    h-40px
                    mr-8px
                    round
                />
                <div flex-1>
                    <p class="text-t-5 text-16px">{{ member.name }}</p>
                    <p v-if="showRegisterTime" class="text-t-4 text-12px">注册日期: {{ formatDate(member.registerTime!, 'YYYY-MM-DD') }}</p>
                    <p v-if="showPhone" class="text-t-4 text-12px">{{ member.phone }}</p>
                </div>
                <div>
                    <div class="self-start flex items-center">
                        <span class="text-t-4 mr-8px">{{ parsed.age }} 岁 </span>
                        <span
                            v-if="parsed.gender === 'male'"
                            class="i-custom-man w-16px h-16px"
                        ></span>
                        <span v-else class="i-custom-woman w-16px h-16px"></span>
                    </div>
                    <!-- <template v-if="showInvite">
                        <van-tag
                            plain
                            type="primary"
                            round
                            class="!h-24px mt-4px !w-56px !justify-center"
                            :class="{ '!text-primary-3': user.invite }"
                        >
                            {{ user.invite ? "已应答" : "应答" }}
                        </van-tag>
                    </template> -->
                </div>
            </div>
        </component>
    </div>
</template>

<style lang="scss">
.user-card {
  .van-checkbox__label {
    width: 100%;
  }
}
</style>
