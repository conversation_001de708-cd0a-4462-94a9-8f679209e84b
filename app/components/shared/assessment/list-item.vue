<script setup lang="ts">
const { image, title } = defineProps<{
    image: string
    title: string
    questionData: any
    participantsAvatar: string[]
    participantsCount?: number
}>()
</script>

<template>
    <div>
        <div p-16px bg-white rd-4px>
            <div flex gap-8px>
                <van-image :src="image" object-cover w-64px h-64px rd-4px overflow-hidden alt="">
                    <template #error>
                        <span text-10px>暂无封面</span>
                    </template>
                </van-image>
                <p><span text="16px" font-500>{{ title }}</span></p>
            </div>

            <div flex mt-16px justify-between>
                <div v-if="participantsCount" flex items-center inline-block>
                    <img
                        v-for="(n, index) in participantsAvatar.slice(0, 2)" :key="index"
                        :src="n"
                        border="2px solid white"
                        class="-ml-6px w-22px h-22px object-cover rounded-full relative inline-flex"
                    />

                    <span text="primary-6" ml-8px text-14px>
                        {{ participantsCount }}人参与
                    </span>
                </div>

                <template v-if="questionData.reportInterpretationId">
                    <nuxt-link :to="`/user/survey/consent?surveyId=${questionData.questionId}&resultId=${questionData.id}`">
                        <van-button round size="small" type="primary" class="!w-88px border-none! bg-primary-1! text-primary-6!">重新评估</van-button>
                    </nuxt-link>

                    <nuxt-link :to="`/user/assessment/${questionData.reportInterpretationId}`">
                        <van-button round size="small" type="primary" class="!w-88px">评估结果</van-button>
                    </nuxt-link>
                </template>

                <template v-else>
                    <nuxt-link :to="`/user/survey/consent?surveyId=${questionData.questionId}`">
                        <van-button round size="small" type="primary" class="!w-88px">立即评估</van-button>
                    </nuxt-link>
                </template>
            </div>
        </div>
    </div>
</template>
