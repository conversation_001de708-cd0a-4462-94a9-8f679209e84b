<script setup lang="ts">
const { mode = 'view', min = 1, max = 100 } = defineProps<
    {
        mode?: 'edit' | 'view'
        min?: number
        max?: number
    }
>()

const count = defineModel<number>('count', {
    default: 1,
})
</script>

<template>
    <div v-show="mode === 'edit'" flex items-center gap-5px>
        <div
            class="i-radix-icons-minus w-16px h-16px" :class="count === min ? 'text-t-2' : 'text-t-5'"
            @click="count = Math.max(count - 1, min)"
        ></div>

        <div bg-fill-1 text-center leading-24px text-t-5 w-32px h-24px>
            {{ count }}
        </div>

        <div
            class="i-radix-icons-plus w-16px h-16px" :class="count === max ? 'text-t-2' : 'text-t-5'"
            @click="count = Math.min(count + 1, max)"
        ></div>
    </div>

    <div v-show="mode === 'view'" text-t-4>
        x{{ count }}
    </div>
</template>
