<script setup lang="ts">
import destr from 'destr'
import { Model } from 'survey-core'
import ID from 'idcard'

import 'survey-core/survey.i18n'
import '@/utils/survey'
import 'survey-core/defaultV2.min.css'
import '@/components/survey'

const {
    content,
    mode = 'edit',
} = defineProps<{
    content: string
    result?: string
    mode?: 'display' | 'edit'
}>()

const emit = defineEmits<{
    (e: 'submit', data: Record<string, string>): void
}>()

const totalPage = ref(0)
const currentPageIndex = ref(1)
const router = useRouter()
const survey = shallowRef<Model | undefined>()

const { userInfo } = useUserStore()

function mergeElements(pages: any) {
    if (!pages || pages.length === 0) return

    // 从第一个页面获取 elements 数组
    const firstPageElements = pages[0].elements

    // 从第二个页面开始，将每个页面的 elements 合并到第一个页面的 elements 中
    for (let i = 1; i < pages.length; i++) {
        firstPageElements.push(...pages[i].elements)
    }

    return pages
}

function isIdCardValid(_: any, options: any) {
    if (options.name === 'archive_idCard') {
        if (!ID.verify(options.value)) {
            options.error = '请输入正确的身份证号'
        }
    }
}

const currentPageJsonObj = ref<any>()
const gender = ref<string>('1')

let skipedQuestionIds: string[] = []

function addSkipBtn(questionId: string) {
    const nextButton = document.querySelector('#sv-nav-next')
    const div = document.createElement('div')
    div.innerHTML = '不清楚，跳过此题'
    div.setAttribute('id', `sv-skip-${questionId}`)
    div.style.cssText = `
                    color: #00AC97;
                    width: 241PX;
                    font-size: 15PX;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-weight: 500;
                    text-align: center;
                    margin-top: 16PX;
                    height: 50PX;
                    border: 1PX solid #00AC97;
                    border-radius: 56PX;
                `
    div.addEventListener('click', () => {
        skipedQuestionIds.push(questionId)
        survey.value?.nextPage()
    })
    nextButton?.appendChild(div)
}

function removeSkipBtn(questionId: string) {
    const existingSkipButton = document.querySelector(`#sv-skip-${questionId}`)
    if (existingSkipButton) {
        existingSkipButton.remove()
    }
}

async function renderSurvey(questionResult?: string) {
    const _content = destr<Record<string, any>>(content)
    // 禁止下一题自动跳转
    _content.goNextPageAutomatic = false
    totalPage.value = _content.pages.length
    // _content.showProgressBar = 'bottom'
    // _content.progressBarType = 'pages'
    // _content.showPrevButton = false
    // _content.progressBarShowPageNumbers = true

    if (mode === 'display') {
        _content.pages = mergeElements(_content.pages)
    }

    survey.value = new Model(_content)
    survey.value.mode = mode
    if (questionResult) {
        survey.value.data = destr<Record<string, any>>(questionResult)
    }
    // survey.value.showNavigationButtons = false
    survey.value.showPrevButton = false
    survey.value.locale = 'zh-cn'

    // 自动填充性别年龄
    const currentPage = survey.value.currentPage

    if (currentPage.jsonObj.name === '页面1' && currentPage.jsonObj.elements[0].title === '性别') {
        autoFill(userInfo!)
    }

    if (currentPage.jsonObj.name === 'demographics') {
        autoFill(userInfo!, 'word')
    }

    survey.value.showCompletedPage = false

    survey.value.onCurrentPageChanged.add(async (sender, options: any) => {
        try {
            currentPageIndex.value = options.newCurrentPage.num
            currentPageJsonObj.value = options.newCurrentPage.jsonObj
            gender.value = sender.data.gender

            // 自动填充性别年龄
            if (options.newCurrentPage.jsonObj.name === '页面1' && options.newCurrentPage.jsonObj.elements[0].title === '性别') {
                if (sender.data.archive_idCard) {
                    autoFill({
                        idCard: sender.data.archive_idCard,
                    } as any)
                }
            }

            if (options.newCurrentPage.jsonObj.name === 'waist-page') {
                await nextTick()
                skipedQuestionIds = skipedQuestionIds.filter(id => id !== 'waist')
                addSkipBtn('waist')
            }
            else {
                removeSkipBtn('waist')
            }

            if (options.newCurrentPage.jsonObj.name === 'fat-rate-page') {
                await nextTick()
                skipedQuestionIds = skipedQuestionIds.filter(id => id !== 'fat_rate')
                addSkipBtn('fat_rate')
            }
            else {
                removeSkipBtn('fat_rate')
            }
        } catch (error) {
            console.log(error)
        }
    })

    survey.value.onValidateQuestion.add(isIdCardValid)

    survey.value.onComplete.add((sender, options: any) => {
        const data = sender.data
        skipedQuestionIds.forEach((id) => {
            data[id] = ''
        })
        emit('submit', data)
    })
}

function autoFill(_userInfo: HostProfileForm, type: 'word' | 'number' = 'number') {
    if (!_userInfo) return

    const autoFillData: Record<string, any> = {
        name: _userInfo.name,
    }

    if (_userInfo.idCard) {
        const ageAndGender = parseIDCard(_userInfo.idCard || '')

        if (ageAndGender.gender) {
            if (type === 'word') {
                autoFillData.gender = ageAndGender.gender === 'male' ? '男' : '女'
            }
            else {
                autoFillData['1'] = ageAndGender.gender === 'male' ? '1' : '2'
            }
        }

        if (ageAndGender.age) {
            if (type === 'word') {
                autoFillData.age = ageAndGender.age
            }
            else {
                autoFillData['2'] = ageAndGender.age
            }
        }
    }
    else {
        if (_userInfo.age) {
            if (type === 'word') {
                autoFillData.age = _userInfo.age
            }
            else {
                autoFillData['2'] = _userInfo.age
            }
        }

        if (_userInfo.gender) {
            if (type === 'word') {
                autoFillData.gender = _userInfo.gender
            }
            else {
                autoFillData['1'] = _userInfo.gender === '男' ? '1' : '2'
            }
        }
    }

    survey.value!.data = {
        ...survey.value!.data,
        ...autoFillData,
    }
}

defineExpose({
    renderSurvey,
})

async function handlePrev() {
    if (currentPageIndex.value === 1) {
        router.go(-1)
    }
    else {
        survey.value?.prevPage()
    }
}

const debouncedProgressValue = ref(0)

// 使用防抖函数包装进度值更新
const updateProgress = useDebounceFn(() => {
    debouncedProgressValue.value = survey.value?.progressValue || 0
}, 300)

const percentage = computed(() => {
    // 监听原始进度值变化
    if (survey.value?.progressValue !== undefined) {
        updateProgress()
    }
    return debouncedProgressValue.value
})

if (import.meta.hot) {
    if (content)
        renderSurvey()
}
</script>

<template>
    <div class="h-screen overflow-auto relative bg-#fff">
        <div v-if="mode === 'edit'" absolute h-50PX z-100 px-16PX text-t-4 w-full flex items-end bg-white>
            <div justify-between items-center flex gap-8PX w-full>
                <div flex items-center>
                    <span class="i-radix-icons:chevron-left w-16PX h-16PX"></span>
                    <span @click="handlePrev">
                        {{ currentPageIndex === 1 ? '返回' : '上一题' }}
                    </span>
                </div>

                <van-progress flex-1 stroke-width="12" :show-pivot="false" :percentage="percentage" />

                <!-- <span>
                    {{ `${percentage}%` }}
                </span> -->
            </div>
        </div>

        <!-- <div class="w-full h-full flex flex-col items-center">
            <div class="i-custom:check-circle-fill h-64px w-64px"></div>
            <div class="text-t-4 text-15px">
                问卷题目已全部完成
            </div>
        </div> -->

        <survey-component v-if="survey" :model="survey" />
    </div>
</template>

<style lang="scss">
.sd-root-modern {
    background: #F4F5F700;

    .sd-element--with-frame {
        background: #F4F5F700;
        box-shadow: none;
        padding: 0;
    }

    .sd-body--static {
        height: calc(100vh - 32px - constant(safe-area-inset-bottom));
        height: calc(100vh - 32px - env(safe-area-inset-bottom));
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        padding-bottom: 0;
    }

    .sd-body__page {
        padding-top: 60PX !important;
        display: flex;
        flex-direction: column;
        padding-bottom: 120px !important;
        // gap: 25PX;
    }

    .sv-title-icon {
        position: relative;
        left: 50%;
        transform: translateX(-50%);
        margin-bottom: 10px;
    }

    .sd-description {
        text-align: center;
        font-size: 12PX;
    }

    .sv-title-actions__title,
    .sd-title {
        width: 100%;
        text-align: center;

        .sv-string-viewer {
            font-weight: 600;
            font-size: 20PX;
            margin: 0 20PX;
        }
    }

    .sd-item__control-label {
        .sv-string-viewer {
            display: flex;
            align-items: center;
            font-size: 15PX;
            font-weight: 500;
            justify-content: center;
            gap: 8px;
            margin: 0 20PX;
        }
    }

    .sd-input {
        box-shadow: none;
        background: rgb(var(--fill-1));

        &:not(.sd-comment) {
            border-radius: 56PX;
        }
    }

    // 选择题

    .sd-selectbase {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 14PX;
        font-size: 15PX;
    }

    .sd-selectbase[data-title="基于医生的诊断，您是否有以下疾病"],
    .sd-selectbase[data-title="日常运动项目？（多选）"],
    .sd-selectbase[data-title="自身是否有以下健康问题？（多选）"],
    .sd-selectbase[data-title="是否有以下饮食行为？（多选）"] {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }

    .sd-selectbase__item {
        border-radius: 56PX;
        text-align: center;
        width: 300PX;
        padding: 13PX 0;
    }

    .sd-input {
        font-size: 15PX;
        font-weight: 500;
    }

    .sd-input {
        width: 300PX;
    }

    .sd-question--left .sd-input {
        width: 235PX;
    }

    @media screen and (max-width: 375px) {
        .sd-question--left .sd-input {
            width: 195PX;
        }
    }

    .sd-input[type=date] {
        width: 200PX;
    }

    .sd-question__content {
        display: flex;
        justify-content: center;
    }

    .sd-question--left .sd-question__content {
        justify-content: flex-end;
    }

    .sd-selectbase__label {
        width: 100%;
    }

    .sd-item--checked {
        background: rgb(var(--primary-6));
        border-radius: 56PX;

        .sd-item__control-label {
            color: white;
        }
    }

    .sd-item__control-label {
        text-align: center;
    }

    .sd-item--allowhover {
        background: rgb(var(--fill-1));
    }

    .sd-item__decorator,
    .sd-radio__decorator {
        display: none;
    }

    // 单位
    .sd-input-group-addon {
        position: absolute;
        right: 20PX;
        top: 13PX;
        color: #1D2129;
        font-size: 16PX;
        font-weight: 600;
    }

    // 按钮
    .sd-navigation__complete-btn,
    .sd-navigation__next-btn {
        background: rgb(var(--primary-6));
        border-radius: 56PX;
        color: white;
        font-size: 15PX;
        padding: 13PX 20PX;
        box-shadow: none;
        width: 240PX;
    }
}

.sd-root-modern.sd-root-modern--full-container {
    overflow-x: hidden;
    -ms-overflow-style: none;
    scrollbar-width: none;

    &::-webkit-scrollbar {
        display: none
    }
}

.sv-page-transition-enter-active,
.sv-page-transition-leave-active {
    transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
}

.sv-page-transition-enter-from,
.sv-page-transition-leave-to {
    opacity: 0;
    //   transform: translateX(20PX);
}

.sv-page-transition-enter-to,
.sv-page-transition-leave-from {
    opacity: 1;
    //   transform: translateX(0);
}

.sd-element--with-frame.sd-question--left>.sd-element__erbox--above-element {
    margin-left: unset;
    margin-right: unset;
    margin-bottom: unset;
}

.sd-element--with-frame:not(.sd-question--left)>.sd-element__erbox {
    width: unset;
    margin-left: unset;
    margin-right: unset;
}

.sd-question__required-text {
    display: none;
}

.sd-expression {
    width: 100%;
}

.sv-question-bottom-image {
    margin-top: 40PX;
}

img[src="/images/wine.png"] {
    width: 280px;
}

img[src="/images/sit-long.png"] {
    width: 327px;
}

.sv-components-container-contentBottom {
    position: fixed;
    background: white;
    bottom: 0;
    width: 100%;
    box-shadow: 0px -4px 12px 0px rgba(0, 0, 0, 0.06);
}
</style>
