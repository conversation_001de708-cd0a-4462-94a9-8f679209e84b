<script setup lang="ts">
const { base = '75px' } = defineProps<{
    base?: string
}>()

const height1 = computed(() => {
    return `calc(${base} + constant(safe-area-inset-bottom))`
})

const height2 = computed(() => {
    return `calc(${base} + env(safe-area-inset-bottom))`
})
</script>

<template>
    <div class="safe-buttom">
    </div>
</template>

<style scoped>
.safe-buttom {
  height: v-bind(height1);
  height: v-bind(height2);
}
</style>
