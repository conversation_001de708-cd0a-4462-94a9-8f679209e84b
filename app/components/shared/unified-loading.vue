<script setup lang="ts">
export interface UnifiedLoadingProps {
    size?: 'small' | 'medium' | 'large'
    rainbow?: boolean
    text?: string
    vertical?: boolean
    overlay?: boolean
    overlayColor?: string
}

const {
    size = 'large',
    rainbow = true,
    text = '',
    vertical = true,
    overlay = false,
    overlayColor = 'rgba(0, 0, 0, 0.5)',
} = defineProps<UnifiedLoadingProps>()

const sizeConfig = computed(() => {
    const configs = {
        small: { container: '20px', square: '10px', dotSize: '5px', border: '1.5px', margin: '2.5px', borderExpanded: '2.5px' },
        medium: { container: '32px', square: '16px', dotSize: '8px', border: '2.5px', margin: '4px', borderExpanded: '4px' },
        large: { container: '40px', square: '20px', dotSize: '10px', border: '3px', margin: '5px', borderExpanded: '5px' },
    }
    return configs[size] || configs.large
})

// overlay模式下的文字样式
const textClass = computed(() => [
    vertical ? 'mt-12px' : 'ml-12px',
    rainbow ? 'rainbow-text' : (overlay ? 'text-white' : 'text-#666'),
])
</script>

<template>
    <div
        class="loading-container" :class="[
            vertical ? 'flex-col' : 'flex-row',
            rainbow ? 'rainbow-root' : '',
            overlay ? 'loading-overlay' : '',
        ]"
        :style="overlay ? {
            backgroundColor: overlayColor,
            position: 'fixed',
            top: '0',
            left: '0',
            width: '100vw',
            height: '100vh',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            zIndex: '9999',
        } : {}"
    >
        <div
            class="loading-animation"
            :style="{ width: sizeConfig.container, height: sizeConfig.container }"
        >
            <div
                v-for="i in 4"
                :key="i"
                class="loading-square"
                :class="`square-${i}`"
                :style="{
                    'width': sizeConfig.square,
                    'height': sizeConfig.square,
                    '--dot-size': sizeConfig.dotSize,
                    '--dot-border': sizeConfig.border,
                    '--dot-margin': sizeConfig.margin,
                    '--dot-border-expanded': sizeConfig.borderExpanded,
                }"
            ></div>
        </div>

        <div
            v-if="text"
            class="loading-text"
            :class="textClass"
        >
            {{ text }}
        </div>
    </div>
</template>

<style scoped>
@import '@/assets/styles/loading.css';
</style>
