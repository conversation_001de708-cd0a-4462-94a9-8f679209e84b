<script setup lang="ts">
import { onUnmounted, watch } from 'vue'

const scanVisible = defineModel<boolean>('visible')

const scanTexts = ref<{ text: string, duration: number }[]>([])
const scanTextIndex = ref(-1)
const dots = ref('')

let timer: NodeJS.Timeout | null = null
let dotsTimer: NodeJS.Timeout | null = null

// 监听文字变化，重置点的动画
watch(scanTextIndex, () => {
    dots.value = ''
})

const isUploading = inject<Ref<boolean>>('isUploading')!

whenever(isUploading, () => {
    scanTextIndex.value = 0
    scanTexts.value = [
        {
            text: '正在上传图片',
            duration: Math.floor(Math.random() * 1000 + 3000),
        },
        {
            text: '正在识别食物种类与热量',
            duration: Math.floor(Math.random() * 1000 + 5000),
        },
        {
            text: '正在识别营养元素数据',
            duration: Math.floor(Math.random() * 1000 + 5000),
        },
        {
            text: '正在汇总结果',
            duration: Infinity,
        },
    ]
    // 更新加载点动画
    dotsTimer = setInterval(() => {
        dots.value = dots.value === '...' ? '' : `${dots.value}.`
    }, 500)

    // 根据 duration 更新文本
    const updateText = () => {
        if (scanTextIndex.value < scanTexts.value.length - 1) {
            scanTextIndex.value++
            timer = setTimeout(updateText, scanTexts.value[scanTextIndex.value]!.duration)
        }
    }

    timer = setTimeout(updateText, scanTexts.value[0]!.duration)
}, { immediate: true })

onUnmounted(() => {
    if (timer) {
        clearTimeout(timer)
    }
    if (dotsTimer) {
        clearInterval(dotsTimer)
    }
})
</script>

<template>
    <van-overlay v-if="scanVisible" :show="scanVisible" class="flex justify-center items-center">
        <div class="scan-container">
            <div class="text-16px text-center gap-3px flex text-white font-600">
                <div>
                    {{ scanTexts[scanTextIndex]?.text }}
                </div>
                <div class="loading-dots text-left w-20px">{{ dots }}</div>
            </div>
            <div class="scan-content scanning-animation">
            </div>
        </div>
    </van-overlay>
</template>

<style scoped>
.scan-container {
    background-image: url('@/assets/images/background/scan.svg');
    background-size: 100% 100%;
    width: 243px;
    height: 390px;
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.scan-content {
    width: 100%;
    height: 0;
    background: linear-gradient(180deg, rgba(0, 219, 201, 0.5) 0%, rgba(0, 172, 151, 0) 100%);
    position: absolute;
    bottom: 0;
}

@keyframes scanning {
    0% {
        height: 20%;
        opacity: 0.8;
    }
    45% {
        height: 100%;
        opacity: 0.5;
    }
    55% {
        height: 100%;
        opacity: 0.5;
    }
    100% {
        height: 20%;
        opacity: 0.8;
    }
}

.scanning-animation {
    animation: scanning 3s ease-in-out infinite;
}
</style>
