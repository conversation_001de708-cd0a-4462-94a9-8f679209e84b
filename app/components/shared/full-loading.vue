<script setup lang="ts">
export interface FullLoadingProps {
    type?: 'square' | 'circle'
    overlayColor?: string
    overlay?: boolean
}

const {
    type = 'circle',
    overlayColor = 'rgba(0, 0, 0, 0.5)',
    overlay = false,
} = defineProps<FullLoadingProps>()

// 根据type决定显示文本和是否启用彩虹效果
const loadingConfig = computed(() => {
    return type === 'square'
        ? { text: '问卷填写完成，结果生成中', rainbow: true }
        : { text: '加载中...', rainbow: false }
})
</script>

<template>
    <div class="absolute top-0 left-0 w-100vw h-100vh flex items-center justify-center">
        <shared-unified-loading
            size="large"
            :rainbow="loadingConfig.rainbow"
            :text="loadingConfig.text"
            vertical
            :overlay="overlay"
            :overlay-color="overlayColor"
        />
    </div>
</template>
