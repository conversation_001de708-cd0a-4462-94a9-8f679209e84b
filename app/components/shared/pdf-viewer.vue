<script setup lang="ts">
import VuePdfEmbed from 'vue-pdf-embed'

const props = defineProps<{
    src: string
}>()

const isRendered = ref(false)

const url = ref('')

onMounted(async () => {
    // url.value = `${window.location.origin}/api/v1/file/preview?objectName=${props.src}`
    // url.value = 'https://test.slmc.top/api/v1/file/preview?objectName=report/c02b8957-2964-40d7-8b6d-d87393807222.pdf'
    // isRendered.value = true

    try {
        const response = await fetch(`/api/v1/file/preview?objectName=${props.src}`)
        const blob = await response.blob()
        url.value = URL.createObjectURL(blob)
        isRendered.value = true
    } catch (error) {
        console.error('PDF加载失败:', error)
    }
})
</script>

<template>
    <div v-show="isRendered" v-if="url" class="h-[calc(100vh-30px)] relative scrollbar-hide overflow-auto">
        <vue-pdf-embed
            :source="url"
            :rendering-failed="(err: any) => {
                console.log(err)
            }"
            :scale="1"
            class="pdf-container"
            @rendered="() => {
                isRendered = true
            }"
        />

        <div class="fixed  bottom-0 flex justify-center gap-5px items-center left-0 right-0 bg-white/80 text-13px py-20px">
            <div class="i-radix-icons:info-circled text-gray-4"></div>
            <div class="text-gray-7">
                双指捏合或张开可缩放 pdf 大小
            </div>
        </div>
    </div>

    <shared-full-loading v-show="!isRendered" />
</template>

<style scoped>
:deep(.vue-pdf-embed__page) {
    margin-bottom: 16px; /* 每页PDF之间添加16px的间距 */
}
</style>
