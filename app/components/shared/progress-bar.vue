<script setup lang="ts">
const { percent = 0 } = defineProps<{
    percent: number
}>()
</script>

<template>
    <div flex items-center gap-8px>
        <div flex-1 relative flex h-12px>
            <div class="h-full w-full bg-fill-2 rounded-full"></div>
            <div class="h-full bg-primary-6 rounded-full absolute top-0 left-0 transition-all duration-300" :style="{ width: `${percent * 100}%` }"></div>
        </div>
        <span text-primary-6>{{ Math.floor(percent * 100) }}%</span>
    </div>
</template>
