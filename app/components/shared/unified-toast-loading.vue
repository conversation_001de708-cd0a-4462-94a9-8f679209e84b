<script setup lang="ts">
export interface UnifiedToastLoadingProps {
    message?: string
    forbidClick?: boolean
    duration?: number
    size?: 'small' | 'medium' | 'large'
    overlay?: boolean
    onClose?: () => void
}

const {
    message = '加载中...',
    forbidClick = true,
    duration = 0,
    size = 'large',
    overlay = true,
    onClose,
} = defineProps<UnifiedToastLoadingProps>()

const visible = ref(true)

const sizeConfig = computed(() => {
    const configs = {
        small: { container: '20px', square: '10px', dotSize: '5px', border: '1.5px', margin: '2.5px', borderExpanded: '2.5px' },
        medium: { container: '32px', square: '16px', dotSize: '8px', border: '2.5px', margin: '4px', borderExpanded: '4px' },
        large: { container: '40px', square: '20px', dotSize: '10px', border: '3px', margin: '5px', borderExpanded: '5px' },
    }
    return configs[size] || configs.large
})

let autoCloseTimer: NodeJS.Timeout | null = null

function close() {
    visible.value = false
    if (autoCloseTimer) {
        clearTimeout(autoCloseTimer)
        autoCloseTimer = null
    }
    onClose?.()
}

if (duration > 0) {
    autoCloseTimer = setTimeout(() => {
        close()
    }, duration)
}

defineExpose({ close })

onUnmounted(() => {
    if (autoCloseTimer) {
        clearTimeout(autoCloseTimer)
    }
})
</script>

<template>
    <teleport to="body">
        <transition name="toast-fade">
            <div
                v-if="visible"
                class="unified-toast-loading"
                :class="{
                    'pointer-events-none': !forbidClick,
                    'pointer-events-auto': forbidClick,
                }"
            >
                <div
                    v-if="overlay"
                    class="fixed inset-0 z-50"
                    :class="{
                        'pointer-events-none': !forbidClick,
                        'pointer-events-auto': forbidClick,
                    }"
                ></div>

                <div class="fixed inset-0 z-50 flex items-center justify-center pointer-events-none">
                    <div class="unified-toast-content flex flex-col items-center justify-center rainbow-root">
                        <div
                            class="loading-animation mb-8px"
                            :style="{ width: sizeConfig.container, height: sizeConfig.container }"
                        >
                            <div
                                v-for="i in 4"
                                :key="i"
                                class="loading-square"
                                :class="`square-${i}`"
                                :style="{
                                    'width': sizeConfig.square,
                                    'height': sizeConfig.square,
                                    '--dot-size': sizeConfig.dotSize,
                                    '--dot-border': sizeConfig.border,
                                    '--dot-margin': sizeConfig.margin,
                                    '--dot-border-expanded': sizeConfig.borderExpanded,
                                }"
                            ></div>
                        </div>

                        <div class="text-14px text-center leading-20px rainbow-text">
                            {{ message }}
                        </div>
                    </div>
                </div>
            </div>
        </transition>
    </teleport>
</template>

<style scoped>
@import '@/assets/styles/loading.css';
</style>
