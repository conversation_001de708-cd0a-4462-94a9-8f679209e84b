import male from '~/assets/images/common/male-2.png'
import female from '~/assets/images/common/female-2.png'

export function formatDate(date: Date | number | string, format = 'YYYY-MM-DD HH:mm:ss') {
    const dayjs = useDayjs()
    if (!date) {
        return ''
    }

    return dayjs(date).format(format)
}

export function formatPrice(price: string | number) {
    return new Intl.NumberFormat('zh-CN', { style: 'currency', currency: 'CNY' }).format(Number(price))
}

export function formatResource(id: string) {
    return `/api/v1/file/download?objectName=${id}`
}

export function formatPreviewResource(id: string) {
    return `/api/v1/file/preview?objectName=${id}`
}

export function formatHeadImage(url: string, gender?: 'male' | 'female') {
    try {
        new URL(url)
        if (url.startsWith('https://thirdwx.qlogo.cn')) {
            if (gender === 'male') {
                return male
            }
            return female
        }
        return url
    }
    catch (error) {
        return formatResource(url)
    }
}
