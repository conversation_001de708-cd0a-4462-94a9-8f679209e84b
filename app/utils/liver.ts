// 定义接口类型
interface LabResult {
    item: string
    result: string
}

interface LabResults {
    altAst?: number
    astAlt?: number
    fastingBloodSugar?: number
    triglyceride?: number
    platelets?: number
    albumin?: number
}

// 提取常量配置
const LAB_KEYWORDS = {
    alt: ['丙氨酸氨基转移酶(alt)', '丙氨酸氨基转移酶', 'alt'],
    ast: ['天门冬氨基转移酶(ast)', '天门冬氨基转移酶', 'ast'],
    platelets: ['血小板(plt)', '血小板', 'plt'],
    albumin: ['血蛋白', '白蛋白(alb)', '白蛋白', 'alb'],
    fastingBloodSugar: ['空腹血糖', 'fpg', '葡萄糖测定（空腹）', '空腹葡萄糖'],
    triglyceride: ['甘油三酯(tg)', '甘油三酯', 'tg'],
} as const

// 工具函数：标准化项目名称
function normalizeItemName(name: string): string {
    return name.toLowerCase().replace(/\s+/g, '')
}

// 工具函数：安全的数值转换
function safeNumberConversion(value: string): number | undefined {
    const num = Number(value)
    return Number.isNaN(num) ? undefined : num
}

// 工具函数：计算比值并保留3位小数
function calculateRatio(numerator: number, denominator: number): number {
    return Number((numerator / denominator).toFixed(3))
}

export function parseLabResults(items: LabResult[]): LabResults {
    const result: LabResults = {}
    const tempValues = {
        alt: undefined as number | undefined,
        ast: undefined as number | undefined,
        altAstRatio: undefined as number | undefined,
        astAltRatio: undefined as number | undefined,
    }

    // 处理每个检验项目
    items.forEach(({ item: itemName, result: itemResult }) => {
        const normalizedName = normalizeItemName(itemName)
        const numericValue = safeNumberConversion(itemResult)

        if (!numericValue) return

        // 处理直接的比值
        if (normalizedName.includes('alt/ast')) {
            tempValues.altAstRatio = numericValue
            result.altAst = numericValue
        }
        else if (normalizedName.includes('ast/alt')) {
            tempValues.astAltRatio = numericValue
            result.astAlt = numericValue
        }
        // 处理单独的值
        else {
            processIndividualValue(normalizedName, numericValue, result, tempValues)
        }
    })

    // 计算最终的比值
    calculateFinalRatios(result, tempValues)

    return result
}

function processIndividualValue(
    normalizedName: string,
    value: number,
    result: LabResults,
    tempValues: { alt?: number, ast?: number },
): void {
    if (LAB_KEYWORDS.alt.some(keyword => normalizedName.includes(keyword))) {
        tempValues.alt = value
    }
    else if (LAB_KEYWORDS.ast.some(keyword => normalizedName.includes(keyword))) {
        tempValues.ast = value
    }
    else if (LAB_KEYWORDS.fastingBloodSugar.some(keyword => normalizedName.includes(keyword))) {
        result.fastingBloodSugar = value
    }
    else if (LAB_KEYWORDS.triglyceride.some(keyword => normalizedName.includes(keyword))) {
        result.triglyceride = value
    }
    else if (LAB_KEYWORDS.platelets.some(keyword => normalizedName.includes(keyword))) {
        result.platelets = value
    }
    else if (LAB_KEYWORDS.albumin.some(keyword => normalizedName.includes(keyword))) {
        result.albumin = value
    }
}

function calculateFinalRatios(
    result: LabResults,
    tempValues: {
        alt?: number
        ast?: number
        altAstRatio?: number
        astAltRatio?: number
    },
): void {
    const { alt, ast, altAstRatio, astAltRatio } = tempValues

    // 计算 ALT/AST 比值
    if (!result.altAst) {
        if (astAltRatio && astAltRatio !== 0) {
            result.altAst = calculateRatio(1, astAltRatio)
        }
        else if (alt && ast && ast !== 0) {
            result.altAst = calculateRatio(alt, ast)
        }
    }

    // 计算 AST/ALT 比值
    if (!result.astAlt) {
        if (altAstRatio && altAstRatio !== 0) {
            result.astAlt = calculateRatio(1, altAstRatio)
        }
        else if (alt && ast && alt !== 0) {
            result.astAlt = calculateRatio(ast, alt)
        }
    }
}
