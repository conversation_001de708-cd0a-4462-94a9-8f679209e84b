import Compressor from 'compressorjs'

interface CompressOptions {
    quality?: number
    maxWidth?: number
    maxHeight?: number
    mimeType?: string
}

export function compressImage(file: File, options: CompressOptions = {}): Promise<File> {
    return new Promise((resolve, reject) => {
        new Compressor(file, {
            quality: options.quality || 0.5,
            mimeType: options.mimeType || 'image/jpeg',
            success: (result) => {
                // 保持原始文件名
                const compressedFile = new File([result], file.name, {
                    type: result.type,
                    lastModified: new Date().getTime(),
                })
                resolve(compressedFile)
            },
            error: (err) => {
                reject(err)
            },
        })
    })
}
