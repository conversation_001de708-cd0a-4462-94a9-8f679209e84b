import { ComponentCollection } from 'survey-core'

ComponentCollection.Instance.add({
    name: '报告上传',
    title: '报告上传',
    elementsJSON: [
        {
            type: 'text',
            name: 'REPORT_NAME',
            title: '报告名称',
        },
        {
            type: 'text',
            name: 'REPORT_TIME',
            startWithNewLine: false,
            title: '报告时间',
            inputType: 'datetime-local',
        },
        {
            type: 'file',
            name: 'REPORT_IMAGE',
            title: '报告图片',
            acceptedTypes: 'image/jpg,image/png',
            maxSize: 10485760,
            sourceType: 'file-camera',
            storeDataAsText: false,
        },
    ],
})
