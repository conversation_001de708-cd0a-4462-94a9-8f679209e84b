import { FunctionFactory } from 'survey-core'

function calcEthanol(params: any) {
    const type = params[0] as number
    const value = params[1] as number

    // 啤酒25:1
    // 红酒10:1
    // 白酒5:2

    switch (type) {
        case 1:
            return Math.round(value / 25)
        case 2:
            return Math.round(value / 10)
        case 3:
            return Math.round(value / 2.5)
        case 4:
            return Math.round(value / 2.5)
        default:
            return 0
    }
}

FunctionFactory.Instance.register('calcEthanol', calcEthanol)
