import { jointMeta } from '~/components/survey/pain-joint/meta'
import { suggestMeta } from './suggests'

export interface SurveyDetail {
    title: string
    type: string
    result: string
    unit: string
    suggestion: string
    comment: string
    label: string
    specialType: string | undefined
}

export async function useSurveyDetail(surveyId: number, resultId: number, surveyType: 'preliminary' | 'secondary' = 'preliminary') {
    if (!surveyId || !resultId) return []

    const [questionMetaResponse, questionResultResponse] = await Promise.all([
        useWrapFetch<BaseResponse<QuestionMeta>>(`/question/get/${surveyId}`),
        useWrapFetch<BaseResponse<QuestionResult>>(`/question-result/${resultId}`),
    ])

    const questionMeta = questionMetaResponse.results
    const questionResult = questionResultResponse.results

    const surveyList: SurveyDetail[] = []

    const questionContent = JSON.parse(questionMeta.content)
    const parsedResult = JSON.parse(questionResult.questionResult)

    const surveySuggest = suggestMeta[surveyType]

    questionContent.pages.forEach((page: any) => {
        const { elements } = page
        elements.forEach((element: any) => {
            const { name, title, type, choices, unit, 'special-type': specialType } = element
            const resultKey = parsedResult[name]

            let suggestion = ''
            let comment = ''
            let label = ''
            if (surveySuggest?.[name]) {
                const findSuggestList = surveySuggest[name]
                const suggests: any[] = []
                findSuggestList.forEach((_suggest: any) => {
                    if (_suggest.condition === 'gte' && Number.parseFloat(resultKey) >= Number.parseFloat(_suggest.value)) {
                        suggests.push(_suggest)
                    }
                    else if (_suggest.condition === 'eq' && (resultKey === _suggest.value || resultKey?.includes(_suggest.value))) {
                        suggests.push(_suggest)
                    } else if (_suggest.condition === 'not_include' && !resultKey.includes(_suggest.value)) {
                        suggests.push(_suggest)
                    }

                    return ''
                })

                if (suggests.length > 0) {
                    suggestion = suggests.map((_suggest: any) => _suggest.suggest).join('<br>')
                    comment = suggests.map((_suggest: any) => _suggest.comment).join('<br>')
                    label = suggests.map((_suggest: any) => _suggest.label).join('<br>')
                }
            }

            if (!resultKey || resultKey?.length === 0) return
            if (type === 'radiogroup') {
                const choice = choices.find((_choice: any) => _choice.value === resultKey)

                let result = ''

                if (resultKey === 'none') {
                    result = '以上均无'
                } else {
                    result = choice.text
                }

                surveyList.push({
                    title,
                    type,
                    result: extractTextFormHtml(result),
                    unit,
                    suggestion,
                    comment,
                    specialType,
                    label,
                })
            } else if (type === 'checkbox') {
                let result = ''

                if (resultKey[0] === 'none') {
                    result = '以上均无'
                } else {
                    const choice = choices.filter((_choice: any) => resultKey.includes(_choice.value))
                    result = choice.map((_choice: any) => _choice.text).join(',')
                }

                surveyList.push({
                    title,
                    type,
                    result: extractTextFormHtml(result),
                    unit,
                    suggestion,
                    comment,
                    specialType,
                    label,
                })
            } else if (type === 'addon-pain-joint') {
                const choice = jointMeta.filter((_choice: any) => resultKey.includes(_choice.id))
                const result = choice.map((_choice: any) => _choice.name).join(',')

                surveyList.push({
                    title,
                    type,
                    result: extractTextFormHtml(result),
                    unit,
                    suggestion,
                    comment,
                    specialType,
                    label,
                })
            }
            else {
                surveyList.push({
                    title,
                    type,
                    unit,
                    result: extractTextFormHtml(resultKey),
                    suggestion,
                    comment,
                    specialType,
                    label,
                })
            }
        })
    })

    return surveyList
}
