/**
 * 二维码Logo替换工具
 */
export interface LogoArea {
    x: number
    y: number
    width: number
    height: number
    radius?: number
}

export function createImageFromBase64(base64Data: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
        const img = new Image()
        img.crossOrigin = 'anonymous'
        img.onload = () => resolve(img)
        img.onerror = reject

        if (base64Data.startsWith('data:image/')) {
            img.src = base64Data
        } else {
            img.src = `data:image/png;base64,${base64Data}`
        }
    })
}

export function detectLogoArea(canvas: HTMLCanvasElement): LogoArea {
    const { width, height } = canvas
    const logoSizeRatio = 0.45
    const logoSize = Math.min(width, height) * logoSizeRatio
    const radius = logoSize / 2

    return {
        x: (width - logoSize) / 2,
        y: (height - logoSize) / 2,
        width: logoSize,
        height: logoSize,
        radius,
    }
}

export function drawCustomLogo(
    ctx: CanvasRenderingContext2D,
    logoImage: HTMLImageElement,
    logoArea: LogoArea,
): void {
    const { x, y, width, height, radius = 0 } = logoArea
    const centerX = x + width / 2
    const centerY = y + height / 2
    const circleRadius = radius || width / 2

    ctx.save()

    // 创建圆形蒙版
    ctx.beginPath()
    ctx.arc(centerX, centerY, circleRadius, 0, 2 * Math.PI)
    ctx.clip()

    // 计算缩放让图片填满圆形区域
    const logoRatio = logoImage.width / logoImage.height
    const circleSize = circleRadius * 2

    let drawWidth, drawHeight, drawX, drawY

    if (logoRatio > 1) {
        drawHeight = circleSize
        drawWidth = circleSize * logoRatio
        drawX = centerX - drawWidth / 2
        drawY = centerY - drawHeight / 2
    } else {
        drawWidth = circleSize
        drawHeight = circleSize / logoRatio
        drawX = centerX - drawWidth / 2
        drawY = centerY - drawHeight / 2
    }

    ctx.drawImage(logoImage, drawX, drawY, drawWidth, drawHeight)
    ctx.restore()
}

export async function mergeQRCodeWithLogo(
    qrCodeBase64: string,
    logoImagePath: string,
): Promise<string> {
    try {
        const qrImage = await createImageFromBase64(qrCodeBase64)

        const logoImage = new Image()
        logoImage.crossOrigin = 'anonymous'
        await new Promise((resolve, reject) => {
            logoImage.onload = resolve
            logoImage.onerror = reject
            logoImage.src = logoImagePath
        })

        const canvas = document.createElement('canvas')
        const ctx = canvas.getContext('2d')
        if (!ctx) throw new Error('无法创建Canvas上下文')

        canvas.width = qrImage.width
        canvas.height = qrImage.height

        // 绘制原始二维码
        ctx.drawImage(qrImage, 0, 0)

        // 检测并清除logo区域
        const logoArea = detectLogoArea(canvas)
        const { x, y, width, height, radius = 0 } = logoArea
        const centerX = x + width / 2
        const centerY = y + height / 2
        const circleRadius = radius || width / 2

        ctx.save()
        ctx.fillStyle = '#FFFFFF'
        ctx.beginPath()
        ctx.arc(centerX, centerY, circleRadius, 0, 2 * Math.PI)
        ctx.fill()
        ctx.restore()

        // 绘制自定义logo
        drawCustomLogo(ctx, logoImage, logoArea)

        return canvas.toDataURL('image/png', 1.0)
    } catch (error) {
        console.error('Logo替换失败:', error)
        throw error
    }
}
