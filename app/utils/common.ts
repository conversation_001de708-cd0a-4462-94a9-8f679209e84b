import dayjs from 'dayjs'

export function genRandomStr(length: number) {
    let result = ''
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
    const charactersLength = characters.length
    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * charactersLength))
    }
    return result
}

export function parseIDCard(idCard: string): { age: number, gender: 'male' | 'female' } {
    if (!idCard) {
        return {
            age: 1,
            gender: 'male',
        }
    }
    // 解析出生日期
    const birthYear = Number.parseInt(idCard.substring(6, 10), 10)
    const birthMonth = Number.parseInt(idCard.substring(10, 12), 10) - 1 // 月份从0开始
    const birthDay = Number.parseInt(idCard.substring(12, 14), 10)

    // 获取当前日期
    const today = new Date()
    const birthDate = new Date(birthYear, birthMonth, birthDay)

    // 计算年龄
    let age = today.getFullYear() - birthDate.getFullYear()
    if (
        today.getMonth() < birthDate.getMonth()
        || (today.getMonth() === birthDate.getMonth() && today.getDate() < birthDate.getDate())
    ) {
        age--
    }

    // 解析性别
    const genderCode = Number.parseInt(idCard.substring(16, 17), 10)
    const gender = genderCode % 2 === 0 ? 'female' : 'male'

    return {
        age: age || 1,
        gender,
    }
}

export function calcBmi(weight: number | string, height: number | string) {
    const _weight = Number.parseFloat(weight.toString())
    const _height = Number.parseFloat(height.toString()) / 100
    return (_weight / _height ** 2).toFixed(2)
}

export function encodeIdCard(idCard: string) {
    return idCard.replace(/(\d{6})(\d{8})(\d{4})/, '$1********$3')
}

export function encodePhone(phone: string) {
    return phone.replace(/(\d{3})(\d{4})(\d{4})/, '$1****$3')
}

export function encodeMessage(data: any) {
    return btoa(encodeURIComponent(JSON.stringify(data)))
}

export function isOnWechatMP() {
    return (navigator.userAgent.match(/micromessenger/i) && navigator.userAgent.match(/miniprogram/i)) || window.__wxjs_environment === 'miniprogram'
}

export function getCeilSportValue(value: number) {
    return Math.ceil(value / 50) * 50
}

export function extractNumber(str: string | number): number {
    if (!str) {
        return 0
    }
    const match = str.toString().match(/[\d.]+/)
    return match ? Number(match[0]) : 0
}

export function getMealKindByTime(): DietType {
    const date = new Date()
    const hour = date.getHours()
    if (hour >= 6 && hour < 10) {
        return 'breakfast'
    }
    if (hour >= 11 && hour < 13) {
        return 'lunch'
    }
    if (hour >= 18 && hour < 19) {
        return 'dinner'
    }
    return 'snack'
}

export function extractTextFormHtml(html: string) {
    const parser = new DOMParser()
    const doc = parser.parseFromString(html, 'text/html')
    return doc.body.textContent || ''
}

export function calcBmiData(weight: number, height: number | string): { bmiValue: number, bmiStatus: string } {
    if (!weight || !height) return { bmiValue: 0, bmiStatus: '' }

    const heightNum = typeof height === 'string' ? Number.parseFloat(height) : height

    const heightInMeter = heightNum / 100

    const bmi = weight / (heightInMeter * heightInMeter)
    const bmiValue = Number.parseFloat(bmi.toFixed(1))

    let bmiStatus = 'BMI正常'
    if (bmiValue < 18.5) {
        bmiStatus = 'BMI低'
    } else if (bmiValue >= 24) {
        bmiStatus = 'BMI高'
    }

    return { bmiValue, bmiStatus }
}

export function getBmiText(bmi: number | string): string | null {
    const bmiNum = typeof bmi === 'string' ? Number(bmi) : bmi

    const bmiRanges = [
        { max: 18.5, text: '低体重' },
        { max: 24, text: '正常体重' },
        { max: 28, text: '超重' },
        { max: 32, text: '肥胖' },
        { max: Infinity, text: '重度肥胖' },
    ]

    const result = bmiRanges.find(range => bmiNum < range.max)
    return result?.text || null
}

/**
 * 生成本周（周日-周六，包含今天）的日期字符串数组
 */
function generateCurrentWeekDates(): string[] {
    const today = dayjs().startOf('day')
    const weekStart = today.subtract(today.day(), 'day')
    return Array.from({ length: 7 }, (_, index) => weekStart.add(index, 'day').format('YYYY-MM-DD'))
}

/**
 * 体重打卡状态计算
 * - 返回本周（周日-周六）7天的体重打卡状态数组
 * - weight > 0 视为已打卡（true），否则为未打卡（false）
 * - 未来日期强制返回 false
 * - 性能优化：只处理本周相关的数据，避免遍历大量历史数据
 */
export function computeWeightWeekStatus(results: Array<{ checkInDate: string, weight: number }>): boolean[] {
    try {
        const today = dayjs().startOf('day')
        const weekDates = generateCurrentWeekDates()
        const weekStart = dayjs(weekDates[0])
        const weekEnd = dayjs(weekDates[6])

        // 只过滤本周的数据，减少处理量
        const weekData = (Array.isArray(results) ? results : []).filter((d) => {
            const checkDate = dayjs(d.checkInDate)
            return checkDate.isSameOrAfter(weekStart) && checkDate.isSameOrBefore(weekEnd)
        })

        const weightMap = new Map(
            weekData.map(d => [dayjs(d.checkInDate).format('YYYY-MM-DD'), d.weight || 0]),
        )

        return weekDates.map((date) => {
            if (dayjs(date).isAfter(today)) return false
            const weight = weightMap.get(date) ?? 0
            return weight > 0
        })
    } catch (error) {
        console.error('computeWeightWeekStatus error:', error)
        return Array.from({ length: 7 }, () => false)
    }
}

/**
 * 饮食：mealCount.length > 0 视为 true；未来日期强制为 false
 */
export function computeDietWeekStatus(days: Array<{ day: string, mealCount: string[] | null }>): boolean[] {
    try {
        const today = dayjs().startOf('day')
        const weekDates = generateCurrentWeekDates()
        const dietMap = new Map(
            (Array.isArray(days) ? days : []).map(d => [dayjs(d.day).format('YYYY-MM-DD'), d.mealCount?.length ?? 0]),
        )
        return weekDates.map((date) => {
            if (dayjs(date).isAfter(today)) return false
            const count = dietMap.get(date) ?? 0
            return count > 0
        })
    } catch (error) {
        console.error('computeDietWeekStatus error:', error)
        return Array.from({ length: 7 }, () => false)
    }
}

/**
 * 运动打卡状态计算
 * - 返回本周（周日-周六）7天的运动打卡状态数组
 * - kcal > 0 视为已打卡（true），否则为未打卡（false）
 * - 未来日期强制返回 false
 * - 性能优化：只处理本周相关的数据，避免遍历大量历史数据
 */
export function computeSportWeekStatus(days: Array<{ checkInDate: string, kcal: number }>): boolean[] {
    try {
        const today = dayjs().startOf('day')
        const weekDates = generateCurrentWeekDates()
        const weekStart = dayjs(weekDates[0])
        const weekEnd = dayjs(weekDates[6])

        // 只过滤本周的数据，减少处理量
        const weekData = (Array.isArray(days) ? days : []).filter((d) => {
            const checkDate = dayjs(d.checkInDate)
            return checkDate.isSameOrAfter(weekStart) && checkDate.isSameOrBefore(weekEnd)
        })

        const sportMap = new Map(
            weekData.map(d => [dayjs(d.checkInDate).format('YYYY-MM-DD'), d.kcal || 0]),
        )

        return weekDates.map((date) => {
            if (dayjs(date).isAfter(today)) return false
            const kcal = sportMap.get(date) ?? 0
            return kcal > 0
        })
    } catch (error) {
        console.error('computeSportWeekStatus error:', error)
        return Array.from({ length: 7 }, () => false)
    }
}

/**
 * Encourage 弹窗：每日只弹一次工具
 */
function buildEncourageStorageKey(type: 'weight' | 'diet' | 'sport', userId?: string): string {
    const uid = userId || 'anonymous'
    return `encourage:lastShown:${uid}:${type}`
}

export function getTodayStr(): string {
    return dayjs().format('YYYY-MM-DD')
}

export function canShowEncourageToday(type: 'weight' | 'diet' | 'sport', userId?: string): boolean {
    try {
        if (typeof window === 'undefined' || !window.localStorage) return true
        const key = buildEncourageStorageKey(type, userId)
        const lastShown = window.localStorage.getItem(key)
        const today = getTodayStr()
        return lastShown !== today
    } catch (error) {
        console.error('canShowEncourageToday error:', error)
        return true
    }
}

export function markEncourageShownToday(type: 'weight' | 'diet' | 'sport', userId?: string): void {
    try {
        if (typeof window === 'undefined' || !window.localStorage) return
        const key = buildEncourageStorageKey(type, userId)
        window.localStorage.setItem(key, getTodayStr())
    } catch (error) {
        console.error('markEncourageShownToday error:', error)
    }
}

/**
 * 计算连续打卡天数
 * 从当天开始往前找，直到第一次中断，返回连续打卡的天数
 * @param checkinData 打卡数据数组
 * @param startDate 开始日期，默认为 '2024-08-19'
 * @returns 连续打卡天数
 */
export function calculateContinuousCheckinDays(
    checkinData: any[],
    startDate: string = '2024-08-19',
): number {
    try {
        const today = dayjs().startOf('day')

        // 提取打卡日期集合，有数据就表示已打卡
        const checkinDates = new Set(
            checkinData.map(d => dayjs(d.checkInDate).format('YYYY-MM-DD')),
        )

        let continuousDays = 0
        let currentDate = today

        // 从今天开始往前遍历，最多遍历到开始日期
        const projectStartDate = dayjs(startDate)

        while (currentDate.isSameOrAfter(projectStartDate)) {
            const dateStr = currentDate.format('YYYY-MM-DD')

            if (checkinDates.has(dateStr)) {
                continuousDays++
                currentDate = currentDate.subtract(1, 'day')
            } else {
                break // 找到第一次中断，停止计算
            }
        }

        return continuousDays
    } catch (error) {
        console.error('calculateContinuousCheckinDays error:', error)
        return 0
    }
}
