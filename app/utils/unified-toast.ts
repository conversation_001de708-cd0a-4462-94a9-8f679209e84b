import { type App, createApp } from 'vue'

import UnifiedToastLoading from '@/components/shared/unified-toast-loading.vue'

export interface ToastLoadingOptions {
    message?: string
    forbidClick?: boolean
    duration?: number
    size?: 'small' | 'medium' | 'large'
    overlay?: boolean
    allowMultiple?: boolean
}

interface ToastInstance {
    app: App
    close: () => void
}

// Toast实例管理
let toastInstances: ToastInstance[] = []
let allowMultiple = false

// 允许多个Toast同时显示
export function allowMultipleUnifiedToast() {
    allowMultiple = true
}

// 关闭所有Toast
export function closeAllUnifiedToast() {
    toastInstances.forEach((instance) => {
        instance.close()
        instance.app.unmount()
    })
    toastInstances = []
}

// 关闭最新的Toast
export function closeUnifiedToast(all = false) {
    if (all) {
        closeAllUnifiedToast()
        return
    }

    const lastInstance = toastInstances.pop()
    if (lastInstance) {
        lastInstance.close()
        lastInstance.app.unmount()
    }
}

// 显示Loading Toast
export function showUnifiedLoadingToast(options: ToastLoadingOptions = {}) {
    // 如果不允许多个Toast，先关闭现有的
    if (!allowMultiple && toastInstances.length > 0) {
        closeAllUnifiedToast()
    }

    // 创建容器元素
    const container = document.createElement('div')
    document.body.appendChild(container)

    // 创建Vue应用实例
    const app = createApp(UnifiedToastLoading, {
        ...options,
        onClose: () => {
            // 从实例列表中移除
            const index = toastInstances.findIndex(instance => instance.app === app)
            if (index > -1) {
                toastInstances.splice(index, 1)
            }
            // 卸载应用并移除容器
            app.unmount()
            if (container.parentNode) {
                container.parentNode.removeChild(container)
            }
        },
    })

    // 挂载应用
    const vm = app.mount(container)

    // 创建Toast实例对象
    const toastInstance: ToastInstance = {
        app,
        close: () => {
            if (vm && typeof (vm as any).close === 'function') {
                ;(vm as any).close()
            }
        },
    }

    // 添加到实例列表
    toastInstances.push(toastInstance)

    return toastInstance
}

// 兼容原有API - 重命名避免与Vant冲突
export function showUnifiedLoadingToastCompat(options: ToastLoadingOptions = {}) {
    return showUnifiedLoadingToast(options)
}
