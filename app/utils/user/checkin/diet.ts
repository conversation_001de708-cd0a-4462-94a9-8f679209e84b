// @unocss-include
export interface DietType {
    key: string
    label: string
    icon: string
    time?: string
    suggestion: string
}

export const dietMeta: DietType[] = [
    { key: 'breakfast', label: '早餐', icon: 'i-custom-checkin-diet-breakfast', time: '8:00', suggestion: '健康的早餐应营养素搭配均衡。主食可选全麦面包、燕麦粥等，提供持久能量。搭配鸡蛋、牛奶、豆类等优质蛋白，助力肌肉修复生长。再加上新鲜水果或蔬菜汁，补充维生素与膳食纤维。例如，一份全麦三明治、一杯热牛奶与半个苹果，简单却能开启活力满满的一天。' },
    { key: 'lunch', label: '午餐', icon: 'i-custom-checkin-diet-lunch', time: '12:00', suggestion: '午餐应注重营养均衡与饱腹感。主食可选取糙米饭、红薯等粗粮，增加膳食纤维摄入。搭配丰富的蛋白质，如瘦肉、鱼肉、豆腐等，满足身体对氨基酸的需求。蔬菜方面，绿叶菜与各类根茎蔬菜皆可，提供充足维生素与矿物质。例如，香煎三文鱼、糙米饭与清炒时蔬的组合，美味又健康，能为下午的活动提供充沛精力。' },
    { key: 'dinner', label: '晚餐', icon: 'i-custom-checkin-diet-dinner', time: '18:00', suggestion: '晚餐应以清淡为主，避免油腻与辛辣。主食可选择糙米、全麦面食等，提供足够的能量。搭配适量的蛋白质，如鱼、豆腐等，有助于身体恢复。蔬菜方面，绿叶菜与根茎类蔬菜是首选，提供丰富的维生素与矿物质。例如，清蒸鲈鱼、糙米饭与炒青菜的组合，既健康又不会给肠胃带来负担。' },
    { key: 'snack', label: '加餐', icon: 'i-custom-checkin-diet-snack', suggestion: '加餐应选择易消化、低脂肪的食物，如坚果、酸奶或水果。坚果提供健康的脂肪与蛋白质，酸奶补充钙与蛋白质，水果则提供维生素与膳食纤维。例如，一小把杏仁、一杯低脂酸奶与一个橙子，既能满足口腹之欲，又不会影响晚餐的摄入。' },
]
