import { consola } from 'consola/browser'

import now from '~build/time'
import { commitMessage, sha } from '~build/git'

const buildInfos = [
    {
        name: '提交ID',
        value: sha,
    },
    {
        name: '提交信息',
        value: commitMessage,
    },
    {
        name: '构建时间',
        value: now,
    },
]

function print() {
    if (import.meta.env.MODE === 'production') {
        buildInfos.forEach((info) => {
            if (info.value)
                consola.info(`${info.name}: ${info.value}`)
        })
    }
}

print()
