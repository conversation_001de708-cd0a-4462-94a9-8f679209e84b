<script setup lang="ts">
defineProps<{
    metaLayout?: MetaLayout
}>()

const route = useRoute()

const isUser = computed(() => {
    return route.path.startsWith('/user')
})

const isTabbarShow = computed(() => {
    return (route.meta as any).meta?.tabbar
})
</script>

<template>
    <main
        id="main"
        class="safe-area-bottom w-full h-screen relative text-14px"
        :class="metaLayout?.customBg || 'bg-#F4F5F7'"
    >
        <slot></slot>
        <template v-if="isUser">
            <user-tabbar v-show="isTabbarShow" />
        </template>
    </main>
</template>
