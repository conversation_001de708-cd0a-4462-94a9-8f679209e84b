@import url('./survey.scss');
@import url('./guide.scss');
@import url('./vant.scss');
@import url('./themes.scss');
@import url('./font.scss');

.safe-area-bottom {
  padding-bottom: calc(32px + constant(safe-area-inset-bottom));
  padding-bottom: calc(32px + env(safe-area-inset-bottom))
}

.image-background-gradient {
  background: url('@/assets/images/background/gradient.svg') no-repeat center/cover;
}

.custom-underline {
  font-weight: 600;
  z-index: 10;
  position: relative;
}

.custom-underline::after {
  content: '';
  position: absolute;
  z-index: -1;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 8px;
  height: 8px;
  background: rgba(0, 172, 151, 0.5);
  backdrop-filter: blur(4px);
}

.vertical-bar {
  position: relative;
  padding-left: 12px;

  &::before {
      content: '';
      display: block;
      position: absolute;
      left: 0;
      top: 50%;
      transform: translateY(-50%);
      width: 4px;
      height: 13px;
      border-radius: 2px;
      background: #00AC97;
  }
}

.linear-gradient-content {
  .van-action-sheet__content {
    background: linear-gradient(180deg, #EBFFFE 0%, #FFFFFF 44%, #FFFFFF 100%);
  }
}

html {
  background-color: #F4F5F7;
}