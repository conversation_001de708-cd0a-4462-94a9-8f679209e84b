.sd-body.sd-body--responsive {
  padding: 0!important;
}

.sd-page {
  padding: 10px!important;
}

.sd-body.sd-body--static {
  padding-top: 10px!important;
}


.sd-body.sd-body--static .sd-body__navigation.sd-action-bar {
  padding-left: 10px!important;
  padding-right: 10px!important;
  padding-top: 20px!important;
  padding-bottom: calc(env(safe-area-inset-bottom) + 10px)!important;
  padding-bottom: calc(constant(safe-area-inset-bottom) + 10px)!important;
  justify-content: center;
}

.sv-action-bar-item:disabled,
.sv-popup--dropdown-overlay .sv-popup__button:disabled,
.sv-button-group__item--disabled .sv-button-group__item-decorator,
.sd-ranking--disabled .sv-ranking-item__text,
.sv-ranking-item--disabled .sv-ranking-item__text,
.sd-element__title.sd-element__title--disabled,
.sd-input.sd-input--disabled,
.sd-input.sd-input--disabled::placeholder,
.sd-question--disabled .sd-table__cell,
.sd-table__row-disabled > .sd-table__cell,
.sd-item--disabled .sd-item__control-label,
.sd-question--disabled .sd-rating__item-text,
.sd-boolean--disabled .sd-boolean__thumb,
.sd-boolean--disabled .sd-boolean__label,
.sd-file__choose-file-btn--disabled,
.sd-btn:disabled,
.sd-item--disabled .sd-item__control-label {
  opacity: 0.9 !important;
}
