:root {
  --van-swipe-indicator-inactive-background: rgb(var(--t-4), 0.6) !important;
  --van-swipe-indicator-active-background	: rgba(var(--t-4), 1) !important;
  --van-cell-group-inset-radius: 4px!important;
  --van-overlay-z-index: 1111!important;
  --van-calendar-popup-height: 540px !important;
  --van-rolling-text-item-width: unset!important;
  --van-action-sheet-max-height: 100% !important; 
  --van-tabs-card-height: 40px!important;
}

.van-tabs__nav--card {
  border-radius: 10px!important;
}

.van-tab--card.van-tab--active {
  // color: var(--van-primary-color)!important;
  font-weight: 500!important;
  font-size: 15px!important;

  &:first-child {
    border-radius: 8px 0 0 8px!important;
  }

  &:last-child {
    border-radius: 0 8px 8px 0!important;
  }
}

.van-tabs__line {
  width: 30px!important;
}

.van-cell:not(.van-cell--clickable):not(.van-search__field):not(.original) {
  @apply flex-col space-y-8px;
  .van-cell__title.van-field__label {
    @apply text-13px;
  }

  .van-field__control {
    @apply text-16px;
  }
}

.van-tabbar-item__icon {
  margin-bottom: 6px!important;
}

.van-tabbar-item__text {
  font-size: 10px!important;
}

.van-progress {
  @apply b-rd-full!;
}

.van-cell .van-cell__title {
  font-size: 16px!important;
}

.van-button--plain {
  background: unset!important;
}

.van-rolling-text-item__item {
  text-align: unset;
}

.van-tabs__nav--card {
  margin: 0!important;
}

.van-skeleton {
  padding: 0!important;
}

.van-sidebar {
  background: var(--van-sidebar-background);
}

.van-image__error {
  background: unset;
}
