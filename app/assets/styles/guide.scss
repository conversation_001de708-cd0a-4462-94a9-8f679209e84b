.guide-buttons-container {
    position: fixed;
    width: 100vw;
    padding: 0 24px;
    bottom: 40px;
    right: 0px;
    z-index: 10001;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.guide-skip-btn,
.guide-next-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 90px;
    height: 33px;
    color: white;
    border-radius: 100px;
    font-size: 14px;
    cursor: pointer;
    border: 1px solid #fff;
}

.guide-space-center {
    justify-content: center;
}

.guide-finish-btn {
    width: 190px;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #00AC97;
    border-radius: 100px;
    color: #fff;
    font-size: 14px;
}

.custom-guide-popover {
    display: flex !important;
    flex-direction: column;
    align-items: center !important;
    justify-content: center !important;
    background-color: transparent !important;
    box-shadow: none !important;
    max-width: 100% !important;
    width: 100% !important;
    padding: 48px 15px !important;

    .driver-popover-arrow {
        display: none !important;
    }

    .driver-popover-description {
        position: relative;
        width: fit-content;
        background-color: #00AC97;
        color: #fff;
        font-size: 13px;
        padding: 4px 16px;
        border-radius: 100px;

        &::after {
            content: '';
            position: absolute;
            width: 12px;
            height: 48px;
            background-image: url('~/assets/images/checkin/diet/guide-line.png');
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;

            top: -48px;
            right: 10%;
            rotate: 0deg;
            transform: translateX(50%);
        }
    }

    .driver-popover-footer {
        margin: 15px 0;
    }

    .driver-popover-next-btn {
        width: 76px;
        height: 33px;
        color: #00AC97;
        font-size: 14px;
        display: flex !important;
        align-items: center;
        justify-content: center;
        background-color: #E4FAF9;
        border: 1px solid #E4FAF9;
        border-radius: 100px;
    }
}