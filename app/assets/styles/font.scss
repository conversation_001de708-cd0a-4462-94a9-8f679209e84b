@font-face {
  font-family: 'ddinpro';
  src: url('/fonts/D-DIN-PRO-800-ExtraBold.otf') format('opentype');
  font-weight: 800;
  font-style: normal;
}

@font-face {
  font-family: 'DINPro';
  src: url('/fonts/DINPro-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'DINPro';
  src: url('/fonts/DINPro-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'DINPro';
  src: url('/fonts/DINPro-Bold.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
}


@font-face {
  font-family: 'PingFangSC';
  src: url('/fonts/PingFangSC-Regular.woff2') format('woff2');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'PingFangSC';
  src: url('/fonts/PingFangSC-Medium.woff2') format('woff2');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'PingFangSC';
  src: url('/fonts/PingFangSC-Semibold.woff2') format('woff2');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'fangyuan';
  src: url('/fonts/FangYuan.woff2') format('woff2');
  font-weight: 700;
  font-style: normal;
}

.dinpro-font {
  font-family: 'DINPro', sans-serif;
}

body {
  font-family: 'PingFangSC', sans-serif !important;
}
