<svg width="43" height="43" viewBox="0 0 43 43" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 4C0 1.79086 1.79086 0 4 0H21L27 6L32 11V38C32 40.2091 30.2091 42 28 42H4C1.79086 42 0 40.2091 0 38V4Z" fill="url(#paint0_linear_545_3149)"/>
<path d="M32 11L21 0V7C21 9.20914 22.7909 11 25 11H32Z" fill="#9D50FE"/>
<rect x="3" y="24" width="24" height="3" rx="1.5" fill="url(#paint1_linear_545_3149)"/>
<rect x="3" y="17" width="16" height="3" rx="1.5" fill="url(#paint2_linear_545_3149)"/>
<rect x="3" y="31" width="24" height="3" rx="1.5" fill="url(#paint3_linear_545_3149)"/>
<g filter="url(#filter0_b_545_3149)">
<circle cx="26.5563" cy="26.5563" r="11" transform="rotate(-45 26.5563 26.5563)" fill="#9D50FE" fill-opacity="0.3"/>
</g>
<circle cx="26.5563" cy="26.5563" r="9.5" transform="rotate(-45 26.5563 26.5563)" stroke="#9D50FE" stroke-width="3"/>
<rect x="32.1334" y="34.3345" width="3" height="11" rx="1.5" transform="rotate(-45 32.1334 34.3345)" fill="#9D50FE"/>
<defs>
<filter id="filter0_b_545_3149" x="14.7564" y="14.7563" width="23.6" height="23.6" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="0.4"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_545_3149"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_545_3149" result="shape"/>
</filter>
<linearGradient id="paint0_linear_545_3149" x1="44.0342" y1="-9.7931" x2="14.2222" y2="42" gradientUnits="userSpaceOnUse">
<stop offset="0.384117" stop-color="#D8C0FF"/>
<stop offset="1" stop-color="#CDA6FF"/>
</linearGradient>
<linearGradient id="paint1_linear_545_3149" x1="3" y1="25.5" x2="27" y2="25.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#B377FF"/>
<stop offset="1" stop-color="#C99FFF"/>
</linearGradient>
<linearGradient id="paint2_linear_545_3149" x1="3" y1="18.5" x2="19" y2="18.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#B377FF"/>
<stop offset="1" stop-color="#C99FFF"/>
</linearGradient>
<linearGradient id="paint3_linear_545_3149" x1="3" y1="32.5" x2="27" y2="32.5" gradientUnits="userSpaceOnUse">
<stop stop-color="#B377FF"/>
<stop offset="1" stop-color="#C99FFF"/>
</linearGradient>
</defs>
</svg>
