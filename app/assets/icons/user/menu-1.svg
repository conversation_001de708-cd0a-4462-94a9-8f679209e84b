<svg width="57" height="56" viewBox="0 0 57 56" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#filter0_b_7121_5051)">
  <path d="M13 13C13 11.3431 14.3431 10 16 10H42C43.6569 10 45 11.3431 45 13V43C45 44.6569 43.6569 46 42 46H16C14.3431 46 13 44.6569 13 43V13Z" fill="#03CCB3"/>
  </g>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M22.4495 20C21.3038 20 20.375 20.937 20.375 22.0929C20.375 23.2488 21.3038 24.1858 22.4495 24.1858H32.1306C33.2763 24.1858 34.2051 23.2488 34.2051 22.0929C34.2051 20.937 33.2763 20 32.1306 20H22.4495ZM22.4495 28.3718C21.3038 28.3718 20.375 29.3089 20.375 30.4648C20.375 31.6207 21.3038 32.5577 22.4495 32.5577H36.2796C37.4253 32.5577 38.3541 31.6207 38.3541 30.4648C38.3541 29.3089 37.4253 28.3718 36.2796 28.3718H22.4495Z" fill="white"/>
  <defs>
  <filter id="filter0_b_7121_5051" x="9" y="6" width="40" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
  <feFlood flood-opacity="0" result="BackgroundImageFix"/>
  <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
  <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_7121_5051"/>
  <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_7121_5051" result="shape"/>
  </filter>
  </defs>
  </svg>
  