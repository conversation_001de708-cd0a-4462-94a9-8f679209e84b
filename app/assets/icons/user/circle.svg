<svg width="52" height="52" viewBox="0 0 52 52" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#filter0_d_12857_31437)">
  <circle cx="26" cy="19" r="4" fill="#D9D9D9"/>
  <circle cx="26" cy="19" r="4" fill="url(#paint0_linear_12857_31437)"/>
  </g>
  <defs>
  <filter id="filter0_d_12857_31437" x="0" y="0" width="52" height="52" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
  <feFlood flood-opacity="0" result="BackgroundImageFix"/>
  <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
  <feOffset dy="7"/>
  <feGaussianBlur stdDeviation="11"/>
  <feComposite in2="hardAlpha" operator="out"/>
  <feColorMatrix type="matrix" values="0 0 0 0 0.193741 0 0 0 0 0.317308 0 0 0 0 0.30701 0 0 0 0.25 0"/>
  <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_12857_31437"/>
  <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_12857_31437" result="shape"/>
  </filter>
  <linearGradient id="paint0_linear_12857_31437" x1="23.1429" y1="16.7143" x2="29.1429" y2="21.8571" gradientUnits="userSpaceOnUse">
  <stop stop-color="#6DF2E1"/>
  <stop offset="1" stop-color="#0ED0B9"/>
  </linearGradient>
  </defs>
  </svg>
  