<svg width="57" height="56" viewBox="0 0 57 56" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#filter0_b_7121_5086)">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M11.0131 16C9.35627 16 8.01312 17.3431 8.01312 19V20.6504V29.9498V43.9999C8.01312 45.6568 9.35627 46.9999 11.0131 46.9999L39.1126 46.9999C40.7694 46.9999 42.1126 45.6568 42.1126 43.9999V23.6504C42.1126 21.9935 40.7694 20.6504 39.1126 20.6504L26.6119 20.6504L26.6128 20.6499L24.4035 17.3359C23.8471 16.5013 22.9104 16 21.9073 16H11.0131Z" fill="#03CCB3"/>
  </g>
  <g filter="url(#filter1_b_7121_5086)">
  <path d="M32.5001 13.9367L31.4578 12.8249C27.9267 9.05838 22.0939 9.05837 18.5629 12.8249C13.8124 17.8921 13.8124 25.9594 18.5628 31.0266L27.8971 40.9833C30.418 43.6722 34.5821 43.6722 37.1029 40.9833L46.4372 31.0267C51.1876 25.9596 51.1876 17.8922 46.4372 12.8251C42.9061 9.05857 37.0734 9.05857 33.5423 12.8251L32.5001 13.9367Z" fill="#84FFF0" fill-opacity="0.4"/>
  </g>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M32.6501 20.1641C31.601 20.1641 30.7505 21.0146 30.7505 22.0637V23.9632H28.8509C27.8017 23.9632 26.9512 24.8137 26.9512 25.8628C26.9512 26.9119 27.8017 27.7624 28.8509 27.7624H30.7505V29.6622C30.7505 30.7113 31.601 31.5618 32.6501 31.5618C33.6992 31.5618 34.5497 30.7113 34.5497 29.6622V27.7624H36.4494C37.4985 27.7624 38.349 26.9119 38.349 25.8628C38.349 24.8137 37.4985 23.9632 36.4494 23.9632L34.5497 23.9632V22.0637C34.5497 21.0146 33.6992 20.1641 32.6501 20.1641Z" fill="white"/>
  <defs>
  <filter id="filter0_b_7121_5086" x="4.01312" y="12" width="42.0994" height="39" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
  <feFlood flood-opacity="0" result="BackgroundImageFix"/>
  <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
  <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_7121_5086"/>
  <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_7121_5086" result="shape"/>
  </filter>
  <filter id="filter1_b_7121_5086" x="11" y="6" width="43" height="41" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
  <feFlood flood-opacity="0" result="BackgroundImageFix"/>
  <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
  <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_7121_5086"/>
  <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_7121_5086" result="shape"/>
  </filter>
  </defs>
  </svg>
  