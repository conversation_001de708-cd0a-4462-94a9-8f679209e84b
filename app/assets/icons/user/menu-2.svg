<svg width="57" height="56" viewBox="0 0 57 56" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#filter0_b_7121_5062)">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M13 10C11.3431 10 10 11.3431 10 13V43C10 44.6569 11.3431 46 13 46H39C40.6569 46 42 44.6569 42 43V13C42 11.3431 40.6569 10 39 10H13ZM17.375 22.0929C17.375 20.937 18.3038 20 19.4495 20H29.1306C30.2763 20 31.2051 20.937 31.2051 22.0929C31.2051 23.2488 30.2763 24.1858 29.1306 24.1858H19.4495C18.3038 24.1858 17.375 23.2488 17.375 22.0929ZM17.375 30.4648C17.375 29.3089 18.3038 28.3718 19.4495 28.3718H33.2796C34.4253 28.3718 35.3541 29.3089 35.3541 30.4648C35.3541 31.6206 34.4253 32.5577 33.2796 32.5577H19.4495C18.3038 32.5577 17.375 31.6206 17.375 30.4648Z" fill="#03CCB3"/>
  </g>
  <g filter="url(#filter1_b_7121_5062)">
  <path fill-rule="evenodd" clip-rule="evenodd" d="M46.0689 37.1065C49.1111 32.3649 48.5588 25.9847 44.4121 21.8379C39.6281 17.054 31.8719 17.054 27.0879 21.8379C22.304 26.6219 22.304 34.3781 27.0879 39.1621C31.2347 43.3089 37.6149 43.8611 42.3566 40.8189L45.6495 44.1118C46.6747 45.1369 48.3367 45.1369 49.3618 44.1118C50.387 43.0867 50.387 41.4246 49.3618 40.3995L46.0689 37.1065Z" fill="#84FFF0" fill-opacity="0.4"/>
  </g>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M35.75 21.75C40.5825 21.75 44.5 25.6675 44.5 30.5C44.5 31.4665 43.7165 32.25 42.75 32.25C41.7835 32.25 41 31.4665 41 30.5C41 27.6005 38.6495 25.25 35.75 25.25C34.7835 25.25 34 24.4665 34 23.5C34 22.5335 34.7835 21.75 35.75 21.75Z" fill="white"/>
  <defs>
  <filter id="filter0_b_7121_5062" x="6" y="6" width="40" height="44" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
  <feFlood flood-opacity="0" result="BackgroundImageFix"/>
  <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
  <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_7121_5062"/>
  <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_7121_5062" result="shape"/>
  </filter>
  <filter id="filter1_b_7121_5062" x="19.5" y="14.25" width="34.6307" height="34.6309" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
  <feFlood flood-opacity="0" result="BackgroundImageFix"/>
  <feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
  <feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_7121_5062"/>
  <feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_7121_5062" result="shape"/>
  </filter>
  </defs>
  </svg>
  