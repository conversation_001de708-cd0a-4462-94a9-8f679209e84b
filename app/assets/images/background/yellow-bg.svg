<svg width="363" height="123" viewBox="0 0 363 123" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_d_6915_14215)">
<rect x="10" y="4" width="343" height="103" rx="20" fill="white"/>
<rect x="10" y="4" width="343" height="103" rx="20" fill="url(#paint0_linear_6915_14215)" fill-opacity="0.2"/>
</g>
<defs>
<filter id="filter0_d_6915_14215" x="0" y="0" width="363" height="123" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="6"/>
<feGaussianBlur stdDeviation="5"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.9552 0 0 0 0 0.9648 0 0 0 0 0.963628 0 0 0 1 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_6915_14215"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_6915_14215" result="shape"/>
</filter>
<linearGradient id="paint0_linear_6915_14215" x1="282.294" y1="100.909" x2="353.898" y2="11.8799" gradientUnits="userSpaceOnUse">
<stop stop-color="white" stop-opacity="0"/>
<stop offset="1" stop-color="#FFD400"/>
</linearGradient>
</defs>
</svg>
