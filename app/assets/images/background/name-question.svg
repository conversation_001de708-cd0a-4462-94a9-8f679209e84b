<svg width="375" height="531" viewBox="0 0 375 531" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect y="11" width="375" height="520" fill="url(#paint0_linear_10363_20782)"/>
  <g filter="url(#filter0_f_10363_20782)">
  <rect x="335.861" width="70" height="79" rx="6" transform="rotate(45 335.861 0)" fill="url(#paint1_linear_10363_20782)"/>
  <rect x="335.861" y="0.707107" width="69" height="78" rx="5.5" transform="rotate(45 335.861 0.707107)" stroke="url(#paint2_linear_10363_20782)"/>
  </g>
  <rect x="321.861" y="66.7071" width="69" height="78" rx="5.5" transform="rotate(45 321.861 66.7071)" fill="url(#paint3_linear_10363_20782)" stroke="url(#paint4_linear_10363_20782)"/>
  <rect x="246.94" y="20.6863" width="69" height="78" rx="5.5" transform="rotate(31.0685 246.94 20.6863)" fill="url(#paint5_linear_10363_20782)" stroke="url(#paint6_linear_10363_20782)"/>
  <defs>
  <filter id="filter0_f_10363_20782" x="280.484" y="0.485352" width="104.389" height="104.388" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
  <feFlood flood-opacity="0" result="BackgroundImageFix"/>
  <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
  <feGaussianBlur stdDeviation="1" result="effect1_foregroundBlur_10363_20782"/>
  </filter>
  <linearGradient id="paint0_linear_10363_20782" x1="187.5" y1="11" x2="187.5" y2="531" gradientUnits="userSpaceOnUse">
  <stop stop-color="#DBFFFB"/>
  <stop offset="1" stop-color="#F4F5F7"/>
  </linearGradient>
  <linearGradient id="paint1_linear_10363_20782" x1="370.861" y1="0" x2="370.861" y2="79" gradientUnits="userSpaceOnUse">
  <stop stop-color="#EBFFFD"/>
  <stop offset="1" stop-color="white" stop-opacity="0"/>
  </linearGradient>
  <linearGradient id="paint2_linear_10363_20782" x1="370.861" y1="0" x2="370.861" y2="79" gradientUnits="userSpaceOnUse">
  <stop stop-color="white"/>
  <stop offset="1" stop-color="white" stop-opacity="0"/>
  </linearGradient>
  <linearGradient id="paint3_linear_10363_20782" x1="356.861" y1="66" x2="356.861" y2="145" gradientUnits="userSpaceOnUse">
  <stop stop-color="#EBFFFD"/>
  <stop offset="1" stop-color="white" stop-opacity="0"/>
  </linearGradient>
  <linearGradient id="paint4_linear_10363_20782" x1="356.861" y1="66" x2="356.861" y2="145" gradientUnits="userSpaceOnUse">
  <stop stop-color="white"/>
  <stop offset="1" stop-color="white" stop-opacity="0"/>
  </linearGradient>
  <linearGradient id="paint5_linear_10363_20782" x1="281.77" y1="20" x2="281.77" y2="99" gradientUnits="userSpaceOnUse">
  <stop stop-color="#EBFFFD"/>
  <stop offset="1" stop-color="white" stop-opacity="0"/>
  </linearGradient>
  <linearGradient id="paint6_linear_10363_20782" x1="281.77" y1="20" x2="281.77" y2="99" gradientUnits="userSpaceOnUse">
  <stop stop-color="white"/>
  <stop offset="1" stop-color="white" stop-opacity="0"/>
  </linearGradient>
  </defs>
  </svg>
  