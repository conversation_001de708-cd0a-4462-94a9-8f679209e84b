<svg width="279" height="312" viewBox="0 0 279 312" fill="none" xmlns="http://www.w3.org/2000/svg">
  <g filter="url(#filter0_i_7068_4645)">
  <rect x="3" y="41" width="273" height="270" rx="28" fill="url(#paint0_linear_7068_4645)"/>
  </g>
  <g filter="url(#filter1_di_7068_4645)">
  <rect x="13" y="5" width="253" height="296" rx="25" fill="#FAFFFE"/>
  </g>
  <g filter="url(#filter2_d_7068_4645)">
  <path d="M11 166L133.548 215.114C137.368 216.646 141.632 216.646 145.452 215.114L268 166V278C268 291.807 256.807 303 243 303H36C22.1929 303 11 291.807 11 278V166Z" fill="url(#paint1_linear_7068_4645)"/>
  </g>
  <g filter="url(#filter3_di_7068_4645)">
  <path d="M11 222.007L125.055 164.308C134.137 159.713 144.863 159.713 153.945 164.308L268 222.007V278C268 291.807 256.807 303 243 303H36C22.1929 303 11 291.807 11 278V222.007Z" fill="url(#paint2_linear_7068_4645)"/>
  </g>
  <path d="M82 5H199V31C199 33.2091 197.209 35 195 35H86C83.7909 35 82 33.2091 82 31V5Z" fill="#E2FFFC"/>
  <path fill-rule="evenodd" clip-rule="evenodd" d="M99.6666 13.333C98.9302 13.333 98.3333 13.93 98.3333 14.6663V25.333C98.3333 26.0694 98.9302 26.6663 99.6666 26.6663H108.333C109.07 26.6663 109.667 26.0694 109.667 25.333V16.0569L106.943 13.333H99.6666ZM101.333 19.6663H106.667V18.333H101.333V19.6663ZM101.333 22.333H104.667V20.9997H101.333V22.333Z" fill="#00AC97"/>
  <path d="M125.101 25.034H123.924L123.726 24.297L124.936 24.319C125.354 24.319 125.563 24.077 125.563 23.593V16.421H121.295V15.651H126.366V23.802C126.366 24.616 125.937 25.034 125.101 25.034ZM117.434 16.839H118.237V25.111H117.434V16.839ZM119.898 18.423H123.858V22.559H119.898V18.423ZM123.055 21.8V19.182H120.701V21.8H123.055ZM119.073 14.925C119.579 15.53 120.041 16.19 120.437 16.916L119.733 17.279C119.315 16.509 118.842 15.838 118.336 15.244L119.073 14.925ZM130.733 15.167C131.063 15.629 131.36 16.113 131.602 16.63L130.931 16.927C130.656 16.355 130.348 15.849 130.018 15.409L130.733 15.167ZM136.123 15.167L136.827 15.431C136.541 16.003 136.178 16.52 135.749 16.96L135.122 16.597C135.529 16.19 135.859 15.717 136.123 15.167ZM129.248 17.004H132.746C132.966 16.377 133.131 15.706 133.241 14.991L134.022 15.09C133.912 15.772 133.747 16.41 133.549 17.004H137.454V17.73H133.274C133.098 18.126 132.911 18.5 132.702 18.852H138.059V19.578H135.463C136.057 20.326 137.036 20.986 138.389 21.58L137.872 22.229C137.08 21.811 136.409 21.371 135.859 20.909V22.405C135.848 22.911 135.54 23.186 134.935 23.208C134.374 23.208 133.835 23.197 133.307 23.175L133.131 22.482C133.681 22.515 134.176 22.537 134.638 22.537C134.924 22.537 135.078 22.416 135.078 22.185V21.36H131.547V23.846C131.547 24.088 131.712 24.22 132.053 24.22H135.584C136.046 24.22 136.332 24.132 136.442 23.978C136.552 23.78 136.64 23.406 136.706 22.856L137.476 23.109C137.388 23.967 137.234 24.506 137.003 24.704C136.783 24.858 136.387 24.946 135.804 24.946H131.734C131.074 24.946 130.744 24.66 130.744 24.099V21.096C130.15 21.58 129.49 21.987 128.764 22.339L128.313 21.646C129.501 21.129 130.469 20.436 131.239 19.578H128.621V18.852H131.8C132.042 18.5 132.262 18.126 132.449 17.73H129.248V17.004ZM132.218 19.578C131.91 19.974 131.591 20.337 131.239 20.667H135.595C135.21 20.315 134.902 19.952 134.66 19.578H132.218ZM143.295 15.827H146.089C146.111 15.541 146.144 15.255 146.166 14.969L146.936 15.079C146.914 15.343 146.881 15.585 146.859 15.827H149.488V16.52H146.771C146.727 16.762 146.694 17.004 146.661 17.235H148.861V22.317H149.873V23.021H142.91V22.317H143.933V17.235H145.902C145.946 16.993 145.979 16.751 146.012 16.52H143.295V15.827ZM144.681 22.317H148.113V21.602H144.681V22.317ZM144.681 21.03H148.113V20.359H144.681V21.03ZM144.681 19.787H148.113V19.116H144.681V19.787ZM144.681 18.555H148.113V17.862H144.681V18.555ZM147.618 23.175C148.377 23.571 149.081 24.044 149.741 24.594L149.257 25.144C148.597 24.561 147.882 24.055 147.123 23.626L147.618 23.175ZM145.187 23.164L145.715 23.637C145.077 24.209 144.285 24.715 143.339 25.144L142.866 24.55C143.79 24.154 144.56 23.692 145.187 23.164ZM139.83 17.708H141.062V15.057H141.821V17.708H142.954V18.456H141.821V21.888C142.195 21.756 142.569 21.613 142.932 21.459V22.24C142.008 22.614 141.007 22.922 139.94 23.175L139.742 22.416C140.182 22.328 140.622 22.229 141.062 22.119V18.456H139.83V17.708ZM154.515 18.566C154.383 19.083 154.24 19.567 154.097 20.018H160.004C159.971 22.702 159.795 24.22 159.465 24.572C159.19 24.869 158.596 25.023 157.683 25.034H156.726L156.506 24.297C156.946 24.308 157.342 24.319 157.716 24.319C158.31 24.308 158.684 24.198 158.86 24C159.058 23.769 159.179 22.691 159.212 20.755H153.096C153.558 19.215 153.91 17.851 154.141 16.674L154.922 16.751C154.845 17.125 154.768 17.477 154.691 17.818H159.179V18.566H154.515ZM151.523 22.075H158.123V22.834H151.523V22.075ZM160.609 15.475V17.862H159.828V16.223H152.612V17.851H151.831V15.475H160.609ZM163.414 15.178C164.096 15.761 164.657 16.344 165.108 16.927L164.569 17.466C164.151 16.916 163.59 16.322 162.886 15.684L163.414 15.178ZM169.959 24.902C169.332 24.902 168.65 24.891 167.913 24.88C167.165 24.869 166.56 24.803 166.098 24.682C165.647 24.55 165.251 24.275 164.899 23.879C164.745 23.692 164.591 23.604 164.448 23.604C164.228 23.604 163.843 24.099 163.304 25.111L162.743 24.594C163.249 23.681 163.7 23.12 164.096 22.922V19.545H162.754V18.808H164.844V22.944C164.932 23.01 165.031 23.087 165.13 23.197C165.416 23.516 165.724 23.747 166.032 23.89C166.395 24.044 166.912 24.132 167.583 24.154C168.199 24.165 168.947 24.176 169.849 24.176C170.366 24.176 170.894 24.165 171.433 24.154C171.961 24.143 172.379 24.132 172.665 24.11L172.478 24.902H169.959ZM165.966 15.772H167.022C167.099 15.497 167.154 15.211 167.198 14.914L167.99 15.024C167.924 15.288 167.847 15.541 167.759 15.772H169.068V18.533C169.585 17.609 169.937 16.421 170.135 14.947L170.861 15.079C170.773 15.64 170.663 16.157 170.531 16.641H172.632V17.378H172.082C172.038 18.61 171.873 19.699 171.576 20.634C171.95 21.272 172.324 21.987 172.676 22.79L172.159 23.307C171.873 22.625 171.576 21.987 171.268 21.415C170.872 22.295 170.333 23.01 169.673 23.582L169.266 22.944C169.959 22.372 170.476 21.602 170.828 20.645C170.498 20.106 170.146 19.611 169.772 19.16L170.179 18.698C170.487 19.028 170.784 19.402 171.07 19.842C171.246 19.105 171.356 18.28 171.378 17.378H170.311C170.058 18.071 169.75 18.665 169.387 19.16L169.068 18.764V18.819H167.682L167.957 19.49H169.453V20.161H167.297C167.253 20.403 167.22 20.634 167.187 20.832H169.112C169.112 22.075 169.035 22.834 168.881 23.098C168.716 23.362 168.441 23.505 168.045 23.516H167.418L167.22 22.867H167.858C168.078 22.856 168.232 22.768 168.298 22.603C168.353 22.438 168.386 22.053 168.397 21.459H167.044C166.989 21.646 166.934 21.811 166.879 21.965C166.659 22.471 166.34 22.933 165.922 23.34L165.471 22.757C165.812 22.427 166.076 22.053 166.252 21.646C166.406 21.261 166.516 20.766 166.593 20.161H165.603V19.49H167.209L166.934 18.819H165.966V15.772ZM168.397 18.269V17.565H166.637V18.269H168.397ZM166.637 17.015H168.397V16.322H166.637V17.015ZM180.354 15.013H181.113V15.728H183.841V16.377H181.113V17.114H183.445V17.752H181.113V18.5H184.116V19.16H177.384V18.5H180.354V17.752H178.132V17.114H180.354V16.377H177.692V15.728H180.354V15.013ZM178.979 21.932V22.735H182.532V21.932H178.979ZM182.532 21.316V20.502H178.979V21.316H182.532ZM178.979 23.351V25.1H178.22V19.853H183.291V24.143C183.291 24.737 182.972 25.034 182.356 25.034H181.432L181.245 24.33L182.125 24.363C182.389 24.363 182.532 24.231 182.532 23.978V23.351H178.979ZM175.393 15.134C176.13 15.684 176.746 16.245 177.252 16.806L176.713 17.356C176.251 16.828 175.624 16.267 174.832 15.673L175.393 15.134ZM174.062 18.379H176.394V23.208C176.724 22.9 177.065 22.559 177.439 22.174L177.648 23.01C177.032 23.648 176.372 24.22 175.69 24.715L175.382 24.011C175.547 23.857 175.635 23.692 175.635 23.505V19.138H174.062V18.379Z" fill="#00AC97"/>
  <defs>
  <filter id="filter0_i_7068_4645" x="3" y="40" width="273" height="271" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
  <feFlood flood-opacity="0" result="BackgroundImageFix"/>
  <feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
  <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
  <feOffset dy="-1"/>
  <feGaussianBlur stdDeviation="2"/>
  <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
  <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
  <feBlend mode="normal" in2="shape" result="effect1_innerShadow_7068_4645"/>
  </filter>
  <filter id="filter1_di_7068_4645" x="6" y="0" width="267" height="310" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
  <feFlood flood-opacity="0" result="BackgroundImageFix"/>
  <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
  <feOffset dy="2"/>
  <feGaussianBlur stdDeviation="3.5"/>
  <feComposite in2="hardAlpha" operator="out"/>
  <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.12 0"/>
  <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7068_4645"/>
  <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7068_4645" result="shape"/>
  <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
  <feOffset dy="-3"/>
  <feGaussianBlur stdDeviation="2"/>
  <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
  <feColorMatrix type="matrix" values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 0.25 0"/>
  <feBlend mode="normal" in2="shape" result="effect2_innerShadow_7068_4645"/>
  </filter>
  <filter id="filter2_d_7068_4645" x="0" y="153" width="279" height="159" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
  <feFlood flood-opacity="0" result="BackgroundImageFix"/>
  <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
  <feOffset dy="-2"/>
  <feGaussianBlur stdDeviation="5.5"/>
  <feComposite in2="hardAlpha" operator="out"/>
  <feColorMatrix type="matrix" values="0 0 0 0 0.0427083 0 0 0 0 0.341667 0 0 0 0 0.305792 0 0 0 0.06 0"/>
  <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7068_4645"/>
  <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7068_4645" result="shape"/>
  </filter>
  <filter id="filter3_di_7068_4645" x="0" y="147.862" width="279" height="164.138" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
  <feFlood flood-opacity="0" result="BackgroundImageFix"/>
  <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
  <feOffset dy="-2"/>
  <feGaussianBlur stdDeviation="5.5"/>
  <feComposite in2="hardAlpha" operator="out"/>
  <feColorMatrix type="matrix" values="0 0 0 0 0.0342708 0 0 0 0 0.391667 0 0 0 0 0.348779 0 0 0 0.1 0"/>
  <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_7068_4645"/>
  <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_7068_4645" result="shape"/>
  <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
  <feOffset dy="-1"/>
  <feGaussianBlur stdDeviation="2"/>
  <feComposite in2="hardAlpha" operator="arithmetic" k2="-1" k3="1"/>
  <feColorMatrix type="matrix" values="0 0 0 0 0.8125 0 0 0 0 0.91 0 0 0 0 1 0 0 0 0.31 0"/>
  <feBlend mode="normal" in2="shape" result="effect2_innerShadow_7068_4645"/>
  </filter>
  <linearGradient id="paint0_linear_7068_4645" x1="139.5" y1="41" x2="139.5" y2="311" gradientUnits="userSpaceOnUse">
  <stop stop-color="#AACFCB"/>
  <stop offset="1" stop-color="#85C5BD"/>
  </linearGradient>
  <linearGradient id="paint1_linear_7068_4645" x1="139.5" y1="166" x2="139.5" y2="303" gradientUnits="userSpaceOnUse">
  <stop stop-color="#C6F0EB"/>
  <stop offset="1" stop-color="#DFF4F3"/>
  </linearGradient>
  <linearGradient id="paint2_linear_7068_4645" x1="139.5" y1="157" x2="139.5" y2="303" gradientUnits="userSpaceOnUse">
  <stop stop-color="#ECFFFD"/>
  <stop offset="1" stop-color="#D5FFFA"/>
  </linearGradient>
  </defs>
  </svg>
  