<svg width="375" height="812" viewBox="0 0 375 812" fill="none" xmlns="http://www.w3.org/2000/svg">
<g filter="url(#filter0_b_6878_14627)">
<rect width="375" height="812" fill="url(#paint0_linear_6878_14627)"/>
<rect width="375" height="812" fill="url(#paint1_linear_6878_14627)"/>
</g>
<defs>
<filter id="filter0_b_6878_14627" x="-4" y="-4" width="383" height="820" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feGaussianBlur in="BackgroundImageFix" stdDeviation="2"/>
<feComposite in2="SourceAlpha" operator="in" result="effect1_backgroundBlur_6878_14627"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_backgroundBlur_6878_14627" result="shape"/>
</filter>
<linearGradient id="paint0_linear_6878_14627" x1="187.5" y1="0" x2="187.5" y2="812" gradientUnits="userSpaceOnUse">
<stop stop-color="#0073BE"/>
<stop offset="1" stop-color="#6AD9CB"/>
</linearGradient>
<linearGradient id="paint1_linear_6878_14627" x1="187.5" y1="0" x2="187.5" y2="812" gradientUnits="userSpaceOnUse">
<stop stop-color="#DAF0FF"/>
<stop offset="1" stop-color="white"/>
</linearGradient>
</defs>
</svg>
