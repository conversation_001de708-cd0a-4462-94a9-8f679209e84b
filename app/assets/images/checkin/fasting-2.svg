<svg width="86" height="69" viewBox="0 0 86 69" fill="none" xmlns="http://www.w3.org/2000/svg">
  <path d="M4.83026 8.7038C5.32778 7.7654 6.67222 7.7654 7.16974 8.7038L8.40907 11.0414C8.53315 11.2754 8.72459 11.4668 8.95863 11.5909L11.2962 12.8303C12.2346 13.3278 12.2346 14.6722 11.2962 15.1697L8.95863 16.4091C8.72459 16.5332 8.53315 16.7246 8.40907 16.9586L7.16974 19.2962C6.67222 20.2346 5.32778 20.2346 4.83026 19.2962L3.59093 16.9586C3.46685 16.7246 3.27541 16.5332 3.04137 16.4091L0.703798 15.1697C-0.234601 14.6722 -0.234599 13.3278 0.7038 12.8303L3.04137 11.5909C3.27541 11.4668 3.46685 11.2754 3.59093 11.0414L4.83026 8.7038Z" fill="#A9E8E8"/>
  <path d="M75.2202 17.4692C75.5519 16.8436 76.4481 16.8436 76.7798 17.4692L77.606 19.0276C77.6888 19.1836 77.8164 19.3112 77.9724 19.394L79.5308 20.2202C80.1564 20.5519 80.1564 21.4481 79.5308 21.7798L77.9724 22.606C77.8164 22.6888 77.6888 22.8164 77.606 22.9724L76.7798 24.5308C76.4481 25.1564 75.5519 25.1564 75.2202 24.5308L74.394 22.9724C74.3112 22.8164 74.1836 22.6888 74.0276 22.606L72.4692 21.7798C71.8436 21.4481 71.8436 20.5519 72.4692 20.2202L74.0276 19.394C74.1836 19.3112 74.3112 19.1836 74.394 19.0276L75.2202 17.4692Z" fill="#A9E8E8"/>
  <g clip-path="url(#clip0_11213_22743)">
  <path d="M68.6465 47.0031C69.7704 47.0031 70.8577 47.3367 71.7119 47.9454C72.1441 48.2382 72.4885 48.6113 72.7178 49.0343C72.9469 49.4572 73.0544 49.9193 73.0322 50.3829C72.9218 51.3047 72.4589 52.1725 71.7119 52.8585C70.9199 53.4253 69.9244 53.7571 68.8818 53.8019H11.1543C10.6286 53.8276 10.1023 53.7568 9.61328 53.5939C9.12418 53.4308 8.68374 53.1794 8.32422 52.8585C7.89402 52.5559 7.55194 52.1759 7.32324 51.7472C7.09456 51.3184 6.98521 50.8519 7.00391 50.3829C7.11427 49.4726 7.57765 48.6163 8.32422 47.9454C9.10183 47.3593 10.1061 47.0255 11.1543 47.0031H68.6465ZM40.0176 6.63879C41.105 6.63119 42.1829 6.81242 43.1816 7.17102C44.1804 7.52966 45.079 8.05787 45.8193 8.7218C47.2634 10.1512 48.0495 11.9627 48.0361 13.8312V14.7745C53.3382 16.1732 58.051 18.8046 61.6191 22.3595C65.2871 26.6734 67.5897 31.6892 68.3154 36.9415H68.8818C69.423 36.9559 69.9549 37.0645 70.4424 37.2609C70.9297 37.4572 71.3624 37.7368 71.7119 38.0812C72.1448 38.3819 72.4889 38.7614 72.7178 39.1906C72.9467 39.6198 73.0545 40.0874 73.0322 40.5568C72.9219 41.4669 72.4583 42.3224 71.7119 42.9933C71.3705 43.3456 70.9395 43.6312 70.4502 43.8282C69.9609 44.0253 69.4253 44.1299 68.8818 44.1339H11.1543C10.6264 44.1777 10.0932 44.1145 9.60059 43.9503C9.10811 43.7861 8.67019 43.5256 8.32422 43.1906C7.88834 42.8917 7.54173 42.512 7.3125 42.0822C7.08337 41.6524 6.97799 41.1837 7.00391 40.714C7.42817 40.1639 7.42773 39.6138 7.42773 39.2208C7.65862 38.8068 7.96096 38.4229 8.32422 38.0812C8.70583 37.7671 9.15336 37.5136 9.64453 37.3341C10.1081 37.1146 10.6233 36.981 11.1543 36.9415H11.3896C12.1651 31.6865 14.4997 26.6744 18.1816 22.3595C21.7061 18.7464 26.4562 16.1031 31.8115 14.7745V13.8312C31.7812 12.8888 31.9785 11.951 32.3916 11.0734C32.8048 10.1957 33.4255 9.39567 34.2168 8.7218C34.9515 8.05207 35.8487 7.52036 36.8486 7.16125C37.8486 6.80219 38.9289 6.62385 40.0176 6.63879ZM33.3262 23.1056C33.01 22.1196 32.0004 21.5485 31.0088 21.7589L30.8105 21.8116C27.1258 22.9931 23.9582 25.395 21.8262 28.6154L21.623 28.9288C21.0337 29.8629 21.3131 31.0982 22.2471 31.6876C23.1812 32.277 24.4164 31.9976 25.0059 31.0636L25.3213 30.587C26.8398 28.3993 28.9943 26.7303 31.4922 25.8068L32.0322 25.6202C33.0838 25.283 33.6632 24.1573 33.3262 23.1056ZM40.0176 9.66516C38.8298 9.69972 37.7016 10.1068 36.8584 10.8048C36.3516 11.1423 35.9461 11.5741 35.6758 12.0656C35.4055 12.557 35.2778 13.0947 35.3018 13.6349V13.8312C36.1109 13.8692 36.9222 13.8031 37.707 13.6349H42.4229C43.2868 13.8086 44.1773 13.8746 45.0645 13.8312V13.6349C45.0499 13.0998 44.9043 12.5726 44.6367 12.0861C44.3691 11.5995 43.9848 11.1633 43.5078 10.8048C42.5508 10.0822 41.3089 9.67676 40.0176 9.66516Z" fill="url(#paint0_linear_11213_22743)"/>
  </g>
  <g filter="url(#filter0_d_11213_22743)">
  <rect x="52" y="34" width="26" height="26" rx="13" fill="#E4FAF9" shape-rendering="crispEdges"/>
  <path d="M60.041 41.1666H69.9577" stroke="#00AC97" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M60.041 52.8334H69.9577" stroke="#00AC97" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M61.209 52.8333C61.9868 48.9428 63.2507 46.9984 65.0007 47C66.7507 47.0016 68.0145 48.9461 68.7923 52.8333H61.209Z" stroke="#00AC97" stroke-width="1.2" stroke-linejoin="round"/>
  <path d="M68.7923 41.1666C68.0145 45.0571 66.7507 47.0016 65.0007 47C63.2507 46.9983 61.9868 45.0539 61.209 41.1666H68.7923Z" stroke="#00AC97" stroke-width="1.2" stroke-linejoin="round"/>
  <path d="M64.125 44.375H65.875" stroke="#00AC97" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
  <path d="M63.541 51.0834H66.4577" stroke="#00AC97" stroke-width="1.2" stroke-linecap="round" stroke-linejoin="round"/>
  </g>
  <defs>
  <filter id="filter0_d_11213_22743" x="44" y="26" width="42" height="42" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
  <feFlood flood-opacity="0" result="BackgroundImageFix"/>
  <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
  <feOffset/>
  <feGaussianBlur stdDeviation="4"/>
  <feComposite in2="hardAlpha" operator="out"/>
  <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.06 0"/>
  <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_11213_22743"/>
  <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_11213_22743" result="shape"/>
  </filter>
  <linearGradient id="paint0_linear_11213_22743" x1="40.0176" y1="6.63794" x2="40.0176" y2="53.8071" gradientUnits="userSpaceOnUse">
  <stop stop-color="#27CF94"/>
  <stop offset="1" stop-color="#52DFAE"/>
  </linearGradient>
  <clipPath id="clip0_11213_22743">
  <rect width="66" height="55" fill="white" transform="translate(7)"/>
  </clipPath>
  </defs>
  </svg>
  