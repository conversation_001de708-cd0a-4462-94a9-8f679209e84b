<script setup lang="ts">
definePageMeta({
    meta: {
        tabbar: false,
        layout: {
            customBg: 'bg-#eef9f8 relative',
        },
    },
})

const show = ref(false)
</script>

<template>
    <div class="pt-16px">
        <div class="flex justify-center">
            <div class="bg-image">
            </div>
        </div>

        <div class="flex justify-center mt-16px">
            <van-button class="!w-295px !text-15px" round type="primary" @click="show = true">
                立即加入
            </van-button>
        </div>

        <van-overlay :show="show">
            <div flex="~ col" items-center relative h-full justify-center>
                <img src="@/assets/images/common/qr-code.png" class="w-226px h-276px" />
                <div class="i-custom:close absolute h-26px w-26px z-1000 top-570px left-175px" @click="show = false"></div>
            </div>
        </van-overlay>
    </div>
</template>

<style scoped>
.bg-image {
    background-image: url('@/assets/images/background/camp-bg.png');
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border-radius: 10px;
    position: relative;
    top: 0px;
    height: calc(100vh - 120px - constant(safe-area-inset-bottom));
    height: calc(100vh - 120px - env(safe-area-inset-bottom));
    aspect-ratio: 1242 / 2400;
}

.van-popup {
    background: unset;
}
</style>
