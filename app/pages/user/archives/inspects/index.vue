<script setup lang="ts">
useHead({
    title: '临床检验',
})

const { data, status } = useAPI<InspectionReport[]>('/v1/hospital-data/getOutpatientInspectionReport', {
    // params: {
    //     customerUserId: '3fd6d848-31f5-4605-b1b7-14c780b78017',
    // },
})

const dayjs = useDayjs()

const inspectionList = computed(() => {
    const formatList: Record<string, InspectionReport[]> = {}

    if (data.value?.results) {
        for (const item of data.value.results) {
            const date = dayjs(item.check_date).format('YYYY/MM/DD')
            if (!formatList[date]) {
                formatList[date] = []
            }
            formatList[date].push(item)
        }
    }

    return formatList
})

function handleClick(report: InspectionReport) {
    localStorage.setItem('inspection-report', JSON.stringify(report))
    navigateTo(`/user/archives/inspects/${report.report_no}`)
}
</script>

<template>
    <base-suspense :status="status">
        <div v-if="data?.results.length" class="py-16px flex flex-col gap-16px h-full overflow-y-auto">
            <div v-for="(item, key) in inspectionList" :key="key">
                <div class="text-12px text-t-3 mb-8px text-center">
                    {{ key }}
                </div>
                <van-cell-group inset>
                    <van-cell
                        v-for="report in item"
                        :key="report.report_no"
                        :title="report.test_type_name"
                        is-link
                        @click="handleClick(report)"
                    />
                </van-cell-group>
            </div>
        </div>

        <van-empty v-else description="暂无临床检验报告" />
    </base-suspense>
</template>
