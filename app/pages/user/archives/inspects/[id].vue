<script setup lang="ts">
const route = useRoute()

const inspectionReport = JSON.parse(localStorage.getItem('inspection-report') || '{}') as InspectionReport

const reportInfo = {
    检查单号: inspectionReport.report_no,
    检查项目: inspectionReport.test_type_name,
    检查科室: inspectionReport.request_dept_name,
    检查人: inspectionReport.pati_name,
    检查日期: inspectionReport.check_date.split(' ')[0],
}

const { data, status } = useAPI<InspectionResult[]>('/v1/hospital-data/getOutpatientRoutineTestResults', {
    params: {
        report_no: route.params.id,
    },
})
</script>

<template>
    <base-suspense :status="status">
        <div class="p-16px h-full overflow-auto">
            <div class="rd-10px bg-white p-16px flex flex-col gap-14px">
                <div v-for="(item, key) in reportInfo" :key="key" class="flex justify-between">
                    <div class="text-t-4">
                        {{ key }}
                    </div>

                    <div class="text-#1D2129">
                        {{ item }}
                    </div>
                </div>
            </div>

            <table class="w-full mt-16px">
                <thead class="bg-#E4FAF9 overflow-hidden">
                    <tr class="text-center">
                        <th class="py-12px px-8px rd-tl-10px">项目名称</th>
                        <th class="py-12px px-8px">结果</th>
                        <th class="py-12px px-8px">参考范围</th>
                        <th class="py-12px px-8px rd-tr-10px">单位</th>
                    </tr>
                </thead>
                <tbody class="bg-white text-center text-t-4">
                    <tr v-for="(item, index) in data?.results || []" :key="index">
                        <td class="py-12px px-8px">{{ item.test_item_name }}</td>
                        <td class="py-12px px-8px">{{ item.test_result_value }}</td>
                        <td class="py-12px px-8px">{{ item.reference_range }}</td>
                        <td class="py-12px px-8px">{{ item.test_result_unit }}</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </base-suspense>
</template>
