<script setup lang="ts">
definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white overflow-y-auto',
        },
    },
})

const { data: mixedNutritionPlanData, status: nutritionPlanStatus } = useAsyncData('nutrition-plan', async () => {
    const [oldData, newData] = await Promise.all([
        useWrapFetch<BaseResponse<any[]>>('/patient-assessment/getAllAssessmentReportsByCustomerId'),
        useWrapFetch<BaseResponse<any[]>>('/healthProgram/listHealthProgramPlan'),
    ])

    const badgeStore = useBadgeStore()
    badgeStore.healthProgramPlans = newData?.results || []
    badgeStore.updateBadge('/user/archives', {
        show: badgeStore.hasUnreadPlans,
        num: badgeStore.unreadPlansCount,
    })

    return {
        oldData: oldData.results,
        newData: newData.results,
        totalLength: oldData.results.length + newData.results.length,
    }
})

// 添加页面激活时的处理
onMounted(() => {
    refreshRem()
})

function refreshRem() {
    // 触发重新计算
    window.dispatchEvent(new Event('resize'))
}

function handleClick(reportPath: string) {
    if (isIOS()) {
        // 添加返回事件监听
        const handleVisibilityChange = () => {
            if (document.visibilityState === 'visible') {
                setTimeout(() => {
                    window.dispatchEvent(new Event('resize'))
                }, 100)
                document.removeEventListener('visibilitychange', handleVisibilityChange)
            }
        }
        document.addEventListener('visibilitychange', handleVisibilityChange)

        navigateTo(`${window.location.origin}/api/v1/file/preview?objectName=${reportPath}`, {
            external: true,
        })
    } else {
        navigateTo(`/user/archives/health-report/${reportPath}`)
    }
}

async function handleNewDataClick(item: any) {
    try {
        if (item?.readStatus === 0) {
            await useWrapFetch(`/api/healthProgram/updateReadStatus/${item.planId}/1`, {
                method: 'POST',
            })
        }
        navigateTo(`/user/external/weight-management-plan?planId=${item.planId}`)
    } catch (error) {
        console.error(error)
    }
}
</script>

<template>
    <base-suspense :status="nutritionPlanStatus">
        <div v-if="mixedNutritionPlanData?.totalLength" p-16px gap-16px flex flex-col>
            <user-plan-card
                v-for="(item) in mixedNutritionPlanData?.oldData"
                :key="item.id"
                title="脂肪肝减重管理健康报告"
                :data="item"
                type="report"
                @click="handleClick(item.reportPath)"
            />

            <user-plan-card
                v-for="(item) in mixedNutritionPlanData?.newData"
                :key="item.id"
                title="体重管理健康方案"
                :data="item"
                type="report"
                @click="handleNewDataClick(item)"
            />
        </div>

        <van-empty v-else description="暂无健康报告" />
    </base-suspense>
</template>
