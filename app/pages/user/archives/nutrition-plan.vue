<script setup lang="ts">
definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

const { data: mixedNutritionPlanData, status: nutritionPlanStatus } = useAsyncData('nutrition-plan', async () => {
    const [oldData, newData] = await Promise.all([
        useWrapFetch<BaseResponse<any[]>>('/patient-assessment/getAllAssessmentReportsByCustomerId'),
        useWrapFetch<BaseResponse<any[]>>('/customerPlan/listPayedPlanInfo'),
    ])

    return {
        oldData: oldData.results,
        newData: newData.results,
        totalLength: oldData.results.length + newData.results.length,
    }
})

const router = useRouter()

async function handleClick(item: any) {
    await useWrapFetch(`/patient-assessment/readAssessmentReport/${item.id}`)

    const { payStatus = null, interventionPlan = null } = mixedNutritionPlanData.value?.oldData?.find((r: any) => r.id === item.id) || {}
    const { recommendId = null, interventionPlanVOList = [] } = interventionPlan ? JSON.parse(interventionPlan) : {}
    const price = interventionPlanVOList.find((p: any) => p.id === recommendId)?.price
    router.push(`/user/checkin/plan/${recommendId}?reportId=${item.id}&price=${price}&payStatus=${payStatus}`)
}

async function handleExternalPlanClick(item: any) {
    const wx = await useWxBridge({})

    wx?.miniProgram.navigateTo({
        url: `/pages/order/new-pay?planId=${item.planId}`,
    })
}
</script>

<template>
    <base-suspense :status="nutritionPlanStatus">
        <div v-if="mixedNutritionPlanData?.totalLength" p-16px gap-16px flex flex-col>
            <van-badge v-for="item in mixedNutritionPlanData?.oldData" :key="item.id" :dot="item.readStatus === 0">
                <user-plan-card title="脂肪肝减重管理营养方案" :data="item" type="plan" @click="handleClick(item)" />
            </van-badge>

            <!-- 院外流程管理计划 -->
            <user-external-plan-card
                v-for="item in mixedNutritionPlanData?.newData"
                :key="item.id"
                :title="PLAN_ID_MAP[item.planId as PlanId]"
                :data="item"
                type="plan"
                @click="handleExternalPlanClick(item)"
            />
        </div>

        <van-empty v-else description="暂无管理计划" />
    </base-suspense>
</template>
