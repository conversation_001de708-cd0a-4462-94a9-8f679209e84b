<script setup lang="ts">
useHead({
    title: '评估记录',
})

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

const { data, status } = await useAPI<QuestionList[]>('/user/question/list')

const formatData = computed(() => {
    return [
        ...(data.value?.results || []),
    ] as QuestionList[]
})
</script>

<template>
    <base-suspense :status>
        <div p-16px space-y-16px>
            <user-service-card v-for="(item, index) in formatData" :key="index" :data="item" />
        </div>
    </base-suspense>
</template>
