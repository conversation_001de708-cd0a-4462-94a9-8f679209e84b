<script setup lang="ts">
import { VueDraggable } from 'vue-draggable-plus'

const defaultCardList = ref<CheckInToolsItem[]>(CHECKIN_TOOLS)

const cardShow = ref<CheckInToolsItem[]>([])
const cardHide = ref<CheckInToolsItem[]>([])

const { fetchData, originCardList } = useCheckinCard()

watch(originCardList, (val) => {
    if (val) {
        defaultCardList.value.forEach((item) => {
            const find = val.find(v => v.cardName === item.key)
            if (find) {
                item.status = find.status
                item.sort = find.sort
                item.id = find.id
            }
        })

        cardShow.value = defaultCardList.value.filter(item => item.status === 1).sort((a, b) => {
            return (a.sort || 0) - (b.sort || 0)
        })
        cardHide.value = defaultCardList.value.filter(item => item.status === 0).sort((a, b) => {
            return (a.sort || 0) - (b.sort || 0)
        })
    }
})

async function handleSave() {
    const { closeLoading } = useLoading()
    try {
        await nextTick()
        await useWrapFetch('/checkInCard/sort', {
            method: 'POST',
            body: cardShow.value.map(item => item.id),
        })

        fetchData()
        showSuccessToast('保存成功')
    } catch (error) {
        console.error(error)
    } finally {
        closeLoading()
    }
}
</script>

<template>
    <div p-16px>
        <div p-16px rd-10px bg-white>
            <div flex justify-between items-center>
                <div text="16px t-5" font-600>
                    显示的卡片
                </div>

                <div text="12px t-4">
                    拖动卡片调整顺序或隐藏
                </div>
            </div>

            <vue-draggable
                v-model="cardShow"
                class="grid grid-cols-2 gap-16px !w-full !min-h-80px mt-16px"
                :animation="150"
                group="card"
                @end="handleSave"
            >
                <user-checkin-card-setting-item v-for="card in cardShow" :key="card.id" :data="card" />
            </vue-draggable>
        </div>

        <div p-16px rd-10px bg-white mt-16px relative>
            <div flex justify-between items-center>
                <div text="16px t-5" font-600>
                    隐藏的卡片
                </div>

                <div text="12px t-4">
                    拖动卡片显示
                </div>
            </div>

            <vue-draggable
                v-model="cardHide"
                group="card"
                class="grid grid-cols-2 gap-16px !w-full !min-h-80px mt-16px"
                :animation="150"
                @end="handleSave"
            >
                <user-checkin-card-setting-item v-for="card in cardHide" :key="card.id" z-50 :data="card" />
            </vue-draggable>
        </div>
    </div>
</template>
