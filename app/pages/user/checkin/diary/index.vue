<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import { EffectCoverflow } from 'swiper/modules'

import 'swiper/css'
import 'swiper/css/effect-coverflow'
import { useDiary } from '@/composables/useDiary'

useHead({
    title: '轻轻日记',
})

const { dayjs, monthDiaryList, isEmpty, fetchDiaryList, handleImagePreview, parseDiaryContent } = useDiary()
const router = useRouter()
const modules = [EffectCoverflow]
const swiperInstance = ref<any>()
const showGuide = ref(false)

const todayIndex = computed(() => {
    const today = dayjs().format('YYYY-MM-DD')
    return monthDiaryList.value.findIndex(item =>
        dayjs(item.createTime).format('YYYY-MM-DD') === today,
    )
})

function onSwiper(swiper: any) {
    swiperInstance.value = swiper
    swiper.on('reachEnd', () => {
        showToast('没有更多了')
    })
    swiper.on('reachBeginning', () => {
        showToast('没有更多了')
    })
}

function goToDetail() {
    const id = monthDiaryList.value.find(item => dayjs(item.createTime).format('YYYY-MM-DD') === dayjs().format('YYYY-MM-DD'))?.id ?? 0
    router.push(`/user/checkin/diary/${id}`)
}

function goToList() {
    router.push('/user/checkin/diary/list')
}

function closeGuide() {
    showGuide.value = false
}

onMounted(() => {
    const hasShownGuide = localStorage.getItem('diaryGuideShown')
    if (!hasShownGuide) {
        showGuide.value = true
        localStorage.setItem('diaryGuideShown', 'true')
    }
})

try {
    await fetchDiaryList(
        dayjs().startOf('month').format('YYYY-MM-DD HH:mm:ss'),
        dayjs().endOf('month').format('YYYY-MM-DD HH:mm:ss'),
    )

    // 检查是否包含今天的日记
    const today = dayjs().format('YYYY-MM-DD')
    const hasTodayDiary = monthDiaryList.value.some(item =>
        dayjs(item.createTime).format('YYYY-MM-DD') === today,
    )

    // 如果没有今天的日记，添加一条空记录
    if (!hasTodayDiary) {
        monthDiaryList.value.push({
            id: 0,
            customerId: '',
            weight: 0,
            recordTime: '',
            content: '',
            pictures: '[]',
            is_deleted: 0,
            createTime: dayjs().format('YYYY-MM-DD HH:mm:ss'),
            updateTime: '',
        })
    }

    // 按照 createTime 排序
    monthDiaryList.value.sort((a, b) => {
        if (!a.createTime) return 1
        if (!b.createTime) return -1
        return dayjs(a.createTime).valueOf() - dayjs(b.createTime).valueOf()
    })
} catch (error) {
    console.error('获取日记列表失败:', error)
}
</script>

<template>
    <div class="h-100vh flex items-center justify-center bg-[url('~/assets/icons/checkin/bg-diary.svg')] bg-no-repeat bg-top bg-[length:100%_240px]">
        <!-- 引导蒙版 -->
        <div v-if="showGuide" class="fixed inset-0 z-50 bg-black/50 flex items-center justify-center" @click="closeGuide">
            <div class="relative">
                <img src="@/assets/icons/checkin/diary-guide-model.svg" width="293" height="177" />
                <img src="@/assets/icons/checkin/finger.svg" width="48" height="48" class="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-3/4" />
            </div>
        </div>
        <div class="w-100% -mt-32px">
            <div class="h-32px flex item-center justify-between px-44px mb-16px">
                <div class="flex items-center text-#1D2229 text-16px font-600">{{ dayjs().format('YYYY年MM月DD日') }}</div>
                <div class="w-76px h-100% bg-white flex items-center justify-center rd-20px text-#00AC97 text-14px font-400 cursor-pointer" @click="goToList">日记本</div>
            </div>
            <div class="flex items-center justify-between gap-16px h-403px">
                <div class="flex-1 h-100% min-w-0">
                    <swiper
                        effect="coverflow"
                        :centered-slides="true"
                        slides-per-view="auto"
                        :initial-slide="todayIndex"
                        :modules="modules"
                        class="mySwiper"
                        :coverflow-effect="{
                            stretch: 10,
                            slideShadows: false,
                        }"
                        @swiper="onSwiper"
                    >
                        <swiper-slide v-if="isEmpty" class="swiper-slide">
                            <div class="h-100% w-100% py-12px px-16px bg-white flex flex-col rd-10px">
                                <div class="h-38px flex items-center justify-between text-#868F9C font-400 text-16px shrink-0">
                                    <div><span class="text-#00AC97 text-24px font-600 mr-4px">{{ dayjs().format('DD') }}</span>日</div>
                                    <div>{{ dayjs().format('YYYY年MM月') }}</div>
                                </div>
                                <div class="flex-1 flex flex-col items-center justify-center">
                                    <img src="@/assets/icons/checkin/empty-diary.svg" width="148" height="148" />
                                    <div class="text-#868F9C text-14px">今天还未记录哦</div>
                                </div>
                            </div>
                        </swiper-slide>
                        <swiper-slide v-for="(item) in monthDiaryList" v-else :key="item.id" class="swiper-slide">
                            <div
                                class="h-100% w-100% py-12px px-16px bg-white flex flex-col rd-10px"
                            >
                                <div class="h-38px flex items-center justify-between text-#868F9C font-400 text-16px shrink-0">
                                    <div><span class="text-#00AC97 text-24px font-600 mr-4px">{{ dayjs(item.createTime).format('DD') }}</span>日</div>
                                    <div>{{ dayjs(item.createTime).format('YYYY年MM月') }}</div>
                                </div>
                                <div v-if="parseDiaryContent(item?.content)?.content" class="flex-1 flex flex-col">
                                    <div class="flex-1">
                                        <div class="my-16px text-15px text-#1D2229 font-400 line-clamp-8">
                                            {{ parseDiaryContent(item.content).content }}
                                        </div>
                                        <div class="grid grid-cols-3 gap-8px">
                                            <div v-for="(image, idx) in JSON.parse(item.pictures)" :key="idx" class="w-82px h-82px bg-white border-1px border-#F2F4F7 rd-10px overflow-hidden relative cursor-pointer">
                                                <img :src="image.url" class="w-100% h-100% rd-10px" @click.stop="handleImagePreview(item.pictures, idx)" />
                                            </div>
                                        </div>
                                    </div>
                                    <div v-if="parseDiaryContent(item.content).timestamp" class="h-35px bg-#F2F4F7 flex items-center rd-6px text-#4e5969 text-15px font-400 mt-auto pl-8px">
                                        <img src="@/assets/icons/checkin/record.svg" class="mr-4px" />{{ parseDiaryContent(item.content).timestamp }}
                                    </div>
                                </div>
                                <div v-else class="flex-1 flex flex-col items-center justify-center">
                                    <img src="@/assets/icons/checkin/empty-diary.svg" width="148" height="148" />
                                    <div class="text-#868F9C text-14px">今天还未记录哦</div>
                                </div>
                            </div>
                        </swiper-slide>
                    </swiper>
                </div>
            </div>
            <div
                class="w-287px h-50px flex items-center justify-center rd-100px bg-#00AC97 border-1px border-#00AC97 text-white text-15px font-400 mt-16px mx-auto cursor-pointer"
                @click="goToDetail"
            >
                记录今天
            </div>
        </div>
    </div>
</template>

<style scoped>
.mySwiper {
    width: 100%;
    height: 100%;
}

:deep(.swiper-wrapper .swiper-slide) {
    height: 100%;
    width: 287px;
    border-radius: 10px;
}

.swiper-slide-active {
    transform: translateY(-4px);
}
</style>
