<script setup lang="ts">
import { ref, watch } from 'vue'

import { useDiary } from '@/composables/useDiary'

useHead({
    title: '日记本',
})

const { dayjs, monthDiaryList, isEmpty, fetchDiaryList, handleImagePreview, parseDiaryContent } = useDiary()
const active = ref(0)
const selectedMonth = ref(dayjs().format('YYYY-MM-DD'))

function getDateRange(date: string) {
    const startDate = dayjs(date).startOf('month').format('YYYY-MM-DD HH:mm:ss')
    const endDate = dayjs(date).endOf('month').format('YYYY-MM-DD HH:mm:ss')
    return { startDate, endDate }
}

const initialDateRange = getDateRange(selectedMonth.value)
fetchDiaryList(initialDateRange.startDate, initialDateRange.endDate)

watch(selectedMonth, () => {
    const { startDate, endDate } = getDateRange(selectedMonth.value)
    fetchDiaryList(startDate, endDate)
})
</script>

<template>
    <div class="py-12px px-16px flex flex-col bg-white min-h-100vh">
        <user-checkin-date-switch v-model:date="selectedMonth" switch-type="month" />
        <van-steps v-if="!isEmpty" :active="active" direction="vertical" class="custom-steps">
            <van-step v-for="item in monthDiaryList" :key="item.id">
                <template #active-icon>
                    <div class="step-icon active"></div>
                </template>
                <template #inactive-icon>
                    <div class="step-icon inactive"></div>
                </template>
                <div class="flex justify-between h-25px">
                    <div class="flex justify-center gap-8px">
                        <span class="text-#868F9C text-13px font-400">{{ dayjs(item?.updateTime || item?.recordTime).format('MM-DD') }}</span>
                        <span class="text-#4E5969 text-14px font-600">{{ item.weight }}kg</span>
                    </div>
                    <div v-if="JSON.parse(item.pictures)?.length" class="flex justify-center gap-8px">
                        <span class="text-#868F9C text-13px font-400">{{ item?.pictures ? JSON.parse(item.pictures).length : 0 }}</span>
                        <img
                            src="@/assets/icons/checkin/image.svg"
                            :style="{ width: '16px', height: '16px' }"
                            @click.stop="handleImagePreview(item?.pictures)"
                        />
                    </div>
                </div>
                <div class="p-16px rd-10px bg-#F2F4F7 border-1px border-#E5E7EB text-#1D2229">
                    {{ parseDiaryContent(item.content).content }}
                </div>
            </van-step>
        </van-steps>
        <template v-else>
            <div class="flex-1 flex flex-col items-center justify-center">
                <img src="@/assets/icons/checkin/empty-diary.svg" width="148" height="148" />
                <div class="text-#868F9C text-14px">今天还未记录哦</div>
            </div>
        </template>
    </div>
</template>

<style scoped>
.custom-steps :deep(.van-step__icon) {
    width: 14px;
    height: 14px;
    background: transparent;
}

.step-icon {
    width: 14px;
    height: 14px;
    border-radius: 50%;
    border: 2px solid;
}

.step-icon.active {
    border-color: #00AC97;
}

.step-icon.inactive {
    border-color: #868F9C;
}

:deep(.van-step__line) {
    width: 2px;
    background-color: #F2F3F5;
}
</style>
