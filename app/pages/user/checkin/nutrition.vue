<script setup lang="ts">
import { cloneDeep } from 'lodash-es'

const dayjs = useDayjs()

useHead({
    title: '营养打卡',
})

const checkInDate = ref(dayjs().format('YYYY-MM-DD'))

const defaultNutritionCheckinData = {
    id: null,
    checkInDate: checkInDate.value,
    // 益生菌
    probioticsPictures: [],
    // 益生元
    prebioticsPictures: [],
}
const nutritionCheckinData = ref<NutritionCheckinData>(
    cloneDeep(defaultNutritionCheckinData),
)

const isLoading = ref(false)

async function getData() {
    try {
        isLoading.value = true
        const { results } = await useWrapFetch<BaseResponse<any>>('/checkInCustomerNutrition/get', {
            query: {
                checkInDate: checkInDate.value,
            },
        })

        if (results) {
            nutritionCheckinData.value = {
                ...results,
                probioticsPictures: JSON.parse(results.probioticsPictures || '[]'),
                prebioticsPictures: JSON.parse(results.prebioticsPictures || '[]'),
            }
        } else {
            nutritionCheckinData.value = cloneDeep(defaultNutritionCheckinData)
        }
    } catch (error) {
        console.log(error)
    } finally {
        isLoading.value = false
    }
}

getData()

watch(checkInDate, () => {
    getData()
})

const calendarRef = useTemplateRef('calendarRef')

async function handleAddImage(type: 'probioticsPictures' | 'prebioticsPictures', picture: NutritionPicture) {
    nutritionCheckinData.value[type].push(picture)
    await handleCheckin()
    showSuccessToast('添加成功')
    calendarRef.value?.refresh()
}

async function handleDeleteImage(type: 'probioticsPictures' | 'prebioticsPictures', id: string) {
    nutritionCheckinData.value[type] = nutritionCheckinData.value[type].filter(item => item.id !== id)
    await handleCheckin()
    showSuccessToast('删除成功')
}

async function handleCheckin() {
    await useWrapFetch('/checkInCustomerNutrition/save', {
        method: 'post',
        body: {
            ...nutritionCheckinData.value,
            probioticsPictures: JSON.stringify(nutritionCheckinData.value.probioticsPictures),
            prebioticsPictures: JSON.stringify(nutritionCheckinData.value.prebioticsPictures),
        },
    })

    await getData()
}
</script>

<template>
    <div>
        <user-checkin-calendar
            ref="calendarRef"
            v-model="checkInDate"
            checkin-type="nutrition"
        />

        <div class="p-16px flex flex-col gap-16px">
            <user-checkin-nutrition-checkin
                :nutrition-checkin-data="nutritionCheckinData"
                type="probioticsPictures"
                :is-loading="isLoading"
                :check-in-date="checkInDate"
                @add="handleAddImage"
                @delete="handleDeleteImage"
            />

            <user-checkin-nutrition-checkin
                :nutrition-checkin-data="nutritionCheckinData"
                type="prebioticsPictures"
                :is-loading="isLoading"
                :check-in-date="checkInDate"
                @add="handleAddImage"
                @delete="handleDeleteImage"
            />
        </div>
    </div>
</template>
