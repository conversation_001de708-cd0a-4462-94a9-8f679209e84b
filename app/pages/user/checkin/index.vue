<script setup lang="ts">
import dayjs from 'dayjs'

defineOptions({
    name: 'UserCheckinIndex',
})

definePageMeta({
    meta: {
        tabbar: true,
        layout: {
            customBg: 'bg-#F4F5F7 overflow-auto',
        },
    },
})

interface NewCheckinData {
    details: Array<{
        kind: string
        result: number
    }>
}

type TabId = (typeof tabs)[number]['id']

const { fetchArchivesStatus } = useBadgeStore()
const { startGuide, destroyGuide } = useGuide()
const startTimer = ref<ReturnType<typeof setTimeout> | null>(null)
const showCenter = ref(false)

const tabs = [
    { id: 'diet', label: '饮食' },
    { id: 'weight', label: '体重' },
    { id: 'exercise', label: '运动' },
] as const

const CHECKIN_TYPE_MAP = {
    diet: 'mealCount',
    exercise: 'sport',
    weight: 'weight',
} as const

const activeTab = useRouteQuery<TabId>('tab', 'diet')

const selectedDate = ref(dayjs().format('YYYY-MM-DD'))
const swipeDirection = ref<'left' | 'right'>('right')

const dietRef = ref()

const { data: sportData, refresh: refreshSport } = useAPI<any>('/checkInCustomerSport/list', {
    method: 'POST',
    body: computed(() => ({
        checkInDate: selectedDate.value,
    })),
})

function handleTabChange(tabId: TabId) {
    const currentIdx = tabs.findIndex(tab => tab.id === activeTab.value)
    const targetIdx = tabs.findIndex(tab => tab.id === tabId)
    swipeDirection.value = targetIdx > currentIdx ? 'left' : 'right'
    activeTab.value = tabId
    selectedDate.value = dayjs().format('YYYY-MM-DD')
}

const waterIntakeNum = ref(0)
const waistCircumferenceNum = ref(0)
const stepNum = ref(0)
const planId = ref<number | null>(null)

const touch = {
    startX: 0,
    startY: 0,
    endX: 0,
    endY: 0,
}

function isInNoSwipeElement(target: EventTarget | null): boolean {
    if (!target) return false

    const element = target as HTMLElement
    const noSwipeElement = element.closest('.no-swipe') as HTMLElement | null

    if (!noSwipeElement) return false

    if (element.tagName === 'CANVAS') {
        return element.clientWidth > 320
    }

    const hasHorizontalScroll = noSwipeElement.scrollWidth > noSwipeElement.clientWidth
    const style = window.getComputedStyle(noSwipeElement)
    const canScrollHorizontally = style.overflowX === 'auto' || style.overflowX === 'scroll'

    return hasHorizontalScroll && canScrollHorizontally
}

function isOverlayVisible() {
    const overlay = document.querySelector('.checkin-container .van-overlay')
    return overlay && getComputedStyle(overlay).display !== 'none'
}

function handleTouchStart(e: TouchEvent) {
    if (isOverlayVisible() || isInNoSwipeElement(e.target)) return
    if (!e.touches[0]) return
    touch.startX = e.touches[0].clientX
    touch.startY = e.touches[0].clientY
}

function handleTouchEnd(e: TouchEvent) {
    if (isOverlayVisible() || isInNoSwipeElement(e.target)) return
    if (!e.changedTouches[0]) return
    touch.endX = e.changedTouches[0].clientX
    touch.endY = e.changedTouches[0].clientY

    const deltaX = touch.endX - touch.startX
    const deltaY = touch.endY - touch.startY

    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
        if (deltaX < 0) {
            switchTabBySwipe('left')
        } else {
            switchTabBySwipe('right')
        }
    }
}

function switchTabBySwipe(direction: 'left' | 'right') {
    const idx = tabs.findIndex(tab => tab.id === activeTab.value)
    if (direction === 'left' && idx < tabs.length - 1) {
        swipeDirection.value = 'left'
        handleTabChange(tabs[idx + 1]!.id)
    }
    if (direction === 'right' && idx > 0) {
        swipeDirection.value = 'right'
        handleTabChange(tabs[idx - 1]!.id)
    }
}

const sportRef = useTemplateRef('sportRef')

async function getDailyStats(date: string) {
    try {
        const { results } = await useWrapFetch<BaseResponse<NewCheckinData>>('/checkInCustomerData/getDailyStats', {
            method: 'POST',
            body: {
                checkInDate: date,
            },
        })

        waterIntakeNum.value = results?.details.find(item => item.kind === 'waterIntake')?.result || 0
        waistCircumferenceNum.value = results?.details.find(item => item.kind === 'waistCircumference')?.result || 0
        stepNum.value = results?.details.find(item => item.kind === 'sport')?.result || 0

        if (dayjs().isSame(dayjs(date), 'day')) {
            sportRef.value?.syncStepFromWx()
        }
    } catch (error) {
        console.log('获取每日统计数据失败:', error)
    }
}

const { saveWeightManagementData } = useWeightManagement()

const reportGenerating = ref(localStorage.getItem('reportGenerating') === '1')
const isComponentAlive = ref(true)

const { isPolling, startPolling, stopPolling } = useCancellablePolling({
    interval: 10000,
    maxAttempts: 20,
    onError: (error, attempt) => {
        console.warn(`获取健康计划信息失败 (第${attempt}次):`, error)
    },
})

async function getHealthProgramIndexLoop() {
    if (isPolling.value) return

    const result = await startPolling<BaseResponse<HealthProgramIndex>>(
        '/api/healthProgram/getHealthProgramIndex',
        { method: 'GET' },
        (response) => {
            // 轮询条件：当报告生成完成时停止
            return response.results?.qwenReportEnd === 1
        },
    )

    if (result?.results) {
        const results = result.results

        if (!isComponentAlive.value) return

        const returnData = await saveWeightManagementData({
            healthProgramData: results,
            dietMode: results.dietPlan?.dietMode || '地中海饮食模式',
            targetWeight: JSON.parse(results.weightManagementQuestionnaire).height - 105,
            weightCheckIn: JSON.parse(results.weightManagementQuestionnaire)?.weight,
        })

        if (returnData?.[0]?.status === 'fulfilled') {
            planId.value = returnData[0].value?.results
        }

        localStorage.removeItem('reportGenerating')
        showCenter.value = true
    }
}

function startReportCheckInterval() {
    getHealthProgramIndexLoop()
}

function clearReportCheckInterval() {
    stopPolling()
}

watch(selectedDate, () => {
    getDailyStats(selectedDate.value)
    if (['diet', 'exercise'].includes(activeTab.value)) {
        refreshSport()
    }
    if (activeTab.value === 'diet') {
        dietRef.value?.refreshMeal()
    }
})

onBeforeMount(async () => {
    try {
        await getDailyStats(selectedDate.value)
    } catch (error) {
        console.log(error)
    }
})

onMounted(() => {
    const hasSeenGuide = localStorage.getItem('hasSeenGuide')
    if (!hasSeenGuide) {
        startTimer.value = setTimeout(() => {
            startGuide()
        }, 1000)
    }
})

onBeforeUnmount(() => {
    if (startTimer.value) {
        clearTimeout(startTimer.value)
    }
    isComponentAlive.value = false
    clearReportCheckInterval()
    destroyGuide()
})

if (reportGenerating.value) {
    startReportCheckInterval()
} else {
    fetchArchivesStatus()
}

async function handleCheckNow() {
    try {
        if (planId.value) {
            await useWrapFetch(`/api/healthProgram/updateReadStatus/${planId.value}/1`, {
                method: 'POST',
            })
            navigateTo(`/user/external/weight-management-plan?planId=${planId.value}`)
        }

        showCenter.value = false
    } catch (error) {
        console.error(error)
    }
}

async function handleCheckLater() {
    showCenter.value = false
    await fetchArchivesStatus()
}
</script>

<template>
    <div
        class="checkin-container px-16px pt-16px flex flex-col gap-8px overflow-y-auto overflow-x-hidden"
        @touchstart="handleTouchStart"
        @touchend="handleTouchEnd"
    >
        <user-checkin-health-manage-calendar
            v-model="selectedDate"
            :checkin-type="CHECKIN_TYPE_MAP[activeTab]"
        />

        <div class="w-full h-32px lh-32px flex gap-16px">
            <div
                v-for="tab in tabs"
                :key="tab.id"
                class="relative text-14px font-400 cursor-pointer transition-all z-0"
                :class="activeTab === tab.id ? 'text-[#1D2229] text-18px font-600' : 'text-[#868F9C]'"
                @click="handleTabChange(tab.id)"
            >
                <span class="relative z-1">{{ tab.label }}</span>
                <img
                    v-if="activeTab === tab.id"
                    src="@/assets/images/checkin/health-manage/under-curve.svg"
                    class="absolute left-1/2 -translate-x-1/2 bottom-6px -z-1"
                    width="19px"
                    height="3px"
                />
            </div>
        </div>

        <transition :name="swipeDirection === 'left' ? 'fade-slide-left' : 'fade-slide-right'" mode="out-in">
            <div :key="activeTab">
                <user-checkin-health-manage-diet
                    v-show="activeTab === 'diet'"
                    ref="dietRef"
                    :sport-data="sportData?.results"
                    :selected-date="selectedDate"
                    :water-intake-num="waterIntakeNum"
                    :step-num="stepNum"
                    :is-visible="activeTab === 'diet'"
                />
                <user-checkin-health-manage-weight
                    v-show="activeTab === 'weight'"
                    :waist-circumference-num="waistCircumferenceNum"
                    :is-visible="activeTab === 'weight'"
                />
                <user-checkin-health-manage-sport
                    v-show="activeTab === 'exercise'"
                    ref="sportRef"
                    v-model:step-num="stepNum"
                    :sport-data="sportData?.results"
                    :selected-date="selectedDate"
                    :is-visible="activeTab === 'exercise'"
                />

                <shared-safe-buttom base="60px" />
            </div>
        </transition>

        <van-popup v-model:show="showCenter" round :style="{ padding: '30px' }" :close-on-click-overlay="false" class="checkin-center-popup">
            <div class="w-255px flex flex-col items-center">
                <img src="@/assets/images/checkin/diet/popup-plan.png" width="167px" height="32px" alt="" srcset="" />
                <div class="text-t-5 text-14px">
                    根据您的问卷及检查检验结果，医生已开具针对性体重管理健康评估报告
                </div>
                <img src="@/assets/images/checkin/diet/popup-example.png" width="255px" height="83px" class="my-16px" alt="" srcset="" />
                <van-button type="primary" round class="w-255px! h-50px!" @click="handleCheckNow">
                    立即查看
                </van-button>
                <div class="w-255px text-#868F9C mt-8px text-14px font-400 text-center" @click="handleCheckLater">
                    稍后查看
                </div>
            </div>
        </van-popup>

        <user-sign-overlay />
    </div>
</template>

<style scoped>
.fade-slide-left-enter-active,
.fade-slide-left-leave-active,
.fade-slide-right-enter-active,
.fade-slide-right-leave-active {
    transition: all 0.3s cubic-bezier(.4,0,.2,1);
}

.fade-slide-left-enter-from {
    opacity: 0;
    transform: translateX(30px);
}

.fade-slide-left-leave-to {
    opacity: 0;
    transform: translateX(-30px);
}

.fade-slide-right-enter-from {
    opacity: 0;
    transform: translateX(-30px);
}

.fade-slide-right-leave-to {
    opacity: 0;
    transform: translateX(30px);
}

.fade-slide-left-enter-to,
.fade-slide-left-leave-from,
.fade-slide-right-enter-to,
.fade-slide-right-leave-from {
    opacity: 1;
    transform: translateX(0);
}

.checkin-center-popup {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 16px;
    background:
        linear-gradient(215.92deg, rgba(42, 211, 183, 0.5) -1.28%, rgba(255, 255, 255, 0.5) 55.03%),
        linear-gradient(145.07deg, rgba(166, 191, 255, 0.5) -3.55%, rgba(255, 255, 255, 0.5) 36.78%),
        #FFFFFF;  /* 底层白色背景 */
    box-shadow: 0px -3px 4px 0px #FFFFFF40 inset, 0px 2px 7px 0px #0000001F;
}
</style>
