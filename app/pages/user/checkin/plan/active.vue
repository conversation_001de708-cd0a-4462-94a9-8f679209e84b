<script setup lang="ts">
import Fasting from '@/assets/images/plan/fasting.png'
import LipidReduction from '@/assets/images/plan/lipid-reduction.png'
import MetabolicImprovement from '@/assets/images/plan/metabolic-improvement.png'

useHead({
    title: '定制计划细节',
})

const { data, status, refresh } = useAPI<{
    inProgressPlans: NewPlan[]
    pendingPlans: NewPlan[]
}>('/customerPlan/create')

const inProgressPlan = computed(() => {
    return data.value?.results?.inProgressPlans[0]
})

const dayjs = useDayjs()
function getEndDateByStartDate(startDate: string, period: number) {
    return dayjs(startDate).add(period, 'day').format('YYYY-MM-DD')
}

// async function init() {
//     const { results } = await useWrapFetch<BaseResponse<{
//         inProgressPlans: NewPlan[]
//         pendingPlans: NewPlan[]
//     }>>('/customerPlan/create')

//     planData.value = results

//     const inProgressPlan = planData.value.inProgressPlans[0]
//     const pendingPlan = planData.value.pendingPlans
// }

const activeFailDialogShow = ref(false)
async function handleActivatePlan(planId: number) {
    try {
        if (inProgressPlan.value?.planId !== 'fasting') {
            activeFailDialogShow.value = true
        } else {
            await showConfirmDialog({
                title: '您正在激活新的管理计划',
                message: '请注意，该操作不可撤销',
            })

            const { results } = await useWrapFetch<BaseResponse<boolean>>(`/customerPlan/activate/${planId}`)

            if (results) {
                showToast({
                    message: '激活成功',
                    type: 'success',
                })
                refresh()
            } else {
                showToast({
                    message: '激活失败',
                    type: 'fail',
                })
            }
        }
    } catch (error) {
        showToast({
            message: '激活失败',
            type: 'fail',
        })
        console.error(error)
    }
}
</script>

<template>
    <base-suspense :status>
        <div class="p-16px">
            <div class="bg-white rd-10px p-16px">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-8px">
                        <div class="i-custom:triangle-play w-16px h-16px"></div>

                        <div class="text-16px font-600 text-t-5">
                            当前计划
                        </div>
                    </div>

                    <div v-if="inProgressPlan?.planId !== 'fasting'" class="text-12px text-#fff bg-primary-6 rd-10px p-4px w-97px h-28px rd-10px flex items-center justify-center">
                        周期：{{ inProgressPlan?.period }}天
                    </div>

                    <div v-else class="text-12px text-#fff bg-primary-6 rd-10px p-4px w-97px h-28px rd-10px flex items-center justify-center">
                        长期有效
                    </div>
                </div>

                <img v-if="inProgressPlan?.planId === 'fasting'" class="mt-12px rd-t-10px" :src="Fasting" alt="" srcset="" />
                <img v-if="inProgressPlan?.planId === 'lipid-reduction'" class="mt-12px rd-t-10px" :src="LipidReduction" alt="" srcset="" />
                <img v-if="inProgressPlan?.planId === 'metabolic-improvement'" class="mt-12px rd-t-10px" :src="MetabolicImprovement" alt="" srcset="" />

                <div class="w-full bg-#F4F5F7 py-8px flex items-center justify-around rd-b-10px">
                    <div class="flex flex-col gap-4px items-center">
                        <div class="text-t-3">
                            开始时间
                        </div>

                        <div v-if="inProgressPlan?.startTime">
                            {{ dayjs(inProgressPlan?.startTime).format('YYYY-MM-DD') }}
                        </div>
                    </div>

                    <div class="flex flex-col gap-4px items-center">
                        <div class="text-t-3">
                            结束时间
                        </div>

                        <div v-if="inProgressPlan?.planId === 'fasting'">
                            长期有效
                        </div>

                        <div v-else>
                            {{ getEndDateByStartDate(inProgressPlan?.startTime!, Number(inProgressPlan?.period!)) }}
                        </div>
                    </div>
                </div>
            </div>

            <div v-if="data?.results.pendingPlans && data?.results.pendingPlans.length > 0">
                <div v-for="plan in data?.results.pendingPlans" :key="plan.planId" class="bg-white mt-16px rd-10px p-16px">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-8px">
                            <div class="i-custom:triangle-play w-16px h-16px"></div>

                            <div class="text-16px font-600 text-t-5">
                                待执行计划
                            </div>
                        </div>

                        <div class="text-12px text-#fff bg-primary-6 rd-10px p-4px w-97px h-28px rd-10px flex items-center justify-center">
                            周期：{{ plan.period }}天
                        </div>
                    </div>

                    <img v-if="plan.planId === 'fasting'" class="mt-12px rd-t-10px" :src="Fasting" alt="" srcset="" />
                    <img v-if="plan.planId === 'lipid-reduction'" class="mt-12px rd-t-10px" :src="LipidReduction" alt="" srcset="" />
                    <img v-if="plan.planId === 'metabolic-improvement'" class="mt-12px rd-t-10px" :src="MetabolicImprovement" alt="" srcset="" />

                    <div class="w-full bg-primary-1 py-8px flex items-center justify-around rd-b-10px" @click="handleActivatePlan(plan.planPayOrderId)">
                        <div class="text-14px text-primary-6 font-600">
                            立即激活
                        </div>
                    </div>
                </div>
            </div>

            <van-dialog v-model:show="activeFailDialogShow">
                <div class="p-24px flex flex-col items-center gap-16px">
                    <img src="@/assets/icons/checkin/dialog-error.svg" class="w-48px h-48px" />
                    <div class="text-16px font-600">激活失败</div>
                    <div class="text-t-4 text-16px">
                        存在进行中的体重管理计划，无法直接激活，如有需要，请联系客服人员处理
                    </div>

                    <img class="w-146px h-146px" src="@/assets/images/common/qrcode-2.png" alt="" srcset="" />
                </div>
            </van-dialog>
        </div>
    </base-suspense>
</template>
