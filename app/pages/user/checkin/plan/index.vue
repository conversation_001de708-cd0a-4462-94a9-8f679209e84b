<script setup lang="ts">
useHead({
    title: '体重管理计划',
})

const router = useRouter()
const { plans, setInUsePlan } = usePlans()

onMounted(async () => {
    const planId = localStorage.getItem('currentPlanId')
    await nextTick()
    if (planId) {
        setInUsePlan(planId as PlanId)
    }
})

function handlePlanClick(planId: PlanId) {
    router.replace(`/user/checkin/plan/${planId}`)
}
</script>

<template>
    <div class="bg-white h-100vh p-16px">
        <div class="flex flex-col gap-16px">
            <div
                v-for="plan in plans"
                :key="plan.id"
                class="h-118px flex items-center justify-between rounded-10px relative cursor-pointer"
                :style="{
                    background: plan.gradient,
                }"
                @click="handlePlanClick(plan.id as PlanId)"
            >
                <div
                    v-if="plan.isInUse"
                    class="absolute top-0 left-0 -translate-y-1/2 w-80px h-22px rounded-tl-10px rounded-tr-10px rounded-br-10px p-8px flex items-center justify-center text-10px text-white bg-[#F98804] z-10"
                    style="transform-origin: top left"
                >
                    当前使用中
                </div>
                <div
                    class="absolute top-0 right-0 w-60px h-22px rounded-bl-10px rounded-tr-10px p-8px flex items-center justify-center text-10px text-white bg-#00AC97 z-10"
                >
                    <img
                        v-if="plan.showStar"
                        src="~/assets/icons/star.svg"
                        class="w-12px h-12px mr-4px"
                    />
                    {{ plan.level }}
                </div>
                <div class="flex flex-col pl-16px gap-4px">
                    <div class="text-16px text-#1D2229 font-600">
                        {{ plan.title }}
                    </div>
                    <div class="text-14px text-#4E5969">
                        {{ plan.description }}
                    </div>
                    <div class="flex items-center gap-8px">
                        <div
                            v-for="(tag, tagIndex) in plan.tags"
                            :key="tagIndex"
                            class="flex items-center justify-center w-fit h-29px py-3.5px px-12px rounded-100px border border-#6AD9CB text-#00AC97 text-12px"
                        >
                            {{ tag }}
                        </div>
                    </div>
                </div>
                <img
                    :src="plan.cover"
                    class="absolute right-8px bottom-8px"
                    :class="plan.size"
                />
            </div>
        </div>
    </div>
</template>
