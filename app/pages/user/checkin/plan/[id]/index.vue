<script setup lang="ts">
const route = useRoute()
const router = useRouter()
const planId = route.params.id as PlanId
const { getPlanById } = usePlans()

const currentPlan = getPlanById(planId)

useHead({
    title: currentPlan ? currentPlan.title : '计划详情',
})

async function handlePay() {
    const wx = await useWxBridge({})

    wx?.miniProgram.navigateTo({
        url: `/pages/order/pay?reportId=${route?.query?.reportId}`,
    })
}
</script>

<template>
    <div class="bg-#F4F5F7" :class="!route?.query?.reportId ? 'pb-16px' : 'pb-60px'">
        <div
            class="relative h-277px px-16px pt-32px"
            :style="{
                background: currentPlan.gradient,
                zIndex: 5,
            }"
        >
            <img :src="currentPlan.detail.coverImage" class="w-112px h-112px absolute top-0 right-0" style="z-index: 1" />
            <div
                :style="{
                    color: currentPlan.detail.titleColor,
                    position: 'relative',
                    zIndex: 2,
                }"
            >
                <div class="flex items-center h-27px text-15px font-400">
                    {{ currentPlan.detail.subTitle }}
                </div>
                <div class="flex items-center h-32px text-20px font-900">
                    {{ currentPlan.title }}
                </div>
            </div>
        </div>
        <div class="-mt-160px" style="position: relative; z-index: 10">
            <object v-for="(image, index) in currentPlan.detail.images" :key="index" type="image/svg+xml" :data="`/no-cache/plan-images/${planId}/${image}`" class="w-100% h-auto"></object>
        </div>
        <div v-if="!route?.query?.reportId" class="flex justify-center">
            <button
                class="w-233px h-50px rounded-100px pt-6px pr-16px pb-6px pl-16px flex items-center justify-center text-16px font-medium"
                :style="{
                    background: '#00ac97',
                    border: '1px solid #00ac97',
                    color: 'white',
                }"
                @click="router.replace('/user/checkin/plan')"
            >
                查看其他计划
            </button>
        </div>
        <div v-else className="fixed  bottom-0 bg-[#fff] px-[16px] w-full flex dynamic-height z-11">
            <div className=" safe-area-height w-full flex justify-between items-center">
                <div className="flex items-center gap-[3px]">
                    <span className="text-[12px] text-red-500 font-[600]"> ¥ </span>

                    <span className="text-[17px] text-red-500 font-[600]">0.</span>

                    <span className="text-[13px] text-red-500 font-[600] relative top-[1px]">00</span>

                    <div className="text-[#868F9C] text-[13px] line-through relative top-[2px] left-[3px]">{{ route?.query?.price }}.00</div>
                </div>

                <van-button v-if="route?.query?.payStatus !== 'SUCCESS'" type="success" color="#00AC97" round @click="handlePay">立即下单</van-button>

                <div v-else class="text-gray-500 text-[12px]">订单已完成</div>
            </div>
        </div>
    </div>
</template>

<style scoped>
.safe-area-height {
    height: calc(56px + constant(safe-area-inset-bottom));
    height: calc(56px + env(safe-area-inset-bottom))
}
</style>
