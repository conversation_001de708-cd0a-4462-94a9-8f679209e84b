<script setup lang="ts">
import DietPatternCard from '~/components/user/checkin/diet-pattern/diet-pattern-card.vue'

const props = withDefaults(defineProps<{
    isReplace?: boolean
}>(), {
    isReplace: true,
})

const router = useRouter()

const { createPatterns } = useDietPatterns()

const { diaryId } = storeToRefs(useDiaryStore())

const patterns = computed(() => createPatterns(diaryId.value as PatternId))

function navigateToDetail(patternId: PatternId): void {
    const method = props.isReplace ? 'replace' : 'push'
    router[method]({
        path: `/user/checkin/diet-pattern/${patternId}`,
    })
}
</script>

<template>
    <div class="bg-white p-16px">
        <div class="flex flex-col gap-16px">
            <diet-pattern-card
                v-for="pattern in patterns"
                :key="pattern.title"
                :pattern="pattern"
                :show-active-tag="true"
                size="small"
                @click="navigateToDetail(pattern.id)"
            />
        </div>
    </div>
</template>
