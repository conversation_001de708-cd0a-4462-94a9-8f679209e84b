<script setup lang="ts">
import { dietary } from '@/utils/weight-management-constants'

const route = useRoute()
const router = useRouter()

const { getPatternById } = useDietPatterns()

const patternId = computed(() => route.params.id as PatternId)
const notRecommendedPatterns = ref<{ name?: string, reason?: string }[]>([])
const toggleShow = ref(false)
const errorShow = ref(false)

const currentPattern = computed(() => {
    try {
        return getPatternById(patternId.value)
    } catch {
        return getPatternById('mediterranean')
    }
})

const recommendedPattern = computed((): DietaryPlanType => {
    const pattern = currentPattern.value?.title
    return pattern?.replace('饮食', '') as DietaryPlanType
})
const recommendedPatternName = computed(() => recommendedPattern.value.startsWith('低碳水化合物膳食') ? recommendedPattern.value.replace('膳食', '') : recommendedPattern.value)
const notRecommendedPattern = computed(() => notRecommendedPatterns.value.find(item => item.name?.replace('饮食模式', '') === recommendedPattern.value))

const { diaryName, diaryId, isUseWeightPlanMode } = storeToRefs(useDiaryStore())

async function handleChangeDietaryPattern() {
    try {
        if (notRecommendedPattern.value) {
            toggleShow.value = false
            errorShow.value = true
            return
        }

        const { results } = await useWrapFetch<BaseResponse<boolean>>('/dietary-patterns', {
            method: 'put',
            params: {
                dietaryPatternsName: `${currentPattern.value.title}模式`,
            },
        })
        if (results) {
            diaryName.value = currentPattern.value.title
            diaryId.value = patternId.value
            isUseWeightPlanMode.value = false
            showSuccessToast('饮食方案切换成功')
        } else {
            showFailToast('饮食方案切换失败')
        }

        toggleShow.value = false

        router.back()
    } catch (error) {
        showFailToast('饮食方案切换失败')
    }
}

const isActivePattern = computed(() => {
    return patternId.value === diaryId.value
})

const defaultInterventionPlan = {
    suggestions: {
        nutrition_recommendations: '',
        nutritional_tips: {},
    },
}

const { data: interventionPlan, refresh: refreshInterventionPlan }
    = useAsyncData(
        'intervention-plan',
        async () => {
            try {
                const response = await useWrapFetch<BaseResponse<string>>(
                    '/patient-assessment/getInterventionPlanByToken',
                )
                return JSON.parse(response.results) || defaultInterventionPlan
            } catch (error) {
                console.error('获取干预计划失败:', error)
                return defaultInterventionPlan
            }
        },
        {
            immediate: false,
        },
    )

watch(
    isActivePattern,
    async (newValue) => {
        if (newValue) {
            await refreshInterventionPlan()
        }
    },
    { immediate: true },
)

function validatePatternId(id: PatternId): boolean {
    try {
        getPatternById(id)
        return true
    } catch {
        return false
    }
}

watch(
    patternId,
    (newId) => {
        if (!validatePatternId(newId)) {
            router.push('/user/checkin/diet-pattern')
        }
    },
    { immediate: true },
)

useHead({
    title: `${currentPattern.value.title}模式`,
})

async function getLatestHealthProgramData() {
    try {
        const { results, state } = await useWrapFetch<BaseResponse<any[]>>('/healthProgram/listHealthProgramPlan')
        if (state === 200 && results?.length) {
            notRecommendedPatterns.value = JSON.parse(results[0]?.dietPlan?.dietDetail)?.notRecommendedPatterns || []
        }
    } catch (error) {
        console.error('获取健康计划数据失败:', error)
    }
}

onMounted(() => {
    getLatestHealthProgramData()
})
</script>

<template>
    <div class="flex bg-#F4F5F7 flex-col p-16px pb-80px gap-16px" :style="{ backgroundColor: currentPattern.cardBackgroundColor }">
        <div class="w-full bg-white p-16px flex flex-col gap-16px rd-12px">
            <div class="flex flex-col justify-center items-center rd-10px w-full h-89px bg-gradient-to-b from-[#e6f7f5] to-[#fafdfd] border-solid border-1px border-#e5f7f5">
                <div v-if="recommendedPattern" class="text-#00AC97 text-26px font-600">{{ recommendedPatternName }}饮食</div>
                <div class="text-#333333 text-14px font-400">根据健康优先级，建议选择</div>
            </div>
            <div class="w-full flex flex-col gap-8px">
                <div class="flex flex-col gap-8px">
                    <div v-if="recommendedPattern" class="text-#00AC97 text-14px font-600">{{ recommendedPatternName }}饮食原则/特点</div>
                    <div v-if="recommendedPattern && dietary.principles" class="text-#505D5B text-14px font-300">{{ dietary.principles[recommendedPattern] }}</div>
                </div>

                <div class="flex flex-col gap-8px">
                    <div v-if="recommendedPattern" class="text-#00AC97 text-14px font-600">{{ recommendedPatternName }}饮食的食物宜忌</div>
                    <div v-if="recommendedPattern && dietary.recommendedFoods && dietary.forbiddenFoods">
                        <div class="flex flex-col w-full relative">
                            <div class="text-#505D5B text-14px font-500 flex justify-center items-center bg-#00AC971A h-26px">推荐食物</div>
                            <div class="absolute top-26px bottom-0 left-1/4 w-[0.5px] bg-#00AC97"></div>
                            <div
                                v-for="(food, index) in dietary.recommendedFoods[recommendedPattern]"
                                :key="food.category"
                                class="flex bg-#4BFFE90D"
                                :class="{ 'border-b-0.5px border-b-dashed border-b-#E0EEED': index !== dietary.recommendedFoods[recommendedPattern].length - 1 }"
                            >
                                <div class="flex justify-center items-center w-1/4 text-#505D5B text-10px font-500">
                                    {{ food.category }}
                                </div>
                                <div class="flex-1 p-8px text-#505D5B text-10px font-300">
                                    {{ food.items }}
                                </div>
                            </div>
                        </div>
                        <div class="flex flex-col w-full relative">
                            <div class="text-#505D5B text-14px font-500 flex justify-center items-center bg-#FFA41F26 h-26px">限制/禁忌食物</div>
                            <div class="absolute top-26px bottom-0 left-1/4 w-[0.5px] bg-#FFB852"></div>
                            <div
                                v-for="(food, index) in dietary.forbiddenFoods[recommendedPattern]"
                                :key="food.category"
                                class="flex bg-#FFA41F0D"
                                :class="{ 'border-b-0.5px border-b-dashed border-b-#E0EEED': index !== dietary.forbiddenFoods[recommendedPattern].length - 1 }"
                            >
                                <div class="flex justify-center items-center w-1/4 text-#505D5B text-10px font-500">
                                    {{ food.category }}
                                </div>
                                <div class="flex-1 p-8px text-#505D5B text-10px font-300">
                                    {{ food.items }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col gap-8px">
                    <div v-if="recommendedPattern" class="text-#00AC97 text-14px font-600">{{ recommendedPatternName }}饮食的一日饮食示例</div>
                    <div v-if="recommendedPattern && dietary.examples && dietary.examples[recommendedPattern]">
                        <div class="flex flex-col w-full relative">
                            <div class="absolute top-0 bottom-0 left-1/4 w-[0.5px] bg-#00AC97"></div>
                            <div
                                v-for="(food, index) in dietary.examples[recommendedPattern].dayFoods"
                                :key="food.category"
                                class="flex bg-#4BFFE90D"
                                :class="{ 'border-b-0.5px border-b-dashed border-b-#E0EEED': index !== dietary.examples[recommendedPattern].dayFoods.length - 1 }"
                            >
                                <div class="flex justify-center items-center w-1/4 text-#505D5B text-10px font-500">
                                    {{ food.category }}
                                </div>
                                <div class="flex-1 p-8px text-#505D5B text-10px font-300">
                                    {{ food.items }}
                                </div>
                            </div>
                        </div>
                        <ul class="text-#505D5B text-14px font-300 list-disc marker:text-#505D5B pl-16px mt-8px">
                            <li v-for="(item, index) in dietary.examples[recommendedPattern].fullDay" :key="index">
                                {{ item }}
                            </li>
                            <li class="font-500">您可以进入小程序中获取食物替换建议，以丰富膳食种类、均衡营养摄入。</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <div class="w-full fixed bottom-0 my-20px left-0 right-0 flex px-20px justify-center items-center gap-16px">
            <van-button class="w-263px! h-50px!" type="primary" round @click="router.replace(`/user/checkin/diet-pattern`)">
                查看其他饮食方案
            </van-button>

            <van-button v-if="!isActivePattern" class="w-263px! h-50px! bg-white!" plain type="primary" round @click="toggleShow = true">
                使用当前饮食方案
            </van-button>
        </div>

        <van-dialog v-model:show="toggleShow" theme="round-button" :show-confirm-button="false">
            <div class="flex flex-col items-center justify-center p-16px gap-16px">
                <div class="text-#4E5969 text-16px font-400 text-center">切换饮食方案</div>
                <div class="text-#4E5969 text-16px font-400 text-center">
                    切换后你的饮食方案等数据将同步更新，是否要切换？
                </div>

                <div class="flex justify-between items-center gap-16px mt-32px">
                    <van-button class="w-120px h-40px" color="#E4FAF9" type="primary" @click="toggleShow = false">
                        <span class="text-#00AC97">取消</span>
                    </van-button>

                    <van-button class="w-120px h-40px" type="primary" @click="handleChangeDietaryPattern">
                        确定
                    </van-button>
                </div>
            </div>
        </van-dialog>

        <van-dialog v-model:show="errorShow" theme="round-button" :show-confirm-button="false">
            <div class="flex flex-col items-center justify-center p-16px gap-16px">
                <img src="@/assets/icons/checkin/dialog-error.svg" class="w-48px h-48px" />
                <div class="text-#4E5969 text-16px font-400 text-center">切换失败</div>
                <div class="text-#4E5969 text-16px font-400 text-center"> {{ notRecommendedPattern?.reason }}</div>

                <div class="flex justify-between items-center gap-16px mt-32px">
                    <van-button class="w-120px h-40px" color="#E4FAF9" type="primary" @click="errorShow = false">
                        <span class="text-#00AC97">取消</span>
                    </van-button>
                    <van-button class="w-120px h-40px" type="primary" @click="errorShow = false">
                        确定
                    </van-button>
                </div>
            </div>
        </van-dialog>
    </div>
</template>
