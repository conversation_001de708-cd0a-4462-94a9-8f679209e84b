<script setup lang="ts">
const dayjs = useDayjs()

const { fastingTips } = useFastingTips()
const fullCircleProgressRef = useTemplateRef('fullCircleProgressRef')
const tipRefs = ref<HTMLDivElement[]>([])
const activeTipIndex = ref(0)
const activeTip = computed(() => fastingTips.value[activeTipIndex.value])

watch(() => fullCircleProgressRef.value?.fastingState, async (val) => {
    if (val?.type === 1) {
        const hourAfterLastMeal = dayjs().diff(dayjs(val.startAt), 'hours')
        for (let i = 0; i < fastingTips.value.length; i++) {
            const isActive = hourAfterLastMeal >= fastingTips.value[i]!.timeRange.min && hourAfterLastMeal <= fastingTips.value[i]!.timeRange.max
            fastingTips.value[i]!.isActive = isActive
            if (isActive) {
                activeTipIndex.value = i
                await nextTick()
                tipRefs.value[i]?.scrollIntoView({ behavior: 'smooth', block: 'nearest', inline: 'start' })
            }
        }
    }
}, { deep: true })
</script>

<template>
    <div style="background: linear-gradient(180deg, #EDFFFD 0%, #F4F5F7 100%);" class="h-395px p-16px">
        <div class="bg-white rd-10px px-16px py-12px">
            <user-checkin-fasting-full-circle-progress ref="fullCircleProgressRef" />
        </div>

        <div class="bg-white mt-16px rd-10px px-16px py-12px">
            <div class="text-t-5 font-600 text-15px">
                身体状态
            </div>

            <div class="mt-16px flex gap-12px overflow-x-auto scrollbar-hide">
                <div
                    v-for="(tip, index) in fastingTips" :key="index"
                    :ref="el => tipRefs[index] = el as HTMLDivElement"
                    class="w-48px h-56px flex-shrink-0 bg-fill-1 rd-10px flex items-center justify-center"
                    :class="activeTipIndex === index ? 'bg-primary-1! border-1px border-primary-6! border-solid' : ''"
                    @click="activeTipIndex = index"
                >
                    <img class="w-28px h-28px" :class="activeTipIndex === index ? 'op-100' : 'op-50'" :src="tip.iconUrl" alt="" srcset="" />
                </div>
            </div>

            <div class="mt-16px rd-10px bg-#F4F5F7 px-16px py-12px">
                <div class="flex items-center justify-between">
                    <div class="text-16px text-t-5 font-600">
                        {{ activeTip?.title }}
                    </div>

                    <div v-if="activeTip?.isActive" class="rd-100px bg-warning-6 w-66px h-25px flex items-center justify-center text-12px text-white">
                        当前状态
                    </div>
                </div>

                <div class="flex my-10px">
                    <div class="bg-warning-1 rd-100px h-25px flex items-center justify-center gap-3px px-8px py-2px">
                        <div class="i-custom:clock w-10px h-10px"></div>
                        <div class="text-12px text-warning-6">{{ activeTip?.timeRangeShow }}</div>
                    </div>
                </div>

                <div class="text-13px text-t-4 tracking-2px">
                    {{ activeTip?.desc }}
                </div>
            </div>
        </div>
    </div>
</template>
