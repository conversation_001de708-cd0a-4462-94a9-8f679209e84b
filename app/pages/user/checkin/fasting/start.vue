<script setup lang="ts">
const dragableCircleRef = useTemplateRef('dragableCircleRef')

const dayjs = useDayjs()

const { fastingState } = useFasting()

const router = useRouter()

watch(fastingState, (val) => {
    if (val.isChallenge === true) {
        router.replace('/user/checkin/fasting')
    }
}, { immediate: true, deep: true })

async function handleStart() {
    try {
        const now = dayjs().format('YYYY-MM-DD HH:mm:ss')

        const startFastingForm = {
            type: 0,
            text: '进食期',
            startTime: '',
            endTime: '',
        }

        if (dayjs(now).isBefore(dragableCircleRef.value?.timeRange.startTime)) {
            startFastingForm.type = 1
            startFastingForm.text = '断食期'
            startFastingForm.startTime = now
            startFastingForm.endTime = dayjs(dragableCircleRef.value?.timeRange.startTime).format('YYYY-MM-DD HH:mm:ss')
        } else if (dayjs(now).isAfter(dragableCircleRef.value?.timeRange.startTime) && dayjs(now).isBefore(dragableCircleRef.value?.timeRange.endTime)) {
            startFastingForm.type = 0
            startFastingForm.text = '进食期'
            startFastingForm.startTime = dayjs(dragableCircleRef.value?.timeRange.startTime).format('YYYY-MM-DD HH:mm:ss')
            startFastingForm.endTime = dayjs(dragableCircleRef.value?.timeRange.endTime).format('YYYY-MM-DD HH:mm:ss')
        } else if (dayjs(now).isAfter(dragableCircleRef.value?.timeRange.endTime)) {
            startFastingForm.type = 1
            startFastingForm.text = '断食期'
            startFastingForm.startTime = now
            startFastingForm.endTime = dayjs(dragableCircleRef.value?.timeRange.startTime).add(1, 'day').format('YYYY-MM-DD HH:mm:ss')
        }

        const { state } = await useWrapFetch<BaseResponse<any>>('/light-fasting-record', {
            method: 'POST',
            body: startFastingForm,
        })

        if (state === 302) {
            showFailToast('当前时间段已有记录')
        } else {
            router.replace('/user/checkin/fasting')
        }
    }
    catch (error) {
        console.log(error)
    }
}
</script>

<template>
    <div style="background: linear-gradient(180deg, #EDFFFD 0%, #F4F5F7 100%);" class="h-395px p-16px">
        <div class="rd-10px h-[calc(100vh-40px)] overflow-y-auto" style="background: linear-gradient(180deg, #B9FFF7 0%, #FFFFFF 25.76%);">
            <div class="px-16px py-12px ">
                <div class="flex items-center justify-between">
                    <div class="text-17px text-t-5 font-600">
                        轻断食 16-8 模式
                    </div>

                    <div class="h-25px w-66px bg-#fff rounded-10px flex items-center justify-center text-12px text-primary-6">
                        经典模式
                    </div>
                </div>

                <div class="text-12px text-t-3 my-4px">
                    8小时进食，16小时断食
                </div>

                <div class="text-15px text-t-5">
                    每日8小时进食（如9-17点），16小时禁食（饮水不限）。具有促进脂肪代谢，稳定血糖，高效减脂，优化肠道的功能。助力您轻松减重，提升健康。
                </div>

                <div class="flex items-center gap-8px mt-16px">
                    <div class="h-15px w-4px rd-2px bg-primary-6"></div>

                    <div class="text-15px text-t-5 font-600">
                        设置饮食时间
                    </div>
                </div>

                <user-checkin-dragable-circle ref="dragableCircleRef" />

                <div class="flex justify-center mt-16px">
                    <van-button type="primary" round class="w-233px! h-50px! text-16px!" @click="handleStart">
                        开始挑战
                    </van-button>
                </div>

                <div class="w-full rd-10px px-16px py-8px mt-16px" style="background: linear-gradient(97.04deg, #E4FAF9 0%, #F7FFFE 101.14%);">
                    <div class="text-primary-6 text-15px font-600">
                        断食小知识
                    </div>

                    <ul class="relative left-12px mt-4px">
                        <li>
                            请注意补充适当的水分
                        </li>

                        <li>
                            进食期不要暴饮暴食，尽量控制碳水摄入
                        </li>

                        <li>
                            注意区分身体饥饿和情绪饥饿
                        </li>

                        <li>
                            不建议您在孕期及身体不适状态下进行轻断食
                        </li>

                        <li>
                            适当避免高强度的无氧运动
                        </li>

                        <li>
                            如有不适，建议咨询营养师及专业医生意见
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</template>

<style scoped>
ul {
    list-style: disc;
    --uno: text-t-4 text-14px;
}
</style>
