<script setup lang="ts">
definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

useHead({
    title: '断食记录',
})

interface TimelineItem {
    date: string
    items: {
        type: 0 | 1
        startTime: string
        duration: string
        endTime: string
    }[]
}

const timelineData = ref<TimelineItem[]>([])

const { data, status } = useAPI<{
    startTime: string
    endTime: string
    type: 0 | 1
}[]>('/light-fasting-record/list')

const dayjs = useDayjs()

function calcDuration(startTime: string, endTime: string) {
    let duractionText = ''
    const diff = dayjs(endTime).diff(dayjs(startTime), 's')
    const hours = Math.floor(diff / 3600)
    const minutes = Math.floor((diff % 3600) / 60)

    if (hours > 0) {
        duractionText += `${hours}小时`
    }

    if (minutes > 0) {
        duractionText += `${minutes}分钟`
    }

    return duractionText || '不足1分钟'
}

watch(data, (val) => {
    if (val) {
        val.results.filter(i => !!i.endTime).forEach((item) => {
            const isDateExist = timelineData.value.findIndex(timelineItem => timelineItem.date === dayjs(item.startTime).format('YYYY-MM-DD'))
            if (isDateExist === -1) {
                timelineData.value.push({
                    date: dayjs(item.startTime).format('YYYY-MM-DD'),
                    items: [{ type: item.type, startTime: item.startTime, duration: calcDuration(item.startTime, item.endTime), endTime: item.endTime }],
                })
            } else {
                timelineData.value[isDateExist]!.items.push({ type: item.type, startTime: item.startTime, duration: calcDuration(item.startTime, item.endTime), endTime: item.endTime })
            }
        })
    }
})

function isToday(date: string) {
    return dayjs(date).isSame(dayjs(), 'day')
}
</script>

<template>
    <div class="p-20px">
        <template v-if="status === 'success'">
            <template v-if="timelineData.length > 0">
                <div v-for="(day, index) in timelineData" :key="index" class="flex items-start relative gap-10px">
                    <!-- Date circle and line -->
                    <div class="flex flex-col items-center justify-center">
                        <svg class="w-18px h-18px z-1000" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <circle cx="9" cy="9" r="6" fill="white" :stroke="isToday(day.date) ? '#00AC97' : '#868F9C'" style="stroke-width: 2px" />
                        </svg>
                        <div v-if="index !== timelineData.length - 1" class="h-full top-2px absolute w-2px bg-#F2F3F5"></div>
                    </div>

                    <!-- Timeline items -->
                    <div class="flex-1 min-h-full">
                        <div class="text-t-3 text-13px">{{ day.date }}</div>

                        <div
                            v-for="(item, itemIndex) in day.items"
                            :key="itemIndex"
                            class="rd-10px p-16px my-12px"
                            :class="item.type === 0 ? 'bg-primary-1' : 'bg-warning-1'"
                        >
                            <div class="text-13px font-600" :class="item.type === 0 ? 'text-primary-6' : 'text-warning-6'">{{ item.type === 0 ? '进食期' : '断食期' }}</div>
                            <div class="flex items-center justify-between my-8px">
                                <div class="text-16px font-600 text-t-5">{{ item.duration }}</div>
                                <div class="text-13px text-t-3">
                                    间歇性断食 16:8 模式
                                </div>
                            </div>

                            <div class="bg-white rd-8px p-12px">
                                <div class="flex justify-between items-center mb-8px last:mb-0">
                                    <span class="text-t-3 text-13px">开始时间： {{ item.startTime }}</span>
                                </div>
                                <div class="flex justify-between items-center mb-8px last:mb-0">
                                    <span class="text-t-3 text-13px">结束时间： {{ item.endTime }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>
            <template v-else>
                <van-empty description="暂无断食记录" />
            </template>
        </template>

        <template v-else-if="status === 'pending'">
            <van-skeleton>
                <template #template>
                    <div class="flex flex-1 mt-10px">
                        <div :style="{ flex: 1 }">
                            <van-skeleton-paragraph class="h-160px! rd-10px" />
                            <van-skeleton-paragraph class="h-160px! rd-10px" />
                            <van-skeleton-paragraph class="h-160px! rd-10px" />
                            <van-skeleton-paragraph class="h-160px! rd-10px" />
                        </div>
                    </div>
                </template>
            </van-skeleton>
        </template>
    </div>
</template>
