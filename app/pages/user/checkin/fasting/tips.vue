<script setup lang="ts">
useHead({
    title: '断食期说明',
})

const { fastingTips } = useFastingTips()
</script>

<template>
    <div style="background: linear-gradient(180deg, #FFF9ED 0%, #FFFFFF 45.35%);" class="h-screen pt-24px px-16px flex flex-col gap-16px">
        <div
            v-for="(tip, index) in fastingTips"
            :key="index"
        >
            <div class="flex items-center gap-16px">
                <div class="flex-shrink-0 w-46px">
                    <img
                        :src="tip.iconUrl"
                        :alt="`fasting tip ${index + 1}`"
                    />

                    <div class="text-t-4 text-13px">
                        {{ tip.timeRangeShow }}
                    </div>
                </div>

                <div>
                    <div class="text-t-5 font-600 text-16px">
                        {{ tip.title }}
                    </div>

                    <div class="text-13px text-t-4">
                        {{ tip.desc }}
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
