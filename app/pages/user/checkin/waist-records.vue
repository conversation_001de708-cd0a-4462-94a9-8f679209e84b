<script setup lang="ts">
import dayjs from 'dayjs'

const { data: waistDataList, refresh: refreshWaistDataList, status: waistDataListStatus } = useAPI<{
    results: {
        waistCircumferenceList: {
            checkInDate: string
            waistCircumference: number
        }[]
    }
}>('/api/checkInCustomerWaistCircumference/getWaistCircumferenceTrendByTypeWithTrend', {
    method: 'GET',
    params: {
        type: 'all',
    },
})
</script>

<template>
    <div class="p-16px h-100vh overflow-y-auto">
        <div
            v-if="waistDataListStatus === 'success'"
            class="bg-white rd-10px p-16px flex flex-col gap-12px w-full h-full overflow-y-auto"
        >
            <div class="flex items-end gap-8px text-t-5 text-14px font-600">
                腰围记录
            </div>
            <div class="flex flex-col gap-6px">
                <div v-for="item in waistDataList?.results?.results.waistCircumferenceList" :key="item.checkInDate" class="flex items-center justify-between gap-8px">
                    <div class="text-t-4 text-13px">{{ dayjs(item.checkInDate).format('MM月DD日') }}</div>
                    <div class="flex items-center gap-8px text-t-5 text-18px font-600">
                        {{ item.waistCircumference }}cm
                    </div>
                </div>
            </div>
        </div>

        <div
            v-else
            class="bg-white p-16px rd-10px w-full h-full flex items-center justify-center"
        >
            <shared-unified-loading />
        </div>
    </div>
</template>
