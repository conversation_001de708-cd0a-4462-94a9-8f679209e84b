<script setup lang="ts">
import { generateCheckinFoodRoots } from '@/utils/diet'
import { canShowEncourageToday, computeDietWeekStatus, markEncourageShownToday } from '@/utils/common'

useHead({
    title: '饮食打卡',
})

const dayjs = useDayjs()
const checkInDate = useRouteQuery('date', dayjs().format('YYYY-MM-DD'))
const dietCheck = useRouteQuery<'0' | '1'>('dietCheck', '0')
const showEncouragePopup = ref(false)
const dietWeekStatus = ref<boolean[]>([])
const continuousCheckinDays = ref(0)
const { userInfo } = storeToRefs(useUserStore())

const tabs = [
    {
        title: '早餐',
        name: 'breakfast',
    },
    {
        title: '午餐',
        name: 'lunch',
    },
    {
        title: '晚餐',
        name: 'dinner',
    },
    {
        title: '加餐',
        name: 'snack',
    },
]

const checkinFoodRoots = ref<CheckinFoodRoot[]>([])
provide('checkinFoodRoots', checkinFoodRoots)

const filteredCheckinFoodRoots = computed(() => {
    return checkinFoodRoots.value.filter(item => item.mealContent.items.length > 0)
})

const totalFoodCount = computed(() => {
    return checkinFoodRoots.value.reduce((acc, item) => acc + (item.mealContent.items as FoodItem[]).length, 0)
})

const isLoading = ref(false)
async function getFoodCheckinData() {
    try {
        isLoading.value = true
        const { results } = await useWrapFetch<BaseResponse<any[]>>('/checkInCustomerMeal/list', {
            method: 'POST',
            body: {
                checkInDate: checkInDate.value,
            },
        })

        checkinFoodRoots.value = results?.map((item) => {
            const mealContent = JSON.parse(item.mealContent)

            // 兼容旧数据
            if (Array.isArray(mealContent)) {
                return {
                    ...item,
                    mealContent: {
                        kind: 'recommend',
                        items: mealContent[0],
                    },
                }
            }

            return {
                ...item,
                mealContent,
            }
        }) || []

        const mealTypes = ['breakfast', 'lunch', 'dinner', 'snack'] as DietType[]
        mealTypes.forEach((kind) => {
            if (!checkinFoodRoots.value.find(item => item.kind === kind)) {
                checkinFoodRoots.value.push(generateCheckinFoodRoots(kind, checkInDate.value))
            }
        })

        // 按照指定顺序排序
        checkinFoodRoots.value.sort((a, b) => {
            const orderMap = { breakfast: 0, lunch: 1, dinner: 2, snack: 3 }
            return orderMap[a.kind] - orderMap[b.kind]
        })

        // setFoodItems()
    } catch (error) {
        console.log(error)
    } finally {
        isLoading.value = false
    }
}

watch(checkInDate, () => {
    getFoodCheckinData()
}, {
    immediate: true,
})

async function handlePictureFoodAdd(food: FoodItem[], dietType: DietType, mealPicture: string, mealEvaluation: string) {
    const findIndex = checkinFoodRoots.value.findIndex(item => item.kind === dietType)
    checkinFoodRoots.value[findIndex]!.calorie = food.reduce((acc, item) => acc + item.calories, 0)
    checkinFoodRoots.value[findIndex]!.mealContent.kind = 'picture'
    checkinFoodRoots.value[findIndex]!.mealContent.pictures = [
        ...(checkinFoodRoots.value[findIndex]!.mealContent.pictures as string[]),
        mealPicture,
    ]
    checkinFoodRoots.value[findIndex]!.mealContent.items = [
        ...(checkinFoodRoots.value[findIndex]!.mealContent.items as FoodItem[]),
        ...food,
    ]
    if (mealEvaluation) {
        if (!checkinFoodRoots.value[findIndex]!.mealContent?.mealEvaluationList?.includes(mealEvaluation)) {
            checkinFoodRoots.value[findIndex]!.mealContent!.mealEvaluationList.push(mealEvaluation)
        } else {
            checkinFoodRoots.value[findIndex]!.mealContent!.mealEvaluationList = [mealEvaluation]
        }
    }

    await handleCheckIn(dietType)
    showSuccessToast('保存成功')
}

const calendarRef = useTemplateRef('calendarRef')

async function handleCheckIn(dietType: DietType, refresh = true) {
    try {
        const findFoodRoot = checkinFoodRoots.value.find(item => item.kind === dietType)

        const { results } = await useWrapFetch<BaseResponse<number>>('/checkInCustomerMeal/save', {
            method: 'POST',
            body: {
                ...findFoodRoot,
                status: 1,
                calorie: findFoodRoot?.mealContent.items.reduce((acc, item) => acc + item.calories, 0),
                mealContent: JSON.stringify(findFoodRoot?.mealContent),
                checkInDate: checkInDate.value,
            },
        })

        if (results) {
            findFoodRoot!.id = results
        }

        showSuccessToast('保存成功')
        if (refresh) {
            getFoodCheckinData()
            calendarRef.value?.refresh()
        }
    } catch (error) {
        console.log(error)
        showFailToast('操作失败')
    }
}

function handlePreviewImage(index: number, pictures: string[]) {
    showImagePreview({
        images: pictures,
        startPosition: index,
        closeable: true,
    })
}

const takePhotoRef = useTemplateRef('takePhotoRef')

async function getConsecutiveCheckInDays() {
    const { results } = await useWrapFetch<BaseResponse<number>>('/checkInCustomerMeal/consecutiveCheckInDays', {
        method: 'POST',
        body: {
            checkInDate: dayjs().format('YYYY-MM-DD'),
        },
    })
    return results
}

async function handleDataChange(checkinData: CheckInCustomerData) {
    const days = checkinData?.data?.days ?? []

    if (dietCheck.value === '1' && days.length > 0) {
        if (canShowEncourageToday('diet', userInfo.value?.phone)) {
            dietWeekStatus.value = computeDietWeekStatus(days)
            continuousCheckinDays.value = await getConsecutiveCheckInDays()
            showEncouragePopup.value = true
            markEncourageShownToday('diet', userInfo.value?.phone)
        }
        dietCheck.value = '0'
    }
}
</script>

<template>
    <div>
        <div class="h-10px"></div>
        <user-checkin-health-manage-calendar
            ref="calendarRef"
            v-model="checkInDate"
            checkin-type="mealCount"
            @data-change="handleDataChange"
        />

        <div class="px-14px pt-14px flex flex-col justify-between">
            <div class="bg-white rd-10px p-14px min-h-[calc(100vh-110px)] overflow-y-auto">
                <template v-if="isLoading">
                    <van-skeleton>
                        <template #template>
                            <div class="flex flex-1 mt-10px">
                                <div :style="{ flex: 1 }">
                                    <van-skeleton-paragraph class="h-42px! rd-10px" />
                                    <van-skeleton-paragraph class="h-42px! rd-10px" />
                                    <van-skeleton-paragraph class="h-42px! rd-10px" />
                                    <van-skeleton-paragraph class="h-42px! rd-10px" />
                                </div>
                            </div>
                        </template>
                    </van-skeleton>
                </template>

                <template v-else>
                    <div v-if="totalFoodCount > 0" class="flex flex-col gap-14px pb-70px">
                        <div v-for="(item, index) in filteredCheckinFoodRoots" :key="index">
                            <div class="flex items-center justify-between">
                                <div class="flex items-center gap-8px">
                                    <div class="i-custom:checkin-food-dot w-22px h-22px"></div>
                                    <div class="text-t-5 text-15px font-600">
                                        {{ tabs.find(tab => tab.name === item.kind)?.title }}
                                    </div>
                                    <div class="text-t-4 text-13px">
                                        {{ item.calorie }}千卡
                                    </div>
                                </div>
                            </div>
                            <div class="mt-14px">
                                <user-checkin-food-editable-list
                                    v-model="(checkinFoodRoots.find(root => root.kind === item.kind)!.mealContent.items as FoodItem[])"
                                    :diet-type="item.kind" @change="handleCheckIn"
                                />

                                <div class="flex flex-wrap gap-10px mt-8px">
                                    <van-image
                                        v-for="(picture, index) in item.mealContent.pictures" :key="picture"
                                        :src="picture" class="w-50px h-50px rd-10px overflow-hidden" fit="cover"
                                        @click="handlePreviewImage(index, item.mealContent.pictures)"
                                    />
                                </div>

                                <user-checkin-food-comment :food="item" />
                            </div>

                            <div>
                            </div>
                        </div>
                    </div>

                    <div v-else class="flex flex-col items-center justify-center relative top-100px">
                        <img
                            src="@/assets/images/checkin/diet-placeholder.svg" class="w-175px h-123px" alt=""
                            srcset=""
                        />

                        <div
                            class="bg-fill-1 w-241px h-50px text-primary-6 font-600 text-15px rd-100px flex items-center justify-center"
                            @click="takePhotoRef?.manualTriggerUpload()"
                        >
                            智能扫描餐食 · 点击拍照
                        </div>
                    </div>
                </template>

                <div class="fixed bottom-32px flex justify-center w-screen right-0">
                    <div
                        class="w-128px h-48px rd-40px flex justify-between items-center bg-white"
                        style="box-shadow: 0px 0px 10px 0px rgba(7, 51, 46, 0.28);"
                    >
                        <div
                            class="flex-1/2 flex items-center h-full justify-center active:bg-primary-1 rd-l-40px"
                            @click="navigateTo(`/user/checkin/food/lib?date=${checkInDate}`)"
                        >
                            <div class="i-custom:checkin-meals-lib-btn w-22px h-22px"></div>
                        </div>
                        <div
                            class="flex-1/2 flex items-center h-full justify-center active:bg-primary-1 rd-r-40px"
                            @click="takePhotoRef?.manualTriggerUpload()"
                        >
                            <div class="i-custom:checkin-meals-photo-btn w-22px h-22px"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <user-checkin-food-take-photo ref="takePhotoRef" @add="handlePictureFoodAdd" />

        <user-checkin-health-manage-encourage-popup
            v-model:show="showEncouragePopup"
            type="diet"
            :week-status="dietWeekStatus"
            :continuous-count="continuousCheckinDays"
        />
        <!--
        <base-fixed-bottom :hidden="totalFoodCount === 0">
            <user-checkin-food-take-photo
                ref="takePhotoRef"
                @add="handlePictureFoodAdd"
            >
                <van-button type="primary" class="!w-350px !h-50px" block plain round>
                    <div class="font-600 text-15px">AI拍照识别</div>
                    <template #icon>
                        <div class="i-custom:checkin-camera-3 w-20px h-20px text-#4E5969"></div>
                    </template>
                </van-button>
            </user-checkin-food-take-photo>
        </base-fixed-bottom> -->
    </div>
</template>

<style scoped>
:deep(.van-cell) {
    padding-right: 0 !important;
    padding-left: 0 !important;
}
</style>
