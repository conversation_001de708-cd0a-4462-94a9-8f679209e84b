<script setup lang="ts">
const dayjs = useDayjs()

const selectedDay = ref(dayjs().format('YYYY-MM-DD'))
const oldValue = ref(0)

const { data, status, refresh } = useAsyncData('water-init', async () => {
    const [waterTarget, waterData] = await Promise.all([
        useWrapFetch<BaseResponse<CustomerIndex>>('/checkInCustomerIndex/get', { method: 'post', body: {
            kind: '5',
        } }),

        useWrapFetch<BaseResponse<number>>('/checkInCustomerDrinkWater/get', {
            method: 'post',
            body: {
                kind: '5',
                checkInDate: selectedDay.value,
            },
        }),

    ])

    setTimeout(() => {
        oldValue.value = waterData.results
    }, 1000)

    return {
        waterTarget: waterTarget.results,
        waterData: waterData.results,
    }
})

watch(selectedDay, () => {
    refresh()
})

watch(status, () => {
    if (status.value === 'pending') {
        showLoadingToast({
            message: '加载中...',
            forbidClick: true,
        })
    } else {
        closeToast()
    }
})

const showAddActionSheet = ref(false)
const showWaterSheet = ref(false)

function handleTargetSetSuccess() {
    showAddActionSheet.value = false
    refresh()
}

const calendarRef = useTemplateRef('calendarRef')

function handleWaterSuccess() {
    showWaterSheet.value = false
    refresh()
    calendarRef.value?.refresh()
}

const waveTop = computed(() => {
    const target = Number(data.value?.waterTarget?.waterIndex) || 1700
    const water = Number(data.value?.waterData) || 0

    const percent = water / target

    const maxPercent = Math.min(percent, 1)

    const realPercent = 1 - maxPercent + 0.05

    return realPercent * 100
})

async function handleTakeWater(waterIntake: number) {
    try {
        await useWrapFetch('/checkInCustomerDrinkWater/save', { method: 'post', body: {
            waterIntake: String(waterIntake),
            checkInDate: selectedDay.value,
        } })
        refresh()
    } catch {
        showFailToast('保存失败')
    }
}

const { canCheckin } = useCheckin(selectedDay)
</script>

<template>
    <div>
        <user-checkin-calendar
            ref="calendarRef"
            v-model="selectedDay"
            checkin-type="waterIntake"
        />

        <div class="relative">
            <div class="absolute h-[calc(100vh-130px)] flex flex-col justify-between bottom-0 top-0 z-100 left-0 right-0 p-16px">
                <div>
                    <div class="flex justify-between items-center">
                        <div class="text-t-5 text-16px font-600">
                            饮水情况
                        </div>

                        <!-- <div class="flex items-center">
                            <div class="text-t-4 text-14px">
                                饮水记录
                            </div>
                            <div class="i-radix-icons:chevron-right text-t-4"></div>
                        </div> -->
                    </div>

                    <div class="flex items-center gap-4px" @click="showAddActionSheet = true">
                        <div class="text-t-4">
                            饮水目标 {{ data?.waterTarget?.waterIndex || 1700 }} ml
                        </div>

                        <div class="i-radix-icons:triangle-down text-t-2"></div>
                    </div>

                    <div class="flex flex-col items-center justify-center mt-24px">
                        <div class="battle-container">
                            <div class="wave-container">
                                <div class="bg-#fff/50 relative rd-8px w-8px h-94px z-5 left-20px top-30px -rotate-3">
                                </div>
                                <div class="wave" :style="`top: ${waveTop}%`"></div>
                            </div>
                        </div>

                        <div class="text-t-5 font-600 text-20px mt-8px flex gap-2px">
                            <v-countup :options="{ useGrouping: false }" :end-val="data?.waterData || 0" :start-val="oldValue" />
                            <div>ml</div>
                        </div>
                        <div class="text-t-3 text-12px">
                            今日饮水量
                        </div>
                    </div>

                    <div v-if="canCheckin" class="bg-white rd-10px p-16px h-137px mt-12px">
                        <div class="text-t-5 font-600 text-16px">
                            快捷记录
                        </div>

                        <div class="flex justify-between mt-12px">
                            <div class="i-custom:checkin-water-250 w-48px h-72px" @click="handleTakeWater(250)"></div>

                            <div class="i-custom:checkin-water-400 w-48px h-72px" @click="handleTakeWater(400)"></div>

                            <div class="i-custom:checkin-water-550 w-48px h-72px" @click="handleTakeWater(550)"></div>

                            <div class="i-custom:checkin-water-1000 w-48px h-72px" @click="handleTakeWater(1000)"></div>
                        </div>
                    </div>
                </div>

                <div v-if="canCheckin" class="flex justify-center" @click="showWaterSheet = true">
                    <van-button type="primary" round block class="!w-240px !h-50px">
                        记录喝水
                    </van-button>
                </div>
            </div>

            <div
                class="absolute top-0 bottom-0 left-0 right-0 h-277px"
                style="background: linear-gradient(180deg, #EAF6FF 0%, #F4F5F7 100%);"
            ></div>
        </div>
    </div>

    <user-checkin-water-target
        v-model="showAddActionSheet"
        :default-target-value="Number(data?.waterTarget?.waterIndex) || 1700"
        @success="handleTargetSetSuccess"
    />

    <user-checkin-water v-model="showWaterSheet" :check-in-date="selectedDay" @success="handleWaterSuccess" />
</template>

<style lang="scss" scoped>
.battle-container {
  width: 132px;
  height: 188px;
  background: url('@/assets/images/checkin/water/bottle.svg') no-repeat center center / 100% 100%;
}

.wave-container {
  width: 122px;
  height: 178px;
  position: relative;
  bottom: -1px;
  left: 4px;
  overflow: hidden;
  clip-path: polygon(3% 0%, 99% 0%, 92% 100%, 10% 100%);
}

.wave {
  position: absolute;
  width: 300%;
  height: 205%;
  left: -100%;
  background-color: #4AB0F0;
  border-radius: 45%;
  animation: rotate 6s linear infinite;
  transition: top 1s ease-in-out;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>
