<script setup lang="ts">
import { SPORTS_LIB_MAP } from '@/utils/constants'
import { calculateContinuousCheckinDays, canShowEncourageToday, computeSportWeekStatus, markEncourageShownToday } from '@/utils/common'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-#F2F4F7',
        },
    },
})

useHead({
    title: '运动打卡',
})

const dayjs = useDayjs()
const selectedDay = useRouteQuery('date', dayjs().format('YYYY-MM-DD'))
const { sportCheckStatus, resetSportCheckStatus } = useSportCheckStore()
const showEncouragePopup = ref(false)
const sportWeekStatus = ref<boolean[]>([])
const continuousCheckinDays = ref(0)
const { userInfo } = storeToRefs(useUserStore())

const { data, status, refresh } = useAPI<any>('/checkInCustomerSport/listNew', {
    method: 'POST',
    body: {
        checkInDate: selectedDay,
    },
})
const { data: archive, refresh: refreshArchive } = useAPI<Archives>('/user/preliminaryArchive')

watch(selectedDay, () => {
    refresh()
})

const sportList = computed(() => {
    return data.value?.results?.list || []
})

const calendarRef = useTemplateRef<any>('calendarRef')

const sportCalorie = computed(() => {
    try {
        return calendarRef.value?.checkinData?.data.days.find((item: any) => item.day === selectedDay.value).sportCalorie || 0
    } catch (error) {
        return 0
    }
})

const showSportSheet = ref(false)
const choosedSport = ref<any>({
    sportId: '0',
    sportName: '',
    sportTime: 0,
    sportType: '',
    id: 0,
    met: '',
    note: '',
    strengthGrade: '',
})

function handleChangeSport(item: any) {
    choosedSport.value = {
        ...item,
        sportId: Number(item.sportId),
    }
    showSportSheet.value = true
}

function handleGoRecommendSport() {
    if (!localStorage.getItem('isSetSportPreference')) {
        navigateTo(`/user/survey/sport`)
    } else {
        navigateTo(`/user/recommend/sport`)
    }
}

async function handleDataChange() {
    if (sportCheckStatus) {
        const results = await getCustomSportTrendData()
        if (results.length > 0) {
            if (canShowEncourageToday('sport', userInfo.value?.phone)) {
                sportWeekStatus.value = computeSportWeekStatus(results)
                continuousCheckinDays.value = calculateContinuousCheckinDays(results)
                showEncouragePopup.value = true
                markEncourageShownToday('sport', userInfo.value?.phone)
            }
            resetSportCheckStatus()
        }
    }
}

async function getCustomSportTrendData() {
    const startDate = '2024-08-19' // 项目开始日期
    const endDate = dayjs().format('YYYY-MM-DD')

    const { results } = await useWrapFetch<BaseResponse<any[]>>('/checkInCustomerSport/getSportTrendData', {
        method: 'post',
        body: {
            startDate,
            endDate,
        },
    })

    return Array.isArray(results) ? results : []
}
</script>

<template>
    <base-suspense :status="status">
        <div class="h-100vh flex flex-col justify-between gap-16px pt-16px px-16px">
            <user-checkin-calendar
                ref="calendarRef"
                v-model="selectedDay"
                checkin-type="sportCalorie"
                @data-change="handleDataChange"
            />

            <div class="bg-white rd-10px py-10px px-16px flex items-center justify-between">
                <div class="text-#1D2229 text-14px font-400">今日热量消耗：</div>
                <div class="flex items-center gap-2px text-15px font-600">
                    <div class="text-#00AC97">{{ sportCalorie }}</div>
                    <div class="text-#1D2229">Kcal</div>
                </div>
            </div>

            <div
                class="pt-12px pb-40px px-16px bg-white rd-tr-10px rd-tl-10px h-full flex flex-col gap-8px justify-between items-center"
            >
                <div class="flex flex-col flex-1 h-[calc(100vh-32px)] !max-h-[calc(100vh-320px)] gap-8px w-full overflow-y-auto">
                    <template
                        v-if="sportList.length > 0"
                    >
                        <div
                            v-for="item in sportList"
                            :key="item.sportId"
                            class="flex flex-col gap-4px items-center justify-between w-full"
                        >
                            <div
                                class="flex w-full gap-6px items-center"
                                @click="handleChangeSport(item)"
                            >
                                <div class="w-48px h-48px" :class="SPORTS_LIB_MAP[item.sportName] || 'i-custom:checkin-sport-general-sport'"></div>
                                <div class="flex flex-1 justify-between">
                                    <div class="flex flex-col">
                                        <div class="text-#1D2229 text-14px font-600">
                                            {{ item.sportName }}
                                        </div>
                                        <div class="text-#868F9C text-12px font-400">
                                            {{ item.sportTime }}min
                                        </div>
                                    </div>
                                    <div class="flex items-center text-#1D2229 text-14px font-600">
                                        {{ item.kcal }}kcal
                                    </div>
                                </div>
                            </div>
                            <van-text-ellipsis
                                v-if="item?.note"
                                class="text-#868F9C text-11px w-full"
                                rows="1"
                                :content="item?.note"
                                expand-text="展开"
                                collapse-text="收起"
                            />
                        </div>
                    </template>

                    <img
                        v-else
                        src="@/assets/images/checkin/sport-placeholder.png"
                        class="w-240px h-240px m-auto"
                    />
                </div>

                <div class="flex flex-col justify-between items-center gap-8px">
                    <van-button class="!w-239px !h-50px" round type="primary" plain @click="handleGoRecommendSport">
                        AI智能运动处方
                    </van-button>
                    <van-button class="!w-239px !h-50px" round type="primary" @click="navigateTo(`/user/checkin/sportCalorie/add?date=${selectedDay}`)">
                        记录运动
                    </van-button>
                </div>
            </div>

            <user-checkin-sport-calorie
                v-model:show="showSportSheet"
                v-model:time="choosedSport.sportTime"
                :sport-type="choosedSport.sportType"
                :sport-name="choosedSport.sportName"
                :sport-id="Number(choosedSport.sportId)"
                :burn-calories="choosedSport.kcal"
                :met="choosedSport.met"
                :note="choosedSport.note"
                :strength-grade="choosedSport.strengthGrade"
                :check-in-customer-sport-id="choosedSport.id"
                :archive-weight="Number(archive?.results?.archiveWeight)"
                @success="() => {
                    refresh()
                    showSportSheet = false
                }"
            />

            <user-checkin-health-manage-encourage-popup
                v-model:show="showEncouragePopup"
                type="sport"
                :week-status="sportWeekStatus"
                :continuous-count="continuousCheckinDays"
            />
        </div>
    </base-suspense>
</template>

<style lang="scss" scoped>
:deep(.van-text-ellipsis__action) {
    color: #00AC97;
}
</style>
