<script setup lang="ts">
const dayjs = useDayjs()

const selectedDay = ref<{
    DAY: string
    WEEK: string
    MONTH: string
}>({
    DAY: dayjs().format('YYYY-MM-DD'),
    WEEK: dayjs().format('YYYY-MM-DD'),
    MONTH: dayjs().format('YYYY-MM-DD'),
})

const active = ref<'DAY' | 'WEEK' | 'MONTH'>('DAY')

const currentDate = computed(() => {
    return selectedDay.value[active.value]
})

const { data: exerciseTarget, refresh: refreshExerciseTarget } = useAPI<CustomerIndex>('/checkInCustomerIndex/get', {
    method: 'post',
    body: {
        kind: '4',
    },
})

const { data: exerciseData, status: exerciseDataStatus, refresh: refreshExerciseData } = useAPI<ExerciseData[]>('/checkInCustomerSport/get/foot', {
    method: 'post',
    body: {
        type: active,
        checkInDate: currentDate,
    },
})

useStatusLoading(exerciseDataStatus)

const showTargetActionSheet = ref(false)

const showAddActionSheet = ref(false)

function handleTargetSuccess() {
    showTargetActionSheet.value = false

    refreshExerciseTarget()
}

function handleAddSuccess() {
    showAddActionSheet.value = false

    refreshExerciseData()
}
</script>

<template>
    <div class="p-16px flex flex-col h-full justify-between">
        <div>
            <div class="rd-10px p-16px bg-white">
                <van-tabs v-model:active="active" type="card">
                    <van-tab title="日" name="DAY">
                        <user-checkin-date-switch v-model:date="selectedDay.DAY" class="my-16px" switch-type="day" />

                        <user-checkin-exercise-day
                            :target="exerciseTarget?.results?.footIndex || '6000'"
                            :exercise-data="exerciseData?.results || []"
                        />
                    </van-tab>
                    <van-tab title="周" name="WEEK">
                        <user-checkin-date-switch v-model:date="selectedDay.WEEK" class="my-16px" switch-type="week" />

                        <user-checkin-exercise-week :exercise-data="exerciseData?.results || []" />
                    </van-tab>
                    <van-tab title="月" name="MONTH">
                        <user-checkin-date-switch v-model:date="selectedDay.MONTH" class="my-16px" switch-type="month" />

                        <user-checkin-exercise-month
                            :exercise-data="exerciseData?.results || []"
                            :date="selectedDay.MONTH"
                        />
                    </van-tab>
                </van-tabs>
            </div>

            <van-cell title="每日目标" rd-10px is-link mt-16px inset :value="exerciseTarget?.results?.footIndex || 6000" @click="showTargetActionSheet = true" />
        </div>

        <div flex justify-center>
            <van-button round type="primary" class="w-223px! h-50px!" @click="showAddActionSheet = true">记步数</van-button>
        </div>

        <user-checkin-exercise-target
            v-model="showTargetActionSheet"
            :default-target-value="Number(exerciseTarget?.results?.footIndex) || 6000"
            @success="handleTargetSuccess"
        />

        <user-checkin-exercise v-model="showAddActionSheet" :sport="exerciseData?.results?.[0]?.steps" @success="handleAddSuccess" />
    </div>
</template>

<style scoped lang="scss">
.card {
    background: linear-gradient(131.89deg, rgba(194, 201, 214, 0.1) 8.47%, rgba(194, 201, 214, 0.05) 88.34%);
    @apply relative overflow-hidden;
}
.card-bg {
    background: linear-gradient(30deg, rgba(255, 255, 255, 0) 59.41%, rgba(6, 200, 176, 0.2) 98.11%);
    @apply absolute top-0 left-0 w-full h-full;
}

.card-icon {
    @apply i-custom-checkin-exercise inline-block absolute w-86px h-86px -top-13px -right-13px opacity-10;
}
</style>
