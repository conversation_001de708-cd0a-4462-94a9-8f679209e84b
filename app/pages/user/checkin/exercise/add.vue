<script setup lang="ts">
const value = ref('')

function handleSearch() {

}

const showAddActionSheet = ref(false)

function handleSave() {
    showAddActionSheet.value = false
    value.value = ''
}
</script>

<template>
    <div h-full bg-white space-y-16px p-16px class="add-exercise-container">
        <div flex items-center space-x-8px>
            <van-search v-model="value" placeholder="搜索" background="transparent" class="px-0! flex-1" @search="handleSearch" />
            <div text-t-4 @click="handleSearch">搜索</div>
        </div>

        <user-checkin-exercise-list-item
            v-for="i in 6" :key="i"
            img-src="~/assets/images/checkin/exercise/running.png"
            label="跑步"
        >
            <p text-t-3 text-12px>
                <span text-primary-6>94.5</span>
                Kcal / 30 分钟
            </p>

            <template #right>
                <span class="i-custom-plus inline-block w-16px h-16px text-primary-6" @click="showAddActionSheet = true"></span>
            </template>
        </user-checkin-exercise-list-item>

        <van-action-sheet :show="showAddActionSheet" @close="showAddActionSheet = false">
            <div class="p-16px space-y-16px">
                <div class="flex justify-between">
                    <div text-16px text-t-5 @click="showAddActionSheet = false">取消</div>
                    <div font-600 text-16px text-t-5>设置时长</div>
                    <div text-16px text-primary-6 @click="handleSave">保存</div>
                </div>

                <!-- <div flex justify-between items-center text-16px text-t-4>
                    <div whitespace-nowrap>运动时长</div>
                    <van-field v-model="value" autofocus input-align="right" />
                    <div whitespace-nowrap>分钟</div>
                </div> -->

                <div h-16px></div>
            </div>
        </van-action-sheet>
    </div>
</template>

<style lang="scss" scoped>
.add-exercise-container {
    background: linear-gradient(180deg, #EBFFFE 0%, #FFFFFF 44%, #FFFFFF 100%);
}

:deep(.van-search__content) {
    background: transparent;
    @apply border-t-3 border rounded-10px;
}

:deep(.van-badge__wrapper.van-icon.van-icon-search) {
    @apply text-t-4;
}

:deep(input.van-field__control::placeholder) {
    @apply text-t-3;
}

:deep(.van-field__body) {
    @apply pr-8px;
}
</style>
