<script setup lang="ts">
const step = ref('')
const kcal = ref('')
</script>

<template>
    <div bg-white h-full flex flex-col justify-center items-center space-y-56px>
        <div text-16px text-t-5 flex flex-col items-center space-y-8px class="step-input">
            <span i-custom-exercise-step inline-block w-24px h-24px></span>
            <span text-16px text-t-4>设置步数</span>
            <van-field v-model="step" :border="false" class="pb-0!" autofocus placeholder="请输入步数" input-align="center" type="number" />
            <div h-1px bg-fill-3 w-200px></div>
        </div>
        <div text-16px text-t-5 flex flex-col items-center space-y-8px class="kcal-input">
            <span i-custom-exercise-kcal inline-block w-24px h-24px></span>
            <span text-16px text-t-4>能量消耗</span>
            <van-field v-model="kcal" :border="false" class="pb-0!" autofocus placeholder="请输入能量消耗" input-align="center" type="number" />
            <div h-1px bg-fill-3 w-200px></div>
        </div>
    </div>
</template>

<style scoped lang="scss">
:deep(.step-input input.van-field__control) {
    @apply text-primary-6;
}

:deep(.kcal-input input.van-field__control) {
    @apply text-warning-6;
}
</style>
