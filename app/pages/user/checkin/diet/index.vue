<script setup lang="ts">
import { dietMeta } from '~/utils/user/checkin/diet'

import type { UploaderBeforeRead, UploaderFileListItem } from 'vant/es/uploader/types'

const showUploadActionSheet = ref<typeof dietMeta[number]['key']>()

const imgList = ref<UploaderFileListItem[]>([])

function handleBeforeRead(file: File) {
    if (file.size > 1024 * 1024 * 10) {
        showFailToast('文件大小不能超过10MB')
        return false
    }
    return true
}
</script>

<template>
    <div class="min-h-full bg-white p-16px">
        <div rounded-10px class="card" p-16px>
            <div class="card-bg"></div>
            <div class="card-icon"></div>
            <div text-t-4 text-12px>今日饮食摄入</div>
            <div flex items-end space-x-4px>
                <span text-warning-6 font-600 text-24px>1326</span>
                <span text-t-3 text-12px leading-28px>kcal</span>
            </div>

            <div pt-10px flex justify-between>
                <div v-for="item in dietMeta" :key="item.key" flex flex-col items-center justify-center rounded-10px bg-white>
                    <div bg-primary-1 p-8px rounded-10px>
                        <div :class="item.icon" class="w-24px h-24px"></div>
                    </div>
                    <div text-t-4 text-12px>{{ item.label }}</div>
                </div>
            </div>
        </div>

        <div
            v-for="item in dietMeta" :key="item.key"
            p-16px space-y-8px
        >
            <div flex items-center space-x-8px>
                <div w-24px h-24px rounded-10px bg-primary-1 flex items-center justify-center>
                    <span :class="[item.icon]" class="w-12px h-12px"></span>
                </div>
                <div text-t-5 text-14px>{{ item.label }}</div>
                <div text-t-3 text-12px>{{ item.time }}</div>
                <div flex-1></div>
                <div text-primary-6 text-13px @click="showUploadActionSheet = item.key">上传餐图</div>
            </div>
            <div>
                <div bg-fill-1 p-8px rounded-10px>
                    <p text-t-5 text-13px font-600>{{ item.label }}建议:</p>
                    <p text-t-3 text-12px>{{ item.suggestion }}</p>
                </div>
            </div>
        </div>

        <van-action-sheet :show="!!showUploadActionSheet" @close="showUploadActionSheet = undefined">
            <div class="p-16px space-y-16px">
                <div class="flex justify-between">
                    <div text-16px text-t-5 @click="showUploadActionSheet = undefined">取消</div>
                    <div font-600 text-16px text-t-5>{{ dietMeta.find(item => item.key === showUploadActionSheet)?.label }}</div>
                    <div text-16px text-primary-6>保存</div>
                </div>

                <div h-146px bg-fill-1 rounded-10px flex flex-col items-center justify-center space-y-4px>
                    <van-uploader v-model="imgList" mt-10px :max-count="1" :before-read="(handleBeforeRead as UploaderBeforeRead)" />
                    <div text-12px text-t-3>拍照后代表此餐已吃，热量会有所差异，不作为精确参考</div>
                </div>

                <div h-16px></div>
            </div>
        </van-action-sheet>
    </div>
</template>

<style scoped lang="scss">
.card {
    background: linear-gradient(131.89deg, rgba(194, 201, 214, 0.1) 8.47%, rgba(194, 201, 214, 0.05) 88.34%);
    @apply relative overflow-hidden;
}
.card-bg {
    background: linear-gradient(27deg, rgba(255, 255, 255, 0) 59.41%, rgba(255, 212, 0, 0.2) 98.11%);
    @apply absolute top-0 left-0 w-full h-78px;
}

.card-icon {
    @apply -rotate-33deg i-custom-checkin-diet inline-block absolute w-86px h-86px -top-13px -right-6px opacity-10;
}

:deep(.van-popup) {
    background: linear-gradient(180deg, #EBFFFE 0%, #FFFFFF 44%, #FFFFFF 100%);
}
</style>
