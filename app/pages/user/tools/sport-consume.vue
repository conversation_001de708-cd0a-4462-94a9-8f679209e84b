<script setup lang="ts">
import SlideRuler from '@/utils/slide-rule'
import EditDialog from '@/components/survey/edit-dialog.vue'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

useHead({
    title: '运动消耗',
})

const weightValue = ref<number>(0)
const minutes = ref<number>(60)
const sportItemName = ref<string>('')
const sportItem = ref<SubSport | null>(null)
const showSportItemPicker = ref(false)
const sportItemList = ref<SubSport[]>([])
const sportCalorie = ref<number>(0)
const isWeightDialogShow = ref(false)
const isMinutesDialogShow = ref(false)

const weightRulerRef = useTemplateRef('weightRulerRef')
const minutesRulerRef = useTemplateRef('minutesRulerRef')

let weightSlider: any
let minutesSlider: any

const lastVibrationTime = ref(0)
const VIBRATION_THRESHOLD = 100

async function renderWeightRule() {
    await nextTick()
    weightSlider?.destroy()
    weightSlider = new SlideRuler({
        canvasHeight: 60,
        fontSize: 13,
        heightDecimal: 28,
        heightDigit: 18,
        fontMarginTop: 45,
        el: weightRulerRef.value,
        currentValue: weightValue.value,
        maxValue: 200,
        minValue: 30,
        divide: 5,
        precision: 0.1,
        handleValue: (v: number) => {
            weightValue.value = v || 0

            if (navigator?.vibrate) {
                const now = Date.now()
                if (now - lastVibrationTime.value > VIBRATION_THRESHOLD) {
                    navigator?.vibrate([10, 10])
                    lastVibrationTime.value = now
                }
            }
        },
    })
}

async function renderMinutesRule() {
    await nextTick()
    minutesSlider?.destroy()
    minutesSlider = new SlideRuler({
        canvasHeight: 60,
        fontSize: 13,
        heightDecimal: 28,
        heightDigit: 18,
        fontMarginTop: 45,
        el: minutesRulerRef.value,
        currentValue: minutes.value,
        maxValue: 360,
        minValue: 1,
        divide: 5,
        precision: 1,
        handleValue: (v: number) => {
            minutes.value = v || 0
        },
    })
}

async function getArchiveData() {
    const { closeLoading } = useLoading()
    try {
        const { results: archiveResults } = await useWrapFetch<BaseResponse<Archives>>('/user/preliminaryArchive')
        weightValue.value = Number((archiveResults as any)?.archiveWeight)
    } catch (error) {
        console.error(error)
    } finally {
        closeLoading()
    }
}

async function getSportItemList() {
    const { results } = await useWrapFetch<BaseResponse<Sport[]>>('/checkInCustomerSport/getDictNew', {
        method: 'POST',
    })
    sportItemList.value = results.flatMap(item => item.subList || [])
}

async function initData() {
    await getArchiveData()
    renderWeightRule()
    renderMinutesRule()
    getSportItemList()
}

function handleChooseSportItem(item: SubSport) {
    sportItem.value = item
    sportItemName.value = item.name
    showSportItemPicker.value = false
}

function handleCalculate() {
    if (!sportItemName.value) {
        showToast('请选择运动类目')
        return
    }
    if (!minutes.value) {
        showToast('请输入运动时长')
        return
    }
    // 运动卡路里消耗=MET*体重（kg）*时间（h）
    sportCalorie.value = Math.ceil(Number(sportItem.value?.met) * weightValue.value * minutes.value / 60)
}

onMounted(() => {
    initData()
})
</script>

<template>
    <div class="flex flex-col gap-16px p-16px h-100vh">
        <div class="flex justify-between items-center">
            <div class="text-#1D2129 text-16px font-600">类目</div>
            <van-field
                v-model="sportItemName"
                class="!w-210px"
                is-link
                readonly
                clearable
                placeholder="请选择运动类目"
                @click="showSportItemPicker = true"
            />
        </div>
        <div class="flex w-full flex-col gap-8px items-center justify-center overflow-hidden">
            <div class="flex items-center relative w-full">
                <div class="text-#1D2129 text-16px absolute left-0 top-1/2 -translate-y-1/2 font-600">时长</div>
                <div class="flex-1 text-center flex items-center justify-center">
                    <span text="t-5 18px" mr-4px font-600>
                        {{ minutes ?? 0 }}
                    </span>
                    <span text="t-5 10px" relative top-3px>
                        min
                    </span>
                    <div class="i-custom:pen w-12px h-12px ml-4px relative bottom-3px" @click="isMinutesDialogShow = true"></div>
                </div>
            </div>
            <div ref="minutesRulerRef"></div>
        </div>
        <div class="flex w-full flex-col gap-8px items-center justify-center overflow-hidden">
            <div class="flex items-center relative w-full">
                <div class="text-#1D2129 text-16px absolute left-0 top-1/2 -translate-y-1/2 font-600">体重</div>
                <div class="flex-1 text-center flex items-center justify-center">
                    <span text="t-5 18px" mr-4px font-600>
                        {{ weightValue }}
                    </span>
                    <span text="t-5 10px" relative top-3px>
                        kg
                    </span>
                    <div class="i-custom:pen w-12px h-12px ml-4px relative bottom-3px" @click="isWeightDialogShow = true"></div>
                </div>
            </div>
            <div ref="weightRulerRef"></div>
        </div>

        <div class="flex justify-center">
            <van-button
                type="primary" round class="w-241px h-50px"
                @click="handleCalculate"
            >
                开始计算
            </van-button>
        </div>

        <div
            class="flex flex-col flex-1 mt-24px p-16px gap-8px rd-lt-10px rd-rt-10px bg-gradient-to-b from-#B9FFF7 from-0% to-white to-21.89%"
        >
            <div class="flex justify-between items-center">
                <div class="text-#1D2229 text-15px font-600">运动消耗</div>
                <div class="flex items-center gap-4px text-#00AC97 text-24px font-800 font-ddinpro">
                    {{ sportCalorie }}
                    <div class="text-#1D2229 text-15px font-600 relative top-2px">Kcal</div>
                </div>
            </div>

            <div class="flex flex-col gap-16px text-#1D2229 text-14px">
                减肥就是制造热量缺口，当消耗大于摄入时，身体就得拆脂肪来补供能。而且运动还能让你肌肉变结实，肌肉多了，代谢就会提升。再加上运动能让身体更会“烧脂肪”（优化脂肪代谢，抑制脂肪囤积），这三件事绑在一起，只要每周坚持运动个150到300分钟，时间长了肯定能看到效果。
                <br />
                <div>
                    <span class="font-600 text-14px text-#1D2229">换算参考：</span>消耗1kg脂肪约需7700kcal热量缺口，运动消耗可结合饮食调整实现减重目标。
                </div>
            </div>
        </div>

        <van-popup
            v-model:show="showSportItemPicker" destroy-on-close round position="bottom" closeable
        >
            <div class="p-16px flex flex-col gap-16px">
                <div class="text-#1D2129 text-18px font-600 text-center">类目</div>
                <div class="flex flex-col h-70vh overflow-y-auto">
                    <div
                        v-for="item in sportItemList"
                        :key="item.id"
                        class="rd-10px flex items-center h-64px py-8px px-16px"
                        :class="{ 'bg-#F2F4F7': sportItem?.id === item.id }"
                        @click="handleChooseSportItem(item)"
                    >
                        <div class="flex flex-1 flex-col">
                            <div class="text-#1D2129 text-14px font-600">{{ item.name }}</div>
                            <div class="text-#868F9C text-12px">{{ item.note }}</div>
                        </div>
                        <div v-if="sportItem?.id === item.id" class="i-custom:tools-checked w-16px h-16px"></div>
                    </div>
                </div>
            </div>
        </van-popup>

        <edit-dialog
            v-model:show="isWeightDialogShow" title="设置体重" :value="weightValue" unit="千克"
            @confirm="(value) => {
                weightValue = value
                renderWeightRule()
            }"
        />
        <edit-dialog
            v-model:show="isMinutesDialogShow" title="设置时长" :value="minutes ?? 0" unit="分钟"
            @confirm="(value) => {
                minutes = value
                renderMinutesRule()
            }"
        />
    </div>
</template>

<style  lang="scss" scoped>
:deep(.van-cell) {
    padding: 0;

    input {
        text-align: right;
    }
}
</style>
