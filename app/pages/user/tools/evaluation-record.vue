<script setup lang="ts">
import dayjs from 'dayjs'

useHead({
    title: '测评记录',
})

const route = useRoute()
const type = route.query.type as string

const evaluationResultMap = {
    低风险: '#00AC97',
    中风险: '#FFAE4A',
    高风险: '#F53F3F',
}
const evaluationRecords = ref<any[]>([])

const evaluationTypeTitle = computed(() => {
    switch (type) {
        case 'nfs':
            return '肝纤维化评估'
        case 'zju':
            return '脂肪肝评估'
        default:
            return type || '未知评估'
    }
})

const { results } = await useWrapFetch<BaseResponse<any>>('/api/assessment-instrument/list', {
    method: 'get',
    params: {
        type,
    },
})

function handleClickRecord(record: any) {
    navigateTo({
        path: '/user/tools/evaluation-result',
        query: { type, evaluationResult: record.evaluationResult, [type === 'nfs' ? 'nfsScore' : 'zjuScore']: record.evaluationValue },
    })
}

onMounted(() => {
    evaluationRecords.value = results
})
</script>

<template>
    <div class="min-h-100vh bg-fill-1 p-16px">
        <div class="space-y-16px">
            <div
                v-for="record in evaluationRecords"
                :key="record.id"
                class="bg-white rd-10px p-16px w-full h-75px flex flex-col gap-8px"
                @click="handleClickRecord(record)"
            >
                <div class="flex justify-between items-center">
                    <div class="text-16px font-600 text-t-5">
                        {{ evaluationTypeTitle }}
                    </div>
                    <div
                        class="text-14px font-400"
                        :style="{ color: evaluationResultMap[record.evaluationResult as keyof typeof evaluationResultMap] }"
                    >
                        {{ record.evaluationResult }}
                    </div>
                </div>

                <div class="text-12px font-400 text-t-4">
                    {{ dayjs(record.createTime).format('YYYY-MM-DD') }}
                </div>
            </div>
        </div>

        <van-empty v-if="evaluationRecords.length === 0" description="暂无测评记录" />
    </div>
</template>
