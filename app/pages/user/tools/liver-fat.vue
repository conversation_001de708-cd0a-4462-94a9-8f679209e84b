<script setup lang="ts">
import SlideRuler from '@/utils/slide-rule'
import EditDialog from '@/components/survey/edit-dialog.vue'
// import CustomUploader from '@/components/user/custom-uploader.vue'
// import { useImageUploader } from '@/composables/useCustomUploader'
// import { parseLabResults } from '@/utils/liver'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

useHead({
    title: '脂肪肝评估',
})

const age = ref<number>(0)
const heightValue = ref<number>(0)
const weightValue = ref<number>(0)
const altAst = ref<number>()
const fastingBloodSugar = ref<number>()
const triglyceride = ref<number>()
// const MAX_PICTURES = 1
const isHeightDialogShow = ref(false)
const isWeightDialogShow = ref(false)

// const {
//     pictures: liverPictures,
//     isImageLoading,
//     handleBeforeRead,
//     handlePreview,
//     handleDelete,
//     setImageLoading,
// } = useImageUploader({
//     maxCount: MAX_PICTURES,
//     onUploadComplete: async (fileIds) => {
//         if (fileIds.length > 0) {
//             await getOcrResult(fileIds)
//         }
//     },
// })

const isCalculationDisabled = computed(() => {
    return !(
        age.value > 0
        && heightValue.value
        && weightValue.value
        && altAst.value
        && fastingBloodSugar.value
        && triglyceride.value
    )
})

const heightRulerRef = useTemplateRef('heightRulerRef')
const weightRulerRef = useTemplateRef('weightRulerRef')

let heightSlider: any
let weightSlider: any

const lastVibrationTime = ref(0)
const VIBRATION_THRESHOLD = 100

async function renderHeightRule() {
    await nextTick()
    heightSlider?.destroy()
    heightSlider = new SlideRuler({
        canvasHeight: 60,
        fontSize: 13,
        heightDecimal: 28,
        heightDigit: 18,
        fontMarginTop: 45,
        el: heightRulerRef.value,
        currentValue: heightValue.value,
        maxValue: 300,
        minValue: 60,
        divide: 5,
        precision: 1,
        handleValue: (v: number) => {
            heightValue.value = v || 0

            if (navigator?.vibrate) {
                const now = Date.now()
                if (now - lastVibrationTime.value > VIBRATION_THRESHOLD) {
                    navigator?.vibrate([10, 10])
                    lastVibrationTime.value = now
                }
            }
        },
    })
}

async function renderWeightRule() {
    await nextTick()
    weightSlider?.destroy()
    weightSlider = new SlideRuler({
        canvasHeight: 60,
        fontSize: 13,
        heightDecimal: 28,
        heightDigit: 18,
        fontMarginTop: 45,
        el: weightRulerRef.value,
        currentValue: weightValue.value,
        maxValue: 200,
        minValue: 30,
        divide: 5,
        precision: 0.1,
        handleValue: (v: number) => {
            weightValue.value = v || 0

            if (navigator?.vibrate) {
                const now = Date.now()
                if (now - lastVibrationTime.value > VIBRATION_THRESHOLD) {
                    navigator?.vibrate([10, 10])
                    lastVibrationTime.value = now
                }
            }
        },
    })
}

// async function getOcrResult(fileIds: string[]) {
//     if (!fileIds || fileIds.length === 0) {
//         showToast('未获取到报告文件信息')
//         return
//     }

//     try {
//         setImageLoading(true)
//         const { results } = await useWrapFetch<BaseResponse<any>>('/api/qwen/getOcrResult', {
//             method: 'post',
//             body: {
//                 fileIds,
//             },
//         })

//         if (results?.items && Array.isArray(results.items)) {
//             const parsedResults = parseLabResults(results.items)

//             if (parsedResults.altAst) {
//                 altAst.value = parsedResults.altAst
//             } else {
//                 showToast('未解析到ALT/AST结果')
//             }
//             if (parsedResults.fastingBloodSugar) {
//                 fastingBloodSugar.value = parsedResults.fastingBloodSugar
//             } else {
//                 showToast('未解析到空腹血糖结果')
//             }
//             if (parsedResults.triglyceride) {
//                 triglyceride.value = parsedResults.triglyceride
//             } else {
//                 showToast('未解析到甘油三脂结果')
//             }
//         }
//     } catch (error: any) {
//         console.error('报告生成失败：', error)
//         showToast('报告解析失败')
//     } finally {
//         setImageLoading(false)
//     }
// }

async function getArchiveData() {
    const { closeLoading } = useLoading()
    try {
        const { results: archiveResults } = await useWrapFetch<BaseResponse<Archives>>('/user/preliminaryArchive')
        age.value = Number((archiveResults as any)?.age)
        heightValue.value = Number((archiveResults as any)?.archiveHeight)
        weightValue.value = Number((archiveResults as any)?.archiveWeight)
    } catch (error) {
        console.error(error)
    } finally {
        closeLoading()
    }
}

async function initData() {
    await getArchiveData()
    renderHeightRule()
    renderWeightRule()
}

function handleEvaluationRecord(type: string) {
    navigateTo({
        path: '/user/tools/evaluation-record',
        query: {
            type,
        },
    })
}

async function handleStartCalculation() {
    try {
        // 将身高从 cm 转换为米
        const heightInMeters = heightValue.value / 100

        // 计算 BMI = 体重(kg) / 身高²(m²)
        const bmi = Number((weightValue.value / (heightInMeters ** 2)).toFixed(2))

        // 获取 ALT/AST 比值（页面输入的是 ALT/AST）
        const altAstRatio = Number(altAst.value)

        // 获取空腹血糖（单位：mmol/L）
        const fpg = Number(fastingBloodSugar.value)

        // 获取甘油三脂（单位：mmol/L）
        const tg = Number(triglyceride.value)

        // ZJU index 计算公式：
        // ZJU index = BMI (kg/m²) + FPG空腹血糖 (mmol/L) + TG甘油三脂 (mmol/L) + 3 × ALT/AST 比值
        const zjuScore = bmi + fpg + tg + (3 * altAstRatio)

        // 保留三位小数
        const zjuResult = Number(zjuScore.toFixed(3))

        // 根据ZJU评分结果确定风险等级
        let evaluationResult = ''
        if (zjuResult < 32) {
            evaluationResult = '低风险'
        } else if (zjuResult >= 32 && zjuResult <= 38) {
            evaluationResult = '中风险'
        } else {
            evaluationResult = '高风险'
        }

        const { results } = await useWrapFetch<BaseResponse<any>>('/api/assessment-instrument', {
            method: 'post',
            body: {
                type: 'zju',
                evaluationValue: zjuResult.toString(),
                evaluationResult,
            },
        })

        if (results) {
            navigateTo({
                path: '/user/tools/evaluation-result',
                query: {
                    type: 'zju',
                    zjuScore: zjuResult.toString(),
                    evaluationResult,
                },
            })
        }
    } catch (error) {
        console.error('计算失败', error)
    }
}

onMounted(() => {
    initData()
})
</script>

<template>
    <div class="flex flex-col justify-between p-16px h-100vh overflow-y-auto">
        <div class="flex flex-col gap-24px">
            <div class="flex justify-between items-center">
                <div class="text-#1D2129 text-16px font-600">年龄</div>
                <div class="flex items-center w-90px">
                    <van-field v-model="age" type="digit" placeholder="请输入年龄" clearable />
                </div>
            </div>

            <div class="flex w-full flex-col gap-8px items-center justify-center overflow-hidden">
                <div class="flex items-center relative w-full">
                    <div class="text-#1D2129 text-16px absolute left-0 top-1/2 -translate-y-1/2 font-600">身高</div>
                    <div class="flex-1 text-center flex items-center justify-center">
                        <span text="t-5 18px" mr-4px font-600>
                            {{ heightValue }}
                        </span>
                        <span text="t-5 10px" relative top-3px>
                            cm
                        </span>
                        <div class="i-custom:pen w-12px h-12px ml-4px relative bottom-3px" @click="isHeightDialogShow = true"></div>
                    </div>
                </div>
                <div ref="heightRulerRef"></div>
            </div>

            <div class="flex w-full flex-col gap-8px items-center justify-center overflow-hidden">
                <div class="flex items-center relative w-full">
                    <div class="text-#1D2129 text-16px absolute left-0 top-1/2 -translate-y-1/2 font-600">体重</div>
                    <div class="flex-1 text-center flex items-center justify-center">
                        <span text="t-5 18px" mr-4px font-600>
                            {{ weightValue }}
                        </span>
                        <span text="t-5 10px" relative top-3px>
                            kg
                        </span>
                        <div class="i-custom:pen w-12px h-12px ml-4px relative bottom-3px" @click="isWeightDialogShow = true"></div>
                    </div>
                </div>
                <div ref="weightRulerRef"></div>
            </div>

            <div class="flex justify-between items-center">
                <div class="text-#1D2129 text-16px font-600">ALT/AST</div>
                <div class="flex items-center w-120px">
                    <van-field v-model="altAst" type="number" placeholder="请输入ALT/AST" clearable />
                </div>
            </div>

            <div class="flex justify-between items-center">
                <div class="text-#1D2129 text-16px font-600">空腹血糖</div>
                <div class="flex items-center w-120px">
                    <van-field v-model="fastingBloodSugar" type="number" placeholder="请输入空腹血糖" clearable />
                </div>
            </div>

            <div class="flex justify-between items-center">
                <div class="text-#1D2129 text-16px font-600">甘油三脂</div>
                <div class="flex items-center w-120px">
                    <van-field v-model="triglyceride" type="number" placeholder="请输入甘油三脂" clearable />
                </div>
            </div>
        </div>

        <!-- <custom-uploader
            v-model="liverPictures"
            :max-count="MAX_PICTURES"
            upload-button-height="108px"
            :show-description="true"
            description="上述指标，您可以直接拍照上传"
            :is-image-loading="isImageLoading"
            @preview="handlePreview"
            @delete="handleDelete"
            @before-read="handleBeforeRead"
        /> -->

        <div class="flex justify-center gap-16px mb-36px">
            <van-button
                type="primary"
                round
                class="flex-1 !bg-#E4FAF9 h-50px !text-#00AC97 !border-#E4FAF9"
                @click="handleEvaluationRecord('zju')"
            >
                测评记录
            </van-button>

            <van-button
                type="primary"
                round
                class="flex-1 h-50px"
                :disabled="isCalculationDisabled"
                @click="handleStartCalculation"
            >
                开始计算
            </van-button>
        </div>

        <edit-dialog
            v-model:show="isHeightDialogShow" title="设置身高" :value="heightValue" unit="厘米"
            @confirm="(value) => {
                heightValue = value
                renderHeightRule()
            }"
        />

        <edit-dialog
            v-model:show="isWeightDialogShow" title="设置体重" :value="weightValue" unit="千克"
            @confirm="(value) => {
                weightValue = value
                renderWeightRule()
            }"
        />
    </div>
</template>

<style scoped>
:deep(.van-cell) {
    padding: 0;

    input {
        text-align: right;
    }
}

:deep(.my-uploader .van-uploader__input-wrapper),
:deep(.my-uploader) {
    width: 100% !important;
    height: 108px !important;
}

:deep(.van-uploader) {
    --van-uploader-border-radius: 10px;
    --van-uploader-delete-icon-size: 24px;
    --van-uploader-delete-background: #11111126;
}

:deep(.van-uploader__preview-image) {
    border: 1px solid #F2F4F7
}

:deep(.van-uploader__preview-delete--shadow) {
    border-top-right-radius: 10px;
}
</style>
