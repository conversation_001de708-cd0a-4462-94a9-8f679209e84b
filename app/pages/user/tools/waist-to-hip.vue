<script setup lang="ts">
import SlideRuler from '@/utils/slide-rule'
import EditDialog from '@/components/survey/edit-dialog.vue'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

useHead({
    title: '腰臀比评估',
})

const gender = ref<'male' | 'female'>('male')

const waistRef = useTemplateRef('waistRef')
const hipRef = useTemplateRef('hipRef')

const waist = ref(0)
const hip = ref(80)
const ratio = ref<number>(0)
const isOverweight = ref<boolean>(false)

const isFirstCalculate = ref(true)
const isShowResult = ref(false)

function calculateRatio() {
    if (!hip.value) {
        ratio.value = 0
        return
    }
    ratio.value = waist.value / hip.value
}

function calculateIsOverweight() {
    isOverweight.value = (ratio.value > 0.9 && gender.value === 'male') || (ratio.value > 0.85 && gender.value === 'female')
}

const commonProps = {
    canvasHeight: 60,
    fontSize: 13,
    precision: 0.1,
    divide: 5,
    heightDecimal: 28,
    lineWidth: 2,
    heightDigit: 18,
    fontMarginTop: 45,
}
let waistSlider: any
let hipSlider: any

function renderWaistRule() {
    waistSlider?.destroy()
    waistSlider = new SlideRuler({
        el: waistRef.value,
        ...commonProps,
        maxValue: 200,
        minValue: 10,
        currentValue: waist.value,
        handleValue: (v: number) => {
            waist.value = v
        },
    })
}

function renderHipRule() {
    hipSlider?.destroy()
    hipSlider = new SlideRuler({
        el: hipRef.value,
        ...commonProps,
        maxValue: 200,
        minValue: 10,
        currentValue: hip.value,
        handleValue: (v: number) => {
            hip.value = v
        },
    })
}

onMounted(async () => {
    const { closeLoading } = useLoading()

    try {
        const { results: archiveResults } = await useWrapFetch<BaseResponse<Archives>>('/user/preliminaryArchive')
        waist.value = Number(archiveResults.waist) || 90

        renderWaistRule()
        renderHipRule()
    } catch (error) {
        console.error(error)
    } finally {
        closeLoading()
    }
})

const isWaistDialogShow = ref(false)
const isHipDialogShow = ref(false)

function handleCalculate() {
    isShowResult.value = false
    if (isFirstCalculate.value) {
        isFirstCalculate.value = false
    }

    calculateRatio()
    calculateIsOverweight()

    setTimeout(() => {
        isShowResult.value = true
    }, 1000)
}
</script>

<template>
    <div v-show="waist" class="flex flex-col gap-16px p-16px h-100vh">
        <div class="flex justify-between items-center">
            <div class="text-16px font-600">
                性别
            </div>
            <user-gender-picker v-model="gender" />
        </div>

        <div class="flex w-full flex-col gap-8px items-center justify-center overflow-hidden mt-16px">
            <div class="flex items-center relative w-full">
                <div class="text-#1D2129 text-16px absolute left-0 top-1/2 -translate-y-1/2 font-600">腰围</div>
                <div class="flex-1 text-center flex items-center justify-center">
                    <span text="t-5 18px" mr-4px font-600>
                        {{ waist.toFixed(1) }}
                    </span>
                    <span text="t-5 10px" relative top-3px>
                        cm
                    </span>
                    <div class="i-custom:pen w-12px h-12px ml-4px relative bottom-3px" @click="isWaistDialogShow = true"></div>
                </div>
            </div>
            <div ref="waistRef"></div>
        </div>

        <div class="flex w-full flex-col gap-8px items-center justify-center overflow-hidden mt-16px">
            <div class="flex items-center relative w-full">
                <div class="text-#1D2129 text-16px absolute left-0 top-1/2 -translate-y-1/2 font-600">臀围</div>
                <div class="flex-1 text-center flex items-center justify-center">
                    <span text="t-5 18px" mr-4px font-600>
                        {{ hip.toFixed(1) }}
                    </span>
                    <span text="t-5 10px" relative top-3px>
                        cm
                    </span>
                    <div class="i-custom:pen w-12px h-12px ml-4px relative bottom-3px" @click="isHipDialogShow = true"></div>
                </div>
            </div>
            <div ref="hipRef"></div>
        </div>

        <div v-if="!isFirstCalculate" class="flex justify-center mt-16px">
            <van-button type="primary" round class="w-241px h-50px" @click="handleCalculate">
                重新计算
            </van-button>
        </div>

        <div v-show="isShowResult" class="mt-32px rounded-t-10px" style="background: linear-gradient(180deg, #B9FFF7 0%, #FFFFFF 21.89%);">
            <div class="px-16px py-12px flex flex-col justify-between">
                <div>
                    <div class="flex justify-between items-center">
                        <div class="text-t-5 text-15px font-600">
                            腰臀比
                        </div>

                        <span class="text-primary-6 text-24px font-800 font-ddinpro">
                            {{ ratio.toFixed(2) }}
                        </span>
                    </div>

                    <div class="text-primary-6 font-600 text-14px my-10px">
                        {{ isOverweight ? '腹部肥胖' : '无腹部肥胖' }}
                    </div>

                    <div class="text-t-5">
                        腰臀比：
                        <br />
                        是腰围与臀围的比值，反映脂肪分布。
                        健康范围：男性0.7-0.9，女性0.6-0.8。男性＞0.9、女性＞0.85属腹部肥胖（苹果型），内脏脂肪多，心血管疾病风险高；比值较低（男性≤0.9，女性≤0.85）多为臀部/大腿脂肪堆积（梨型），健康风险相对低。
                    </div>
                </div>
            </div>
        </div>

        <div v-if="isFirstCalculate" class="flex justify-center absolute bottom-86px left-0 w-full">
            <van-button type="primary" round class="w-241px h-50px" @click="handleCalculate">
                开始计算
            </van-button>
        </div>

        <edit-dialog
            v-model:show="isWaistDialogShow" title="设置腰围" :value="waist" unit="厘米"
            @confirm="(value: number) => {
                waist = value
                renderWaistRule()
            }"
        />
        <edit-dialog
            v-model:show="isHipDialogShow" title="设置臀围" :value="hip" unit="厘米"
            @confirm="(value: number) => {
                hip = value
                renderHipRule()
            }"
        />

        <shared-full-loading v-if="!isFirstCalculate && !isShowResult" overlay />
    </div>
</template>
