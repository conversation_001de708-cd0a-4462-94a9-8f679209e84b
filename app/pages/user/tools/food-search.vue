<script setup lang="ts">
definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white overflow-auto',
        },
    },
})

useHead({
    title: '食物查询',
})

const searchValue = ref<string>('')
const storeKeyword = useRouteQuery<string>('keyword', '')
const skeletonLoading = ref<boolean>(false)
const foodList = ref<any[]>([])

onMounted(() => {
    if (storeKeyword.value) {
        searchValue.value = String(storeKeyword.value)
        handleSearchFood()
    }
})

function handleClearSearch() {
    searchValue.value = ''
    storeKeyword.value = ''
    foodList.value = []
}

async function handleSearchFood() {
    if (!searchValue.value) {
        return
    }
    try {
        storeKeyword.value = searchValue.value
        skeletonLoading.value = true
        const { results } = await useWrapFetch<BaseResponse<any>>('/checkInFoodRecommend/listFoodClassificationsByFoodName', {
            params: {
                foodName: searchValue.value,
            },
        })
        foodList.value = formatSubNodes([results])
    } catch (error) {
        foodList.value = []
    } finally {
        skeletonLoading.value = false
    }
}

watch(searchValue, (newVal, oldVal) => {
    if (!newVal && oldVal) {
        handleClearSearch()
    }
})

function formatSubNodes(subResults: any[]) {
    return subResults
        .map((item) => {
            const foodType = item.foodClassification

            function addFoodType(arr: any[]) {
                return Array.isArray(arr) ? arr.map(food => ({ ...food, foodType })) : []
            }

            return [
                ...addFoodType(item.aiFoods),
                ...addFoodType(item.aiIngredients),
                ...addFoodType(item.aiPackagedFoods),
            ]
        })
        .flat()
        .filter(Boolean)
}

function handleChooseFood(item: any) {
    if (!item) return
    localStorage.setItem('foodDetail', JSON.stringify(item))
    navigateTo('/user/tools/food-detail')
}

function highlightText(text: string) {
    if (!searchValue.value) return text

    const reg = new RegExp(searchValue.value, 'gi')
    return text.replace(reg, match => `<span class="text-primary-6">${match}</span>`)
}
</script>

<template>
    <div class="flex flex-col justify-between h-100vh max-h-100vh">
        <div>
            <img v-if="!storeKeyword" src="@/assets/images/service/banner-swcx.png" class="w-100% h-155px" />
            <div
                :class="{
                    '-mt-24px': !storeKeyword,
                }"
            >
                <van-search
                    v-model="searchValue"
                    shape="round"
                    clearable
                    clear-trigger="always"
                    placeholder="海量食物一键搜索"
                    background="#FFFFFF"
                    @clear="handleClearSearch"
                />
            </div>
        </div>

        <div
            v-if="storeKeyword"
            class="px-12px flex-1 h-calc(100vh - 56px) overflow-y-auto relative"
        >
            <van-skeleton v-if="skeletonLoading">
                <template #template>
                    <div class="flex flex-1">
                        <div :style="{ flex: 1 }">
                            <van-skeleton-paragraph v-for="i in 12" :key="i" class="h-50px! rd-10px" />
                        </div>
                    </div>
                </template>
            </van-skeleton>
            <template v-else>
                <template v-if="foodList.length > 0">
                    <div
                        v-for="item in foodList"
                        :key="item.foodId"
                        class="food-item flex items-center px-16px py-12px active:bg-gray-100"
                        @click="handleChooseFood(item)"
                    >
                        <van-image
                            :src="item.foodImage || `/images/foodTypes/${item.foodType}.png`"
                            class="w-50px h-50px rd-4px mr-8px overflow-hidden"
                        >
                            <template #error>
                                <img src="/images/foodTypes/others.png" alt="" srcset="" />
                            </template>
                        </van-image>
                        <div class="flex-1">
                            <div
                                class="text-14px text-t-5 font-600 line-clamp-1"
                                v-html="highlightText(item.foodName)"
                            ></div>
                            <div class="text-12px text-t-4 mt-4px">
                                <span class="text-primary-6">{{
                                    extractNumber(item.caloriesPer100g)
                                }}</span>千卡/100克
                            </div>
                        </div>
                    </div>
                </template>

                <van-empty v-else description="暂无食物数据" class="w-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
            </template>
        </div>

        <div
            v-if="!storeKeyword"
            class="flex justify-center mb-48px"
        >
            <van-button
                type="primary"
                round
                class="w-241px h-50px"
                @click="handleSearchFood"
            >
                搜索食物
            </van-button>
        </div>
    </div>
</template>

<style lang="scss" scoped>
:deep(.van-search__content) {
    border: 1px solid #00AC97;
    background: #FFFFFF;
}

:deep(.van-field__left-icon .van-icon) {
    color: #00AC97;
}

.food-item {
    border-bottom: 1px solid var(--van-gray-3);
}

.food-item:last-child {
    border-bottom: none;
}
</style>
