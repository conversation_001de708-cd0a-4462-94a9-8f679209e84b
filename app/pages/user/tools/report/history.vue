<script setup lang="ts">
useHead({
    title: '历史上传',
})

const list = ref([
    {
        date: '2025-07-16',
        images: [
            {
                id: 1,
                img: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg',
            },
            {
                id: 2,
                img: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg',
            },
            {
                id: 3,
                img: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg',
            },
            {
                id: 4,
                img: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg',
            },
        ],
    },
    {
        date: '2025-07-15',
        images: [
            {
                id: 5,
                img: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg',
            },
            {
                id: 6,
                img: 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg',
            },
        ],
    },
])
</script>

<template>
    <div class="relative h-100vh p-16px">
        <div style="background: linear-gradient(180deg, #DBFFFB 0%, #F4F5F7 100%);" class="h-[270px] absolute top-0 left-0 right-0">
        </div>

        <div class="bg-white rounded-10px p-16px h-full flex flex-col relative overflow-y-auto">
            <template v-if="list.length > 0">
                <div v-for="item in list" :key="item.date">
                    <div class="text-12px font-400 text-#868F9C h-22px w-full lh-22px">{{ item.date }}</div>
                    <div class="grid grid-cols-3 gap-8px">
                        <div v-for="image in item.images" :key="image.id" class="flex items-center justify-center">
                            <van-image
                                width="100px"
                                height="80px"
                                fit="cover"
                                :src="image.img"
                            />
                        </div>
                    </div>
                </div>
            </template>

            <template v-else>
                <van-empty class="m-auto" description="暂无检测项目" />
            </template>
        </div>
    </div>
</template>
