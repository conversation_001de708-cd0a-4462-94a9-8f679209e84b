<script setup lang="ts">
import CustomUploader from '@/components/user/custom-uploader.vue'
import { useImageUploader } from '@/composables/useCustomUploader'

const MAX_PICTURES = 1

useHead({
    title: '上传报告',
})

const {
    pictures: reportPictures,
    handleBeforeRead,
    handlePreview,
    handleDelete,
} = useImageUploader({
    maxCount: MAX_PICTURES,
    onUploadComplete: (fileIds) => {
        console.log('上传完成，文件ID:', fileIds)
    },
})
</script>

<template>
    <div class="relative min-h-[100vh] p-16px bg-[url('~/assets/images/external/bg-report-uploader.svg')] bg-no-repeat bg-top bg-[length:100%_269px] flex flex-col items-center bg-#F4F5F7">
        <div class="w-full h-68px absolute top-16x bg-[url('~/assets/images/external/bg-report-uploader-text.svg')] bg-no-repeat bg-center bg-[length:100%_100%]"></div>
        <div class="flex flex-col w-100% h-fit bg-white rounded-10px p-16px gap-8px mt-80px">
            <custom-uploader
                v-model="reportPictures"
                :max-count="MAX_PICTURES"
                upload-button-height="68px"
                :show-description="false"
                @preview="handlePreview"
                @delete="handleDelete"
                @before-read="handleBeforeRead"
            />

            <div class="text-13px font-weight-400 text-#86909C">
                请上传 6 个月内的血压、血常规、血生化（肝功能、肾功能、血糖、血脂）、糖化血红蛋白、空腹胰岛素、尿微量白蛋白/尿肌酐、肝脏弹性检测报告单照片（最多{{ MAX_PICTURES }}张）
            </div>
        </div>
        <div class="flex flex-col items-center justify-center w-100% h-fit bg-white rounded-10px p-16px gap-8px mt-8px mb-16px">
            <div class="w-100% h-27px flex justify-center items-center gap-4px">
                <div class="w-14px h-13px bg-[url('~/assets/images/external/report-camera.svg')] bg-no-repeat bg-center bg-[length:100%_100%]"></div>
                拍摄示例
            </div>
            <div class="text-14px font-weight-400 text-#86909C">
                请将边框完整、图文清晰的单份检查、检验报告图片或者10M以内的体检报告文件发送给我。我将提供专业的医学解读，并且给出针对性的健康建议。
            </div>
            <div class="flex w-100% h-121px bg-#F2F4F7 rd-10px">
                <div class="flex-1 flex flex-col items-center justify-center gap-4px">
                    <div class="flex items-center justify-center gap-4px">
                        <div class="i-custom:check-circle-fill w-16px h-16px"></div>平整放置
                    </div>
                    <div class="w-109px h-76px bg-[url('~/assets/images/external/place-flat.svg')] bg-no-repeat bg-center bg-[length:100%_100%]"></div>
                </div>
                <div class="flex-1 flex flex-col items-center justify-center gap-4px">
                    <div class="flex items-center justify-center gap-4px">
                        <div class="i-custom:check-circle-fill w-16px h-16px"></div>完整拍摄
                    </div>
                    <div class="w-109px h-76px bg-[url('~/assets/images/external/full-placement.svg')] bg-no-repeat bg-center bg-[length:100%_100%]"></div>
                </div>
            </div>
        </div>
        <div class="flex items-center justify-center w-full gap-8px">
            <van-button
                type="primary"
                round class="flex-1 h-50px !bg-#E5E7EB !text-#1D2229 font-600 !border-none"
                @click="navigateTo('/user/tools/report/history')"
            >
                历史上传
            </van-button>
            <van-button
                type="primary" round class="flex-1 h-50px font-500"
                @click="navigateTo('/user/tools/report/interpretation')"
            >
                立即分析
            </van-button>
        </div>
    </div>
</template>

<style scoped>
:deep(.my-uploader .van-uploader__input-wrapper),
:deep(.my-uploader) {
    width: 100% !important;
}

:deep(.van-uploader) {
    --van-uploader-border-radius: 10px;
    --van-uploader-delete-icon-size: 24px;
    --van-uploader-delete-background: #11111126;
}

:deep(.van-uploader__preview-image) {
    border: 1px solid #F2F4F7
}

:deep(.van-uploader__preview-delete--shadow) {
    border-top-right-radius: 10px;
}
</style>
