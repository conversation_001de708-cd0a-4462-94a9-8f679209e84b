<script setup lang="ts">
import SlideRuler from '@/utils/slide-rule'
import EditDialog from '@/components/survey/edit-dialog.vue'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

useHead({
    title: '标准体重',
})

const isFirstCalculate = ref(true)
const isShowResult = ref(false)

const gender = ref<'male' | 'female'>('male')

const rulerRef = useTemplateRef('rulerRef')

const height = ref(0)

const standWeight = ref<number>(0)

function calculateStandWeight() {
    if (gender.value === 'male') {
        standWeight.value = (height.value - 80) * 0.7
    } else {
        standWeight.value = (height.value - 70) * 0.6
    }
}

const normalRange = ref<[number, number]>([0, 0])

function calculateNormalRange() {
    // BMI 18~24
    const minBmi = 18
    const maxBmi = 24

    const minWeight = minBmi * (height.value / 100) ** 2
    const maxWeight = maxBmi * (height.value / 100) ** 2

    normalRange.value = [minWeight, maxWeight]
}

const isDialogShow = ref(false)

let rulerSlider: any

function renderHeightRule() {
    rulerSlider?.destroy()
    rulerSlider = new SlideRuler({
        el: rulerRef.value,
        canvasHeight: 60,
        fontSize: 13,
        precision: 0.1,
        divide: 5,
        heightDecimal: 28,
        lineWidth: 2,
        heightDigit: 18,
        fontMarginTop: 45,
        maxValue: 300,
        minValue: 60,
        currentValue: height.value,
        handleValue: (v: number) => {
            height.value = v
        },
    })
}

onMounted(async () => {
    const { closeLoading } = useLoading()

    try {
        const { results: archiveResults } = await useWrapFetch<BaseResponse<Archives>>('/user/preliminaryArchive')
        height.value = Number(archiveResults.archiveHeight) || 160
        renderHeightRule()
    } catch (error) {
        console.error(error)
    } finally {
        closeLoading()
    }
})

function handleCalculate() {
    isShowResult.value = false
    if (isFirstCalculate.value) {
        isFirstCalculate.value = false
    }

    calculateStandWeight()
    calculateNormalRange()

    setTimeout(() => {
        isShowResult.value = true
    }, 1000)
}
</script>

<template>
    <div v-show="height" class="flex flex-col gap-16px p-16px h-100vh">
        <div class="flex justify-between items-center">
            <div class="text-16px font-600">
                性别
            </div>
            <user-gender-picker v-model="gender" />
        </div>

        <div class="flex w-full flex-col gap-8px items-center justify-center overflow-hidden mt-16px">
            <div class="flex items-center relative w-full">
                <div class="text-#1D2129 text-16px absolute left-0 top-1/2 -translate-y-1/2 font-600">身高</div>
                <div class="flex-1 text-center flex items-center justify-center">
                    <span text="t-5 18px" mr-4px font-600>
                        {{ height.toFixed(1) }}
                    </span>
                    <span text="t-5 10px" relative top-3px>
                        cm
                    </span>
                    <div class="i-custom:pen w-12px h-12px ml-4px relative bottom-3px" @click="isDialogShow = true"></div>
                </div>
            </div>
            <div ref="rulerRef"></div>
        </div>

        <div v-if="!isFirstCalculate" class="flex justify-center">
            <van-button type="primary" round class="w-241px h-50px" @click="handleCalculate">
                重新计算
            </van-button>
        </div>

        <div
            v-show="isShowResult"
            class="mt-16px rounded-t-10px" style="background: linear-gradient(180deg, #B9FFF7 0%, #FFFFFF 21.89%);"
        >
            <div class="px-16px py-12px flex flex-col justify-between h-[calc(100vh-350px)]">
                <div>
                    <div class="flex justify-between items-center">
                        <div class="text-t-5 text-15px font-600">
                            标准体重值
                        </div>

                        <div class="flex items-center gap-5px">
                            <span class="text-primary-6 text-24px font-800 font-ddinpro">
                                {{ standWeight.toFixed(1) }}
                            </span>
                            <span class="text-t-5 text-15px font-600">
                                kg
                            </span>
                        </div>
                    </div>

                    <div class="text-t-5 py-8px mt-5px">
                        <div>
                            <div class="flex justify-between">
                                <div>
                                    正常区间：
                                </div>

                                <div>
                                    {{ normalRange[0]?.toFixed(1) }}~{{ normalRange[1]?.toFixed(1) }} 公斤
                                </div>
                            </div>
                        </div>

                        <div class="mt-10px">
                            <div class="flex justify-between">
                                <div>
                                    标准值：
                                </div>

                                <div>
                                    {{ standWeight.toFixed(1) }} 公斤
                                </div>
                            </div>
                        </div>
                        <div class="mt-20px">
                            标准体重:
                            <br />
                            人的体重受多种因素影响，不同个体、一天中不同时段、地理位置、季节气候、生理周期等都会造成体重波动，所以标准体重仅作参考，要综合多方面因素评估体重是否健康。
                        </div>
                    </div>
                </div>

                <div class="text-t-3 text-12px flex flex-col gap-8px">
                    <div>
                        *小程序中标准体重采用世界卫生组织（WHO）标准：
                    </div>
                    <div>
                        男性标准体重（kg）=（身高（cm）-80）×70%；
                    </div>
                    <div>
                        女性标准体重（kg）=（身高（cm）-70）×60%
                    </div>
                </div>
            </div>
        </div>

        <div v-if="isFirstCalculate" class="flex justify-center absolute bottom-86px left-0 w-full">
            <van-button type="primary" round class="w-241px h-50px" @click="handleCalculate">
                开始计算
            </van-button>
        </div>

        <edit-dialog
            v-model:show="isDialogShow" title="设置身高" :value="height" unit="厘米"
            @confirm="(value) => {
                height = value
                renderHeightRule()
            }"
        />

        <shared-full-loading v-if="!isFirstCalculate && !isShowResult" overlay />
    </div>
</template>
