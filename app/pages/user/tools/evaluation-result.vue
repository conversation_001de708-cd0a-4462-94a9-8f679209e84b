<script setup lang="ts">
useHead({
    title: '评估结果',
})

const route = useRoute()
const evaluationType = route.query.type as string || 'nfs' // 'nfs' 或 'zju'
const nfsScore = ref(Number.parseFloat(route.query.nfsScore as string) || -0.67)
const zjuScore = ref(Number.parseFloat(route.query.zjuScore as string) || 30)
const evaluationResult = ref(route.query.evaluationResult as string)

const progressColors = computed(() => {
    const colorMap = {
        低风险: ['#00AC97', '#FFCF8B', '#FDCDC5'],
        中风险: ['#A9E8E8', '#FFAE4A', '#FDCDC5'],
        高风险: ['#A9E8E8', '#FFCF8B', '#F53F3F'],
    }

    return colorMap[evaluationResult.value as keyof typeof colorMap] || ['#A9E8E8', '#FFCF8B', '#FDCDC5']
})

// 评估类型信息
const evaluationTypeInfo = computed(() => {
    if (evaluationType === 'zju') {
        return {
            title: '脂肪肝风险',
            scoreLabel: 'ZJU评分',
            scoreValue: zjuScore.value,
            tipsTitle: 'Tips：关于ZJU评分',
            tipsContent: '适用人群为12 - 85岁男性和女性，能更有效识别代谢功能障碍及其相关健康风险，相比单一生化指标，具有更强的临床应用价值。',
        }
    } else {
        return {
            title: '肝纤维化风险',
            scoreLabel: 'NFS评分',
            scoreValue: nfsScore.value,
            tipsTitle: 'Tips：关于NFS评分',
            tipsContent: '即非酒精性脂肪性肝病纤维化评分（Nonalcoholic Fatty Liver Disease Fibrosis Score），主要用于非酒精性脂肪性肝病（NAFLD）患者的肝纤维化评估。',
        }
    }
})

const evaluationData = computed(() => {
    if (evaluationType === 'zju') {
        // ZJU评分逻辑
        const score = zjuScore.value

        if (score < 32) {
            return {
                riskLevel: '低风险',
                riskLevelColor: '#00AC97',
                riskDescription: '有较大可能未患有MAFLD（代谢相关脂肪性肝病），但不能完全排除其他肝脏疾病的可能',
                suggestion: '保持均衡饮食（多吃蔬果、全谷物、优质蛋白），每周150分钟中等强度运动（如快走、游泳），规律作息（7-9小时睡眠），戒烟限酒，定期体检。',

            }
        } else if (score >= 32 && score <= 38) {
            return {
                riskLevel: '中风险',
                riskLevelColor: '#FFAE4A',
                riskDescription: '无法明确是否患有MAFLD（代谢相关脂肪性肝病），需结合其他检查如肝功能、血脂、血糖、肝脏超声等进行综合判断',
                suggestion: '控制总热量，减少高糖/高盐/高脂食物；每周150分钟有氧运动+力量训练（如瑜伽、举重），避免久坐；管理压力（冥想、深呼吸），保证睡眠，定期监测指标。',
            }
        } else {
            return {
                riskLevel: '高风险',
                riskLevelColor: '#F53F3F',
                riskDescription: '患有MAFLD的可能性较大（代谢相关脂肪性肝病），但也可能存在其他导致指数升高的因素，需进一步做肝活检等检查来确诊',
                suggestion: '严格控能（每日减500-1000千卡），均衡膳食（碳水50%-60%、脂肪20%-30%）；高强度运动（有氧+力量），戒烟限酒；保证睡眠，定期就医复查。',
            }
        }
    } else {
        // NFS评分逻辑
        const score = nfsScore.value

        if (score < -1.455) {
            return {
                riskLevel: '低风险',
                riskLevelColor: '#00AC97',
                riskDescription: '提示肝纤维化程度低',
                suggestion: '发展为肝硬化等严重疾病的可能性较小。建议保持健康生活方式，定期复查。',
            }
        } else if (score >= -1.455 && score <= 0.676) {
            return {
                riskLevel: '中风险',
                riskLevelColor: '#FFAE4A',
                riskDescription: '存在一定肝纤维化进展风险',
                suggestion: '需积极改善生活方式，如控制饮食、增加运动、管理体重等，并定期进行相关检查，监测病情变化。',
            }
        } else {
            return {
                riskLevel: '高风险',
                riskLevelColor: '#F53F3F',
                riskDescription: '提示肝纤维化程度较高',
                suggestion: '发生肝硬化、肝癌等严重并发症的风险增加。建议及时就医，在医生指导下进行进一步检查和治疗，如肝活检等，以明确纤维化程度并采取相应治疗措施。',
            }
        }
    }
})
</script>

<template>
    <div class="h-100vh p-16px pb-0px">
        <div
            class="w-full flex flex-col justify-between h-full bg-white p-16px pb-32px rd-lt-10px rd-rt-10px"
        >
            <div class="flex flex-col gap-16px">
                <div class="flex justify-between items-center w-full text-16px font-600">
                    <span class="text-t-5">{{ evaluationTypeInfo.title }}</span>
                    <span :style="{ color: evaluationData.riskLevelColor }">{{ evaluationData.riskLevel }}</span>
                </div>

                <div class="text-12px font-600 text-primary-6 w-fit h-22px px-8px rd-4px bg-#E4FAF9 lh-22px">
                    {{ evaluationTypeInfo.scoreLabel }}：{{ evaluationTypeInfo.scoreValue.toFixed(evaluationType === 'zju' ? 1 : 3) }}
                </div>

                <div>
                    <div class="flex items-stretch gap-8px h-8px">
                        <div
                            v-for="(color, index) in progressColors"
                            :key="index"
                            class="flex-1 rd-30px h-8px"
                            :style="{ backgroundColor: color }"
                        ></div>
                    </div>
                    <div class="flex items-stretch gap-8px h-fit mt-4px">
                        <div
                            class="flex-1 flex items-center justify-center"
                        >
                            <img v-if="evaluationData.riskLevel === '低风险'" src="@/assets/images/service/risk-1.png" class="w-10px h-12px" />
                        </div>
                        <div
                            class="flex-1 flex items-center justify-center"
                        >
                            <img v-if="evaluationData.riskLevel === '中风险'" src="@/assets/images/service/risk-2.png" class="w-10px h-12px" />
                        </div>
                        <div
                            class="flex-1 flex items-center justify-center"
                        >
                            <img v-if="evaluationData.riskLevel === '高风险'" src="@/assets/images/service/risk-3.png" class="w-10px h-12px" />
                        </div>
                    </div>
                </div>

                <div class="h-fit w-full bg-#F4F5F7 rd-10px px-16px py-8px text-12px text-#4E5969">
                    {{ evaluationData.riskDescription }}
                </div>

                <div class="flex flex-col">
                    <div class="w-fit bg-#E4FAF9 px-16px rd-lt-10px rd-rt-10px h-30px lh-30px text-14px font-600 text-#00AC97">
                        改善建议
                    </div>
                    <div class="bg-fill-1 rd-rt-10px rd-lb-10px rd-rb-10px px-16px py-8px text-13px text-#4E5969">
                        {{ evaluationData.suggestion }}
                    </div>
                </div>
            </div>

            <div class="text-t-3 text-12px">
                <div class="text-13px font-600">{{ evaluationTypeInfo.tipsTitle }}</div>
                {{ evaluationTypeInfo.tipsContent }}
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.font-dinpro {
    font-family: 'DINPro-Medium', sans-serif;
}
</style>
