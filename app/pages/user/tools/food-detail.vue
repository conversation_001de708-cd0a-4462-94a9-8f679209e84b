<script setup lang="ts">
useHead({
    title: '食物查询',
})

const foodDetail = ref<any>({})

onMounted(() => {
    const storedData = localStorage.getItem('foodDetail')
    if (storedData) {
        try {
            foodDetail.value = JSON.parse(storedData)
        } catch (e) {
            console.error('解析foodDetail数据失败:', e)
            foodDetail.value = {}
        }
    }
})

const nutrients = computed(() => {
    try {
        return JSON.parse(foodDetail.value.nutrientsPer100g || '[]')
    } catch (e) {
        return []
    }
})

function extractValue(str: string | undefined): number {
    if (!str) return 0
    const match = str.match(/([0-9.]+)/)
    return match?.[1] ? Number.parseFloat(match[1]) : 0
}

const mainNutrients = [
    { name: '蛋白质', displayName: '蛋白质', color: '#FBACA3' },
    { name: '脂肪', displayName: '脂肪', color: '#FFCF8B' },
    { name: '碳水化合物', displayName: '碳水', color: '#7BE188' },
    { name: '膳食纤维', displayName: '膳食纤维', color: '#6AD9CB' },
]

function getNutrientValue(nutrientName: string): number {
    const nutrient = nutrients.value.find((n: any) => n.name === nutrientName)
    return nutrient ? extractValue(nutrient.value) : 0
}

const nutritionData = computed(() => {
    const result = mainNutrients.map((nutrient) => {
        const value = getNutrientValue(nutrient.name)
        const percentage = Math.min(value, 100)

        return {
            name: nutrient.displayName,
            value: value.toFixed(1),
            unit: 'g',
            color: nutrient.color,
            percentage: Number(percentage.toFixed(1)),
        }
    })

    return result
})

const nutritionDetails = computed(() => {
    const excludedNames = mainNutrients.map(n => n.name)
    return nutrients.value
        .filter((nutrient: any) => !excludedNames.includes(nutrient.name))
        .map((nutrient: any) => ({
            name: nutrient.name,
            value: nutrient.value || '-',
        }))
})

onBeforeUnmount(() => {
    localStorage.removeItem('foodDetail')
})
</script>

<template>
    <div class="flex flex-col h-100vh max-h-100vh gap-12px">
        <div
            class="food-item flex items-center px-16px py-12px bg-white"
        >
            <van-image
                :src="foodDetail?.foodImage || `/images/foodTypes/${foodDetail?.foodType}.png`"
                class="w-50px h-50px rd-4px mr-8px overflow-hidden"
            >
                <template #error>
                    <img src="/images/foodTypes/others.png" alt="" srcset="" />
                </template>
            </van-image>
            <div class="flex-1">
                <div
                    class="text-14px text-t-5 font-600 line-clamp-1"
                >
                    {{ foodDetail.foodName }}
                </div>
                <div class="text-12px text-t-4 mt-4px">
                    <span class="text-primary-6">{{
                        extractNumber(foodDetail.caloriesPer100g)
                    }}</span>千卡/100克
                </div>
            </div>
        </div>

        <div class="mx-12px flex-1 bg-white rd-lt-10px rd-rt-10px h-calc(100vh - 100px) overflow-y-auto">
            <div class="p-12px flex flex-col gap-12px">
                <div class="flex items-center text-15px text-t-5 font-600">
                    营养元素
                </div>

                <div class="grid grid-cols-4 gap-6px">
                    <div
                        v-for="(item, index) in nutritionData"
                        :key="index"
                        class="flex flex-col items-center py-2px rd-10px bg-white"
                    >
                        <van-circle
                            v-if="item.percentage > 0"
                            :current-rate="item.percentage"
                            :size="48"
                            :stroke-width="80"
                            :color="item.color"
                            layer-color="#F4F5F7"
                            :text="`${item.value}${item.unit}`"
                        />
                        <div v-else class="w-48px h-48px flex items-center justify-center">
                            -
                        </div>
                        <span class="text-12px text-t-3 leading-22px mt-2px">
                            {{ item.name }}
                        </span>
                    </div>
                </div>

                <div class="w-full h-1px bg-#E5E7EB"></div>

                <div class="flex flex-col gap-8px">
                    <template v-if="nutritionDetails.length > 0">
                        <div class="flex items-center gap-8px">
                            <div class="flex-1 text-13px text-t-3">营养素</div>
                            <div class="text-13px text-t-3">每 100 克</div>
                        </div>

                        <div
                            v-for="(item, index) in nutritionDetails"
                            :key="index"
                            class="flex items-center gap-8px"
                        >
                            <div class="flex-1 text-15px text-t-5">{{ item.name }}</div>
                            <div class="text-15px text-t-5">{{ item.value }}</div>
                        </div>
                    </template>
                    <van-empty v-else description="暂无其他营养元素数据" class="w-full m-auto" />
                </div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
:deep(.van-circle__text) {
    font-size: 13px;
    font-weight: 600;
}
</style>
