<script setup lang="ts">
import SlideRuler from '@/utils/slide-rule'
import EditDialog from '@/components/survey/edit-dialog.vue'
// import CustomUploader from '@/components/user/custom-uploader.vue'
// import { useImageUploader } from '@/composables/useCustomUploader'
// import { parseLabResults } from '@/utils/liver'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

useHead({
    title: '肝纤维化评估',
})

const age = ref<number>(0)
const heightValue = ref<number>(0)
const weightValue = ref<number>(0)
const diabetes = ref<string>('')
const astAlt = ref<number>()
const platelets = ref<number>()
const albumin = ref<number>()
// const MAX_PICTURES = 1
const isHeightDialogShow = ref(false)
const isWeightDialogShow = ref(false)

// const {
//     pictures: liverPictures,
//     isImageLoading,
//     handleBeforeRead,
//     handlePreview,
//     handleDelete,
//     setImageLoading,
// } = useImageUploader({
//     maxCount: MAX_PICTURES,
//     onUploadComplete: async (fileIds) => {
//         if (fileIds.length > 0) {
//             await getOcrResult(fileIds)
//         }
//     },
// })

const isCalculationDisabled = computed(() => {
    return !(
        age.value > 0
        && heightValue.value
        && weightValue.value
        && diabetes.value
        && astAlt.value
        && platelets.value
        && albumin.value
    )
})

const heightRulerRef = useTemplateRef('heightRulerRef')
const weightRulerRef = useTemplateRef('weightRulerRef')

let heightSlider: any
let weightSlider: any

const lastVibrationTime = ref(0)
const VIBRATION_THRESHOLD = 100

async function renderHeightRule() {
    await nextTick()
    heightSlider?.destroy()
    heightSlider = new SlideRuler({
        canvasHeight: 60,
        fontSize: 13,
        heightDecimal: 28,
        heightDigit: 18,
        fontMarginTop: 45,
        el: heightRulerRef.value,
        currentValue: heightValue.value,
        maxValue: 300,
        minValue: 60,
        divide: 5,
        precision: 1,
        handleValue: (v: number) => {
            heightValue.value = v || 0

            if (navigator?.vibrate) {
                const now = Date.now()
                if (now - lastVibrationTime.value > VIBRATION_THRESHOLD) {
                    navigator?.vibrate([10, 10])
                    lastVibrationTime.value = now
                }
            }
        },
    })
}

async function renderWeightRule() {
    await nextTick()
    weightSlider?.destroy()
    weightSlider = new SlideRuler({
        canvasHeight: 60,
        fontSize: 13,
        heightDecimal: 28,
        heightDigit: 18,
        fontMarginTop: 45,
        el: weightRulerRef.value,
        currentValue: weightValue.value,
        maxValue: 200,
        minValue: 30,
        divide: 5,
        precision: 0.1,
        handleValue: (v: number) => {
            weightValue.value = v || 0

            if (navigator?.vibrate) {
                const now = Date.now()
                if (now - lastVibrationTime.value > VIBRATION_THRESHOLD) {
                    navigator?.vibrate([10, 10])
                    lastVibrationTime.value = now
                }
            }
        },
    })
}

// async function getOcrResult(fileIds: string[]) {
//     if (!fileIds || fileIds.length === 0) {
//         showToast('未获取到报告文件信息')
//         return
//     }

//     try {
//         setImageLoading(true)
//         const { results } = await useWrapFetch<BaseResponse<any>>('/api/qwen/getOcrResult', {
//             method: 'post',
//             body: {
//                 fileIds,
//             },
//         })

//         if (results?.items && Array.isArray(results.items)) {
//             const parsedResults = parseLabResults(results.items)

//             if (parsedResults.altAst) {
//                 astAlt.value = parsedResults.altAst
//             } else {
//                 showToast('未解析到AST/ALT结果')
//             }
//             if (parsedResults.platelets) {
//                 platelets.value = parsedResults.platelets
//             } else {
//                 showToast('未解析到血小板结果')
//             }
//             if (parsedResults.albumin) {
//                 albumin.value = parsedResults.albumin
//             } else {
//                 showToast('未解析到血蛋白结果')
//             }
//         }
//     } catch (error: any) {
//         console.error('报告生成失败：', error)
//     } finally {
//         setImageLoading(false)
//     }
// }

async function getArchiveData() {
    const { closeLoading } = useLoading()
    try {
        const { results: archiveResults } = await useWrapFetch<BaseResponse<Archives>>('/user/preliminaryArchive')
        age.value = Number((archiveResults as any)?.age)
        heightValue.value = Number((archiveResults as any)?.archiveHeight)
        weightValue.value = Number((archiveResults as any)?.archiveWeight)
    } catch (error) {
        console.error(error)
    } finally {
        closeLoading()
    }
}

async function initData() {
    await getArchiveData()
    renderHeightRule()
    renderWeightRule()
}

function handleEvaluationRecord(type: string) {
    navigateTo({
        path: '/user/tools/evaluation-record',
        query: {
            type,
        },
    })
}

async function handleStartCalculation() {
    try {
    // 将身高从 cm 转换为米
        const heightInMeters = heightValue.value / 100

        // 计算 BMI = 体重(kg) / 身高²(m²)
        const bmi = Number((weightValue.value / (heightInMeters ** 2)).toFixed(2))

        // 转换糖尿病值为数值（"1"/"0" -> 1/0）
        const diabetesValue = Number(diabetes.value)

        // 拿到 AST/ALT 比值
        const astAltRatio = astAlt.value!

        // 拿到血小板（单位：×10⁹/L）
        const plateletCount = platelets.value!

        // 拿到白蛋白（单位：g/dL）
        const albuminLevel = albumin.value!

        // NFS=[-1.675 + 0.037×年龄（岁）+ 0.094×BMI（kg/m2）*****×空腹血糖受损（IFG）/糖尿病（是= 1，否= 0) +0.99×AST/ALT比值+ 0.013×血小板（×109/L）-0.66×白蛋白（g/dl）
        const nfsScore = -1.675
            + 0.037 * age.value
            + 0.094 * bmi
            + 1.13 * diabetesValue
            + 0.99 * astAltRatio
            + 0.013 * plateletCount
            - 0.66 * albuminLevel

        // 保留三位小数
        const nfsResult = Number(nfsScore.toFixed(3))

        // 根据NFS评分结果确定风险等级
        let evaluationResult = ''
        if (nfsResult < -1.455) {
            evaluationResult = '低风险'
        } else if (nfsResult >= -1.455 && nfsResult <= 0.676) {
            evaluationResult = '中风险'
        } else {
            evaluationResult = '高风险'
        }

        const { results } = await useWrapFetch<BaseResponse<any>>('/api/assessment-instrument', {
            method: 'post',
            body: {
                type: 'nfs',
                evaluationValue: nfsResult.toString(),
                evaluationResult,
            },
        })

        if (results) {
            navigateTo({
                path: '/user/tools/evaluation-result',
                query: {
                    type: 'nfs',
                    nfsScore: nfsResult,
                    evaluationResult,
                },
            })
        }
    } catch (error) {
        console.error('计算失败', error)
    }
}

onMounted(() => {
    initData()
})
</script>

<template>
    <div class="flex flex-col justify-between p-16px h-100vh overflow-y-auto">
        <div class="flex flex-col gap-24px">
            <div class="flex justify-between items-center">
                <div class="text-#1D2129 text-16px font-600">年龄</div>
                <div class="flex items-center w-90px">
                    <van-field v-model="age" type="digit" placeholder="请输入年龄" clearable />
                </div>
            </div>

            <div class="flex w-full flex-col gap-8px items-center justify-center overflow-hidden">
                <div class="flex items-center relative w-full">
                    <div class="text-#1D2129 text-16px absolute left-0 top-1/2 -translate-y-1/2 font-600">身高</div>
                    <div class="flex-1 text-center flex items-center justify-center">
                        <span text="t-5 18px" mr-4px font-600>
                            {{ heightValue }}
                        </span>
                        <span text="t-5 10px" relative top-3px>
                            cm
                        </span>
                        <div class="i-custom:pen w-12px h-12px ml-4px relative bottom-3px" @click="isHeightDialogShow = true"></div>
                    </div>
                </div>
                <div ref="heightRulerRef"></div>
            </div>

            <div class="flex w-full flex-col gap-8px items-center justify-center overflow-hidden">
                <div class="flex items-center relative w-full">
                    <div class="text-#1D2129 text-16px absolute left-0 top-1/2 -translate-y-1/2 font-600">体重</div>
                    <div class="flex-1 text-center flex items-center justify-center">
                        <span text="t-5 18px" mr-4px font-600>
                            {{ weightValue }}
                        </span>
                        <span text="t-5 10px" relative top-3px>
                            kg
                        </span>
                        <div class="i-custom:pen w-12px h-12px ml-4px relative bottom-3px" @click="isWeightDialogShow = true"></div>
                    </div>
                </div>
                <div ref="weightRulerRef"></div>
            </div>

            <div class="flex justify-between items-center">
                <div class="text-#1D2129 text-16px font-600">糖尿病</div>
                <van-radio-group v-model="diabetes" direction="horizontal">
                    <van-radio name="1" checked-color="#00AC97" class="flex-1">有</van-radio>
                    <van-radio name="0" checked-color="#00AC97" class="flex-1">无</van-radio>
                </van-radio-group>
            </div>

            <div class="flex justify-between items-center">
                <div class="text-#1D2129 text-16px font-600">AST/ALT</div>
                <div class="flex items-center w-130px">
                    <van-field v-model="astAlt" type="number" placeholder="请输入AST/ALT" clearable />
                </div>
            </div>

            <div class="flex justify-between items-center">
                <div class="text-#1D2129 text-16px font-600">血小板</div>
                <div class="flex items-center w-130px">
                    <van-field v-model="platelets" type="number" placeholder="请输入血小板" clearable />
                </div>
            </div>

            <div class="flex justify-between items-center">
                <div class="text-#1D2129 text-16px font-600">血蛋白</div>
                <div class="flex items-center w-130px">
                    <van-field v-model="albumin" type="number" placeholder="请输入血蛋白" clearable />
                </div>
            </div>
        </div>

        <!-- <custom-uploader
            v-model="liverPictures"
            :max-count="MAX_PICTURES"
            upload-button-height="108px"
            :show-description="true"
            description="上述指标，您可以直接拍照上传"
            :is-image-loading="isImageLoading"
            @preview="handlePreview"
            @delete="handleDelete"
            @before-read="handleBeforeRead"
        /> -->

        <div class="flex justify-center gap-16px mb-36px">
            <van-button
                type="primary"
                round
                class="flex-1 !bg-#E4FAF9 h-50px !text-#00AC97 !border-#E4FAF9"
                @click="handleEvaluationRecord('nfs')"
            >
                测评记录
            </van-button>

            <van-button
                type="primary"
                round
                class="flex-1 h-50px"
                :disabled="isCalculationDisabled"
                @click="handleStartCalculation"
            >
                开始计算
            </van-button>
        </div>

        <edit-dialog
            v-model:show="isHeightDialogShow" title="设置身高" :value="heightValue" unit="厘米"
            @confirm="(value) => {
                heightValue = value
                renderHeightRule()
            }"
        />

        <edit-dialog
            v-model:show="isWeightDialogShow" title="设置体重" :value="weightValue" unit="千克"
            @confirm="(value) => {
                weightValue = value
                renderWeightRule()
            }"
        />
    </div>
</template>

<style scoped>
:deep(.van-cell) {
    padding: 0;

    input {
        text-align: right;
    }
}

:deep(.my-uploader .van-uploader__input-wrapper),
:deep(.my-uploader) {
    width: 100% !important;
    height: 108px !important;
}

:deep(.van-uploader) {
    --van-uploader-border-radius: 10px;
    --van-uploader-delete-icon-size: 24px;
    --van-uploader-delete-background: #11111126;
}

:deep(.van-uploader__preview-image) {
    border: 1px solid #F2F4F7
}

:deep(.van-uploader__preview-delete--shadow) {
    border-top-right-radius: 10px;
}
</style>
