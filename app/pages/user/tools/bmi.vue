<script setup lang="ts">
import SlideRuler from '@/utils/slide-rule'
import EditDialog from '@/components/survey/edit-dialog.vue'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

useHead({
    title: 'BMI计算器',
})

const isFirstCalculate = ref(true)
const isShowResult = ref(false)

const heightValue = ref<number>(0)
const weightValue = ref<number>(0)
const bmi = ref<number>(0)
const isHeightDialogShow = ref(false)
const isWeightDialogShow = ref(false)

const heightRulerRef = useTemplateRef('heightRulerRef')
const weightRulerRef = useTemplateRef('weightRulerRef')

let heightSlider: any
let weightSlider: any

const lastVibrationTime = ref(0)
const VIBRATION_THRESHOLD = 100

async function renderHeightRule() {
    await nextTick()
    heightSlider?.destroy()
    heightSlider = new SlideRuler({
        canvasHeight: 60,
        fontSize: 13,
        heightDecimal: 28,
        heightDigit: 18,
        fontMarginTop: 45,
        el: heightRulerRef.value,
        currentValue: heightValue.value,
        maxValue: 300,
        minValue: 60,
        divide: 5,
        precision: 1,
        handleValue: (v: number) => {
            heightValue.value = v || 0

            if (navigator?.vibrate) {
                const now = Date.now()
                if (now - lastVibrationTime.value > VIBRATION_THRESHOLD) {
                    navigator?.vibrate([10, 10])
                    lastVibrationTime.value = now
                }
            }
        },
    })
}

async function renderWeightRule() {
    await nextTick()
    weightSlider?.destroy()
    weightSlider = new SlideRuler({
        canvasHeight: 60,
        fontSize: 13,
        heightDecimal: 28,
        heightDigit: 18,
        fontMarginTop: 45,
        el: weightRulerRef.value,
        currentValue: weightValue.value,
        maxValue: 200,
        minValue: 30,
        divide: 5,
        precision: 0.1,
        handleValue: (v: number) => {
            weightValue.value = v || 0

            if (navigator?.vibrate) {
                const now = Date.now()
                if (now - lastVibrationTime.value > VIBRATION_THRESHOLD) {
                    navigator?.vibrate([10, 10])
                    lastVibrationTime.value = now
                }
            }
        },
    })
}

function calculateBMI() {
    if (!heightValue.value || !weightValue.value) {
        return
    }
    const height = heightValue.value / 100
    const weight = weightValue.value
    bmi.value = Number((weight / (height * height)).toFixed(1))
}

async function getArchiveData() {
    const { closeLoading } = useLoading()
    try {
        const { results: archiveResults } = await useWrapFetch<BaseResponse<Archives>>('/user/preliminaryArchive')
        heightValue.value = Number((archiveResults as any)?.archiveHeight)
        weightValue.value = Number((archiveResults as any)?.archiveWeight)
    } catch (error) {
        console.error(error)
    } finally {
        closeLoading()
    }
}

async function initData() {
    await getArchiveData()
    renderHeightRule()
    renderWeightRule()
}

onMounted(() => {
    initData()
})

function handleCalculate() {
    isShowResult.value = false
    if (isFirstCalculate.value) {
        isFirstCalculate.value = false
    }

    calculateBMI()

    setTimeout(() => {
        isShowResult.value = true
    }, 1000)
}

const bmiMessage = computed(() => {
    if (!bmi.value) {
        return ''
    }
    return getBmiTips(bmi.value)
})
</script>

<template>
    <div class="flex flex-col gap-16px p-16px h-100vh">
        <div class="flex flex-col gap-4px items-center">
            <div class="text-#1D2129 text-18px font-600">选择身高体重</div>
            <div class="text-#868F9C text-12px font-400">填写真实信息，生成结果更准确哦</div>
        </div>

        <div class="flex w-full flex-col gap-8px items-center justify-center overflow-hidden">
            <div class="flex items-center relative w-full">
                <div class="text-#1D2129 text-16px absolute left-0 top-1/2 -translate-y-1/2 font-600">身高</div>
                <div class="flex-1 text-center flex items-center justify-center">
                    <span text="t-5 18px" mr-4px font-600>
                        {{ heightValue }}
                    </span>
                    <span text="t-5 10px" relative top-3px>
                        cm
                    </span>
                    <div class="i-custom:pen w-12px h-12px ml-4px relative bottom-3px" @click="isHeightDialogShow = true"></div>
                </div>
            </div>
            <div ref="heightRulerRef"></div>
        </div>

        <div class="flex w-full flex-col gap-8px items-center justify-center overflow-hidden">
            <div class="flex items-center relative w-full">
                <div class="text-#1D2129 text-16px absolute left-0 top-1/2 -translate-y-1/2 font-600">体重</div>
                <div class="flex-1 text-center flex items-center justify-center">
                    <span text="t-5 18px" mr-4px font-600>
                        {{ weightValue }}
                    </span>
                    <span text="t-5 10px" relative top-3px>
                        kg
                    </span>
                    <div class="i-custom:pen w-12px h-12px ml-4px relative bottom-3px" @click="isWeightDialogShow = true"></div>
                </div>
            </div>
            <div ref="weightRulerRef"></div>
        </div>

        <div v-if="!isFirstCalculate" class="flex justify-center">
            <van-button type="primary" round class="w-241px h-50px" @click="handleCalculate">
                重新计算
            </van-button>
        </div>

        <div
            v-show="isShowResult"
            class="flex flex-col flex-1 mt-16px p-16px gap-16px rd-lt-10px rd-rt-10px bg-gradient-to-b from-#B9FFF7 from-0% to-white to-21.89%"
        >
            <div class="flex justify-between items-center">
                <div class="text-#1D2229 text-15px font-600">BMI结果值</div>
                <div class="text-#00AC97 text-24px font-800 font-ddinpro">{{ bmi }}</div>
            </div>

            <div>
                <user-weight-graph :bmi="bmi" :show-description="false" />
            </div>

            <div class="text-#1D2229 text-14px">
                {{ bmiMessage }}
            </div>
        </div>

        <div v-if="isFirstCalculate" class="flex justify-center absolute bottom-86px left-0 w-full">
            <van-button type="primary" round class="w-241px h-50px" @click="handleCalculate">
                开始计算
            </van-button>
        </div>

        <edit-dialog
            v-model:show="isHeightDialogShow" title="设置身高" :value="heightValue" unit="厘米"
            @confirm="(value) => {
                heightValue = value
                renderHeightRule()
            }"
        />

        <edit-dialog
            v-model:show="isWeightDialogShow" title="设置体重" :value="weightValue" unit="千克"
            @confirm="(value) => {
                weightValue = value
                renderWeightRule()
            }"
        />

        <shared-full-loading v-if="!isFirstCalculate && !isShowResult" overlay />
    </div>
</template>
