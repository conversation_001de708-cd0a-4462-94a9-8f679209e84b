<script setup lang="ts">
import type { DefineComponent } from 'vue'

const profileForm = ref<ProfileForm>({
    height: 160,
    weight: 60,
    historyOfIllness: [],
    drugHistory: [],
    allergyHistory: [],
    familyHistory: [],
    label: [],
})

const forms: DefineComponent[] = []
const steps = ref(0)

const formComponents = import.meta.glob<{ default: DefineComponent }>('@/components/form/*.vue', { eager: true })

for (const path in formComponents) {
    forms.push(formComponents[path]!.default)
}

const percentage = computed(() => {
    return Number(((steps.value + 1) / forms.length).toFixed(2)) * 100
})

function handleNextOrFinish() {
    if (steps.value < forms.length - 1) {
        steps.value++
    } else {
        console.log('finish')
    }
}
</script>

<template>
    <div class="mx-40px pt-16px flex flex-col justify-between pb-40px h-full">
        <div>
            <h1 class="text-primary-6 font-500 text-center pb-16px">建立您的个人专属档案</h1>

            <keep-alive>
                <component :is="forms[steps]" v-model:form="profileForm" v-motion-pop />
            </keep-alive>
        </div>

        <div>
            <div class="flex justify-between">
                <van-button
                    class="bg-primary-1! h-32px! border-none! w-74px! text-primary-6!" :disabled="steps === 0" @click="() => {
                        if (steps > 0) steps--
                    }"
                >
                    上一步
                </van-button>

                <van-button
                    class="bg-primary-1! h-32px! border-none! w-74px! text-primary-6!" @click="handleNextOrFinish"
                >
                    {{ forms.length - 1 === steps ? '完成' : '下一步' }}
                </van-button>
            </div>

            <div mt-10px flex items-center gap-5px>
                <van-progress flex-1 :percentage="percentage" :show-pivot="false" />
                <div text-primary-6>
                    {{ `${percentage}%` }}
                </div>
            </div>
        </div>
    </div>
</template>
