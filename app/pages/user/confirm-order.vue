<script setup lang="ts">
useHead({
    title: '确认订单',
})
const isDeliver = ref(true)

const activeTabClass = 'h-full mt-0 bg-white text-primary-6'
const inactiveTabClass = 'mt-6px bg-fill-3 text-t-4'
const sharedTabClass = 'w-1/2 text-16px font-500 flex items-center justify-center rounded-t-4px'

const checkedAddressId = useSessionStorage('checkedAddressId', undefined)

const route = useRoute()
const reportId = route.query.reportId

const addressListRef = useTemplateRef('addressListRef')

async function handleToMiniProgram() {
    try {
        if (!checkedAddressId.value) {
            showToast('请选择地址')
            return
        }

        const wx = await useWxBridge({})

        const address = addressListRef.value?.currentAddress

        const dataToBase64 = encodeMessage({
            id: address?.id,
            name: address?.name,
            phone: address?.phone,
            province: address?.province,
            city: address?.city,
            district: address?.district,
            detail: address?.detail,
        })

        wx?.miniProgram.redirectTo({
            url: `/pages/order/pay?reportId=${reportId}&message=${dataToBase64}`,
        })
    } catch (error) {
        console.log(error)
        showToast('打开小程序失败')
    }
}

// if (!nutritionProgramId) {
//     showToast('缺少健康方案参数')
//     router.go(-1)
// }
// async function handlePay() {
//     if (!checkedAddressId.value) {
//         showToast('请选择地址')
//         return
//     }
//     const { results } = await useWrapFetch<BaseResponse<OrderStatus>>('/pay', {
//         method: 'post',
//         body: {
//             nutritionProgramId: route.query.nutritionProgramId,
//             addressId: checkedAddressId.value,
//         },
//     })

//     if (results === 'SUCCESS') {
//         showSuccessToast('下单成功')
//         router.go(-1)
//     }
// }
</script>

<template>
    <div flex="~ col" justify-between h-screen>
        <div p-16px>
            <!-- <div class="flex w-full h-38px">
                <div
                    :class="[isDeliver ? activeTabClass : inactiveTabClass, sharedTabClass]"
                    @click="isDeliver = true"
                >
                    寄送到家
                </div>
                <div
                    :class="[isDeliver ? inactiveTabClass : activeTabClass, sharedTabClass]"
                    @click="isDeliver = false"
                >
                    药房自取
                </div>
            </div> -->
            <div v-if="isDeliver" class="bg-white rd-4px p-16px rounded-b-4px h-[calc(100vh-130px)] scrollbar-hide overflow-auto">
                <!-- <div class="flex items-center text-t-5 text-16px font-500">
                    <span class="i-custom-location inline-block w-16px h-16px mr-4px text-t-4"></span>
                    请选择收货地址
                </div>
                <p class="pl-20px text-t-3 text-12px">准确填写收件地址，以便准时送达哦~</p> -->

                <user-address-list ref="addressListRef" v-model="checkedAddressId" />
            </div>
            <div v-else class="bg-white rd-4px p-16px pt-24px rounded-b-4px min-h-200px">
                暂不支持药房自取
            </div>
        </div>

        <div class="safe-area-height flex gap-24px justify-between items-center px-16px">
            <van-button round block type="primary" plain class="h-50px!" @click="navigateTo('/user/address/new')">
                新增收货地址
            </van-button>
            <van-button round block type="primary" class="h-50px!" @click="handleToMiniProgram">
                确定
            </van-button>
        </div>
    </div>
</template>

<style  scoped>
.safe-area-height {
    padding-bottom: calc(24px + constant(safe-area-inset-bottom));
    padding-bottom: calc(24px + env(safe-area-inset-bottom))
}
</style>
