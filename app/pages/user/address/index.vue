<script setup lang="ts">
useHead({
    title: '收货地址',
})
</script>

<template>
    <div p-16px>
        <div bg-white rd-4px p-16px>
            <p flex items-center gap-8px>
                <span text="16px t-5" font-500>王华</span>
                <span text="16px t-5" font-500>15256236232</span>

                <span bg="primary-1" text="12px primary-6" px-4px py-1px rd-2px>
                    默认
                </span>
            </p>

            <p>
                <span text="13px t-4">
                    浙江省杭州市余杭区良渚街道通运街368号良渚生命科技小镇6号楼1楼
                </span>
            </p>

            <div mt-16px flex justify-between>
                <van-radio>
                    <span>设为默认</span>
                </van-radio>

                <div flex gap-12px>
                    <div flex items-center>
                        <div class="i-custom-del w-10px h-10px mr-4px"></div>
                        <span text="12px t-4">
                            删除
                        </span>
                    </div>

                    <div flex items-center>
                        <div class="i-custom-pen w-10px h-10px mr-4px"></div>
                        <span text="12px t-4">
                            编辑
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
