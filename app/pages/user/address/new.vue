<script setup lang="ts">
import { areaList } from '@vant/area-data'
import { useForm } from 'slimeform'

import type { LocationQueryValue } from 'vue-router'
import type { AreaInstance } from 'vant'

useHead({ title: '新增收货地址' })

const router = useRouter()
const route = useRoute()

const isEdit = !!route.query.id

function parseRouteQuery(param: LocationQueryValue | LocationQueryValue[] | undefined) {
    if (Array.isArray(param)) {
        const ret = param.at(-1)
        return ret || ''
    }
    return param || ''
}

const { form, submitter, verify } = useForm({
    form: () => ({
        name: parseRouteQuery(route.query.name),
        phone: parseRouteQuery(route.query.phone),
        province: parseRouteQuery(route.query.province),
        city: parseRouteQuery(route.query.city),
        district: parseRouteQuery(route.query.district),
        detail: parseRouteQuery(route.query.detail),
        postalCode: parseRouteQuery(route.query.postalCode),
        defaultFlag: parseRouteQuery(route.query.defaultFlag) === '1',
    }),
    rule: {
        name: isRequired,
        phone: [
            isRequired,
            // eslint-disable-next-line regexp/no-unused-capturing-group, regexp/no-misleading-capturing-group
            v => /(?:(\+\d{1,4})[-.\s]?)?(?:\((\d{1,3})\)[-.\s]?)?(\d{1,4})[-.\s]?(\d{1,4})[-.\s]?(\d{1,9})/.test(v) || '请输入正确的手机号',
        ],
        province: isRequired,
        city: isRequired,
        district: isRequired,
        detail: isRequired,
    },
})

const showArea = ref(false)
const areaRef = ref<AreaInstance>()
function handleAreaConfirm(value: any) {
    showArea.value = false
    const [province, city, district] = value.selectedOptions
    form.province = province?.text
    form.city = city?.text
    form.district = district?.text
}

const { submitting, submit } = submitter(async () => {
    try {
        const body = { ...form, defaultFlag: form.defaultFlag ? 1 : 0 } as any
        if (isEdit) {
            body.id = Number(route.query.id)
        }
        const { results } = await useWrapFetch<BaseResponse<number>>('/v1/address', {
            method: isEdit ? 'PUT' : 'POST',
            body,
        })

        sessionStorage.setItem('checkedAddressId', String(results))

        showSuccessToast(isEdit ? '修改地址成功' : '新增地址成功')
        router.back()
    } catch {
        showFailToast(isEdit ? '修改地址失败' : '新增地址失败')
    }
})

function handleSave() {
    if (verify())
        submit()
    else
        showFailToast('请完善表单')
}
</script>

<template>
    <div py-16px flex="~ col" justify-between h-full>
        <div>
            <van-cell-group inset>
                <van-field v-model="form.name" class="original" label="姓名" placeholder="请输入真实姓名" />
                <van-field v-model="form.phone" class="original" label="手机号" placeholder="请填写收货人手机号" />
            </van-cell-group>

            <van-cell-group inset class="!mt-16px">
                <van-field :model-value="`${form.province ? `${form.province} ${form.city} ${form.district}` : '省市区县、乡镇等'} `" class="original" readonly is-link label="所在地区" placeholder="省市区县、乡镇等" @click="showArea = true" />
                <van-field v-model="form.detail" class="original" label="详细地址" placeholder="街道、楼牌号等" />
            </van-cell-group>

            <van-cell-group inset class="!mt-16px px-16px h-74px flex items-center">
                <van-checkbox id="defaultFlag" v-model="form.defaultFlag" size="20px" w-full h-full justify-between label-position="left">
                    <div flex justify-between items-center>
                        <p flex="~ col" space-y-2px>
                            <span text="16px t-5">
                                设为默认地址
                            </span>
                            <span text-t-3 text-14px>
                                下单时会优先使用该地址
                            </span>
                        </p>
                    </div>
                </van-checkbox>
            </van-cell-group>
        </div>

        <div px-40px>
            <van-button type="primary" block :loading="submitting" @click="handleSave">
                保存并使用
            </van-button>
        </div>

        <van-popup v-model:show="showArea" position="bottom">
            <van-area
                ref="areaRef"
                v-model="form.postalCode"
                :area-list="areaList"
                @cancel="showArea = false"
                @confirm="handleAreaConfirm"
            />
        </van-popup>
    </div>
</template>
