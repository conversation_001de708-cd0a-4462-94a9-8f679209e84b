<script setup lang="tsx">
import dayjs from 'dayjs'
import { v4 as uuidv4 } from 'uuid'

import { useDietPatterns } from '../../../composables/useDietPatterns'
import { useWrapFetch } from '../../../composables/useWrapFetch'
import { saveDietRecord } from '../../../utils/diet'
import DietPatternCard from '../../../components/user/checkin/diet-pattern/diet-pattern-card.vue'

interface Food {
    name: string
    weight: string
    calories: string
    foodType: string
    rawMaterial: string[]
    carbohydrates: string
    protein: string
    fat: string
    dietaryFiber: string
}

interface DietRecommendation {
    date: string
    totalCalories: number
    nutrition: Record<string, { current: string, goal: string }>
    meals: Array<{ mealType: string, foods: Food[], calories: string }>
    dietaryPatternName?: string
    meals_rn: { breakfast_id_list: number[], lunch_id_list: number[], dinner_id_list: number[], snack_id_list: number[] }
}

interface DietItem {
    title: string
    mealType: string
    icon: string
    totalCalories: string
    foods: Food[]
}

useHead({
    title: '饮食推荐',
})

const { diaryId } = storeToRefs(useDiaryStore())
const { getPatternById } = useDietPatterns()
const activePattern = computed(() => getPatternById(diaryId.value as PatternId))

const totalCalories = ref(0)
const mealObj = ref({})

const nutritionList = ref([
    {
        title: '碳水化合物',
        key: 'carbohydrates',
        current: 0,
        total: 0,
        unit: '克',
        gradient: {
            from: '#00AC97',
            to: '#52D2C2',
        },
    },
    {
        title: '脂肪',
        key: 'fat',
        current: 0,
        total: 0,
        unit: '克',
        gradient: {
            from: '#FFB348',
            to: '#FFD191',
        },
    },
    {
        title: '蛋白质',
        key: 'protein',
        current: 0,
        total: 0,
        unit: '克',
        gradient: {
            from: '#3DB5FF',
            to: '#7ECEFF',
        },
    },
    {
        title: '膳食纤维',
        key: 'dietaryFiber',
        current: 0,
        total: 0,
        unit: '克',
        gradient: {
            from: '#30DD4A',
            to: '#80F692',
        },
    },
])

const mealTypeMap: Record<DietType, string> = {
    breakfast: '早餐',
    lunch: '午餐',
    dinner: '晚餐',
    snack: '加餐',
}

const dietList = shallowRef<DietItem[]>([
    {
        title: '早餐',
        mealType: 'breakfast',
        icon: 'i-custom-recommend-breakfast',
        totalCalories: '0',
        foods: [],
    },
    {
        title: '午餐',
        mealType: 'lunch',
        icon: 'i-custom-recommend-lunch',
        totalCalories: '0',
        foods: [],
    },
    {
        title: '晚餐',
        mealType: 'dinner',
        icon: 'i-custom-recommend-dinner',
        totalCalories: '0',
        foods: [],
    },
    {
        title: '加餐',
        mealType: 'snack',
        icon: 'i-custom-recommend-add-meal',
        totalCalories: '0',
        foods: [],
    },
])

const recordedRecommendFoods = ref<string[]>([])
const loadingStates = ref<boolean[]>([false, false, false, false])

function handleDietRecommendation(results: DietRecommendation[]) {
    if (!results?.length) return

    const today = dayjs().format('YYYY-MM-DD')
    const todayResult = results.find(item => item.date === today)

    if (!todayResult) {
        return
    }

    const { totalCalories: totalCal, nutrition, meals, meals_rn } = todayResult

    mealObj.value = meals_rn

    nutritionList.value.forEach((item) => {
        const key = item.key as keyof typeof nutrition
        const nutritionItem = nutrition[key]
        if (nutritionItem) {
            item.current = Number(nutritionItem.current)
            item.total = Number(nutritionItem.goal)
        }
    })

    totalCalories.value = totalCal

    const updatedDietList = dietList.value.map((item) => {
        const meal = meals.find(m => mealTypeMap[m.mealType as DietType] === item.title)
        if (meal) {
            return {
                ...item,
                totalCalories: meal.calories,
                foods: meal.foods,
            }
        }
        return item
    })

    dietList.value = updatedDietList
}

async function switchFoods(mealIndex: number) {
    const meal = dietList.value[mealIndex]
    if (!meal?.mealType) return

    const { results } = await useWrapFetch<BaseResponse<DietRecommendation[]>>('/api/checkInCustomerMeal/replaceDailyDietaryRecommendation', { method: 'POST', body: {
        type: '0',
        date: dayjs().format('YYYY-MM-DD'),
        mealType: meal.mealType,
        meals_rn: mealObj.value,
    } })

    if (results) {
        handleDietRecommendation(results)
    }
}

function isMealRecorded(foods: Food[]) {
    if (!foods.length || !recordedRecommendFoods.value.length) return false
    return foods.every(food => recordedRecommendFoods.value.includes(food.name))
}

async function handleRecordToday(mealIndex: number) {
    const meal = dietList.value[mealIndex]
    if (!meal?.foods?.length) {
        return
    }

    if (isMealRecorded(meal.foods)) {
        return
    }

    loadingStates.value[mealIndex] = true
    try {
        const items = meal.foods
        const dietType = meal.mealType as DietType
        const foodItems = items.map((item: Food) => ({
            uuid: uuidv4().split('-')[0]!,
            ...item,
            weight: extractNumber(item.weight) || 0,
            calories: extractNumber(item.calories) || 0,
            carbohydrates: extractNumber(item.carbohydrates) || 0,
            protein: extractNumber(item.protein) || 0,
            fat: extractNumber(item.fat) || 0,
            dietaryFiber: extractNumber(item.dietaryFiber) || 0,
            mealPicture: '',
            source: 'recommend' as const,
        }))

        await saveDietRecord(foodItems, dietType)
        showSuccessToast('保存成功')

        recordedRecommendFoods.value.push(...items.map((item: Food) => item.name))
    } catch (error) {
        showFailToast('记录今日饮食失败')
    } finally {
        loadingStates.value[mealIndex] = false
    }
}

function getProgress(current: number, total: number) {
    return Number((current / total).toFixed(2))
}

async function getRecommendDiet() {
    const { closeLoading } = useLoading()
    try {
        const { results } = await useWrapFetch<BaseResponse<DietRecommendation[]>>('/checkInCustomerMeal/recommend', { method: 'POST' })
        if (results) {
            handleDietRecommendation(results)
        }
    } catch (error) {
        console.error(error)
    } finally {
        closeLoading()
    }
}

async function getCheckinFoodList() {
    const { closeLoading } = useLoading()
    try {
        const { results } = await useWrapFetch<BaseResponse<any[]>>('/checkInCustomerMeal/list', {
            method: 'POST',
            body: {
                checkInDate: dayjs().format('YYYY-MM-DD'),
            },
        })

        if (!results?.length) return []

        results.forEach((rItem) => {
            const mealContent = JSON.parse(rItem.mealContent)
            const recommendItems = mealContent.items.filter((item: any) => item.source === 'recommend')
            recordedRecommendFoods.value.push(...recommendItems.map((item: any) => item.name))
        })
    } catch (error) {
        console.error(error)
    } finally {
        closeLoading()
    }
}

onMounted(async () => {
    await Promise.allSettled([
        getRecommendDiet(),
        getCheckinFoodList(),
    ])
})
</script>

<template>
    <div class="relative p-16px bg-#F4F5F7">
        <div class="absolute top-0 left-0 w-full h-115px bg-gradient-to-b from-[#DBFFFB] to-[#F4F5F7] z-0"></div>
        <div class="relative w-full flex flex-col gap-16px z-2">
            <div class="w-full flex flex-col rd-10px py-10px px-16px gap-8px bg-white">
                <diet-pattern-card
                    v-if="activePattern"
                    :key="activePattern.title"
                    :pattern="activePattern"
                    :show-active-tag="false"
                    size="small"
                />
                <div class="flex items-center gap-8px h-40px">
                    <div class="flex-1 bg-#FFF7E8 rd-10px flex justify-center items-center py-8px text-13px text-#1D2229 font-400" @click="navigateTo('/user/checkin/diet-pattern')">
                        更换饮食模式
                    </div>
                    <div class="flex-1 bg-#E4FAF9 rd-10px flex justify-center items-center py-8px text-13px text-#1D2229 font-400" @click="navigateTo('/user/survey/dietary')">
                        修改饮食偏好
                    </div>
                </div>
                <div class="flex gap-8px">
                    <div class="w-117px flex flex-col justify-center items-center rd-10px bg-#E4F7F5">
                        <div class="text-32px text-#1D2229 font-600">{{ totalCalories || 0 }}</div>
                        <div class="text-12px text-#868F9C font-400">方案总热量</div>
                    </div>
                    <div class="flex flex-1 gap-8px">
                        <div class="flex-1 flex flex-col gap-6px">
                            <div v-for="item in nutritionList.slice(0, 2)" :key="item.title" class="flex flex-col gap-6px" :class="{ 'pb-8px': nutritionList.indexOf(item) === 0 }">
                                <div class="text-12px text-#1D2229 font-600">{{ item.title }}</div>
                                <div class="relative w-full h-6px rd-6px overflow-hidden">
                                    <div class="absolute inset-0 bg-#F2F4F7"></div>
                                    <div
                                        class="absolute inset-0 rd-6px" :style="{
                                            width: `${getProgress(item.current, item.total) * 100}%`,
                                            background: `linear-gradient(to right, ${item.gradient.from}, ${item.gradient.to})`,
                                        }"
                                    ></div>
                                </div>
                                <div class="text-10px text-#4E5969 font-400">{{ item.current }}/{{ item.total }}{{ item.unit }}</div>
                            </div>
                        </div>
                        <div class="flex-1 flex flex-col gap-6px">
                            <div v-for="item in nutritionList.slice(2, 4)" :key="item.title" class="flex flex-col gap-6px" :class="{ 'pb-8px': nutritionList.indexOf(item) === 2 }">
                                <div class="text-12px text-#1D2229 font-600">{{ item.title }}</div>
                                <div class="relative w-full h-6px rd-6px overflow-hidden">
                                    <div class="absolute inset-0 bg-#F2F4F7"></div>
                                    <div
                                        class="absolute inset-0 rd-6px" :style="{
                                            width: `${getProgress(item.current, item.total) * 100}%`,
                                            background: `linear-gradient(to right, ${item.gradient.from}, ${item.gradient.to})`,
                                        }"
                                    ></div>
                                </div>
                                <div class="text-10px text-#4E5969 font-400">{{ item.current }}/{{ item.total }}{{ item.unit }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-for="(meal, index) in dietList" :key="meal.title" class="w-full flex flex-col rd-10px py-12px px-16px gap-12px bg-white">
                <div class="w-full flex items-center justify-between">
                    <div class="flex gap-8px items-center">
                        <div :class="meal.icon" class="w-22px h-22px"></div>
                        <div class="text-15px text-#1D2229 font-600">{{ meal.title }}</div>
                        <div class="text-12px text-#868F9C font-400">{{ meal.totalCalories }}千卡</div>
                    </div>
                    <van-button
                        v-if="!isMealRecorded(meal.foods)"
                        plain
                        type="primary"
                        round
                        size="small"
                        class="w-70px"
                        @click="switchFoods(index)"
                    >
                        换一换
                    </van-button>
                </div>

                <template v-if="meal?.foods?.length">
                    <div v-for="(food, fIndex) in meal.foods" :key="food.name + fIndex" class="flex items-center justify-between">
                        <div class="flex gap-8px items-center">
                            <div class="flex flex-col gap-8px">
                                <div class="text-12px text-#1D2229 font-600">{{ food.name }}</div>
                                <div class="flex items-center gap-8px">
                                    <div class="text-11px text-#868F9C font-400">{{ food.weight }}</div>
                                </div>
                            </div>
                        </div>
                        <div class="text-12px text-#4E5969 font-400">{{ food.calories }}千卡</div>
                    </div>
                </template>
                <div v-else class="text-center text-12px text-#868F9C">暂无食物数据</div>

                <div
                    v-if="meal?.foods?.length"
                    class="w-full h-38px lh-38px flex items-center justify-center text-14px font-600 rd-100px gap-8px"
                    :class="[
                        isMealRecorded(meal.foods) || loadingStates[index]
                            ? 'bg-#E4FAF9 text-#868F9C cursor-not-allowed'
                            : 'bg-#F2F4F7 text-#33ac97 cursor-pointer',
                    ]"
                    @click="!loadingStates[index] && handleRecordToday(index)"
                >
                    <div class="w-15px h-15px" :class="isMealRecorded(meal.foods) ? 'i-custom-check-circle-fill' : 'i-custom-recommend-circle-plus'"></div>
                    {{ isMealRecorded(meal.foods) ? '已记入' : loadingStates[index] ? '记录中...' : `记入今日${meal.title}` }}
                </div>
            </div>
        </div>
    </div>
</template>
