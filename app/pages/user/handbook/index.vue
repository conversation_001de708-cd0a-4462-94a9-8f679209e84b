<script setup lang="ts">
import screening1 from '~/assets/images/handbook/screening/1.png'
import screening2 from '~/assets/images/handbook/screening/2.png'
import screening3 from '~/assets/images/handbook/screening/3.png'
import screening4 from '~/assets/images/handbook/screening/4.png'
import principle1 from '~/assets/images/handbook/principle/1.png'
import principle2 from '~/assets/images/handbook/principle/2.png'
import guide1 from '~/assets/images/handbook/guide/1.png'
import guide2 from '~/assets/images/handbook/guide/2.png'
import usage1 from '~/assets/images/handbook/usage/1.png'
import usage2 from '~/assets/images/handbook/usage/2.png'
import reference1 from '~/assets/images/handbook/reference/1.png'

definePageMeta({
    meta: {
        tabbar: false,
    },
})

const tabs = ref([
    { name: '认知与筛查', key: 'screening', images: [screening1, screening2, screening3, screening4] },
    { name: 'Li05干预原理', key: 'principle', images: [principle1, principle2] },
    { name: 'Li05干预指导篇', key: 'guide', images: [guide1, guide2] },
    { name: 'Li05干预使用篇', key: 'usage', images: [usage1, usage2] },
    { name: '参考文献', key: 'reference', images: [reference1] },
])

const activeTab = ref('screening')
const isSticky = ref(false)
const contentRef = ref<HTMLElement | null>(null)
const isImagesLoaded = ref(false)
const tabContainerRef = ref<HTMLElement | null>(null)

const managementPlans = ref([
    {
        title: 'Li05生活方式管理计划',
        key: 'fasting',
        duration: '（28天/84天/长期有效）',
        suitable: [
            '健康减重需求者',
            '健康生活探索者',
            '生活方式重塑者',
            '全面健康管理追求者',
        ],
        unsuitable: [
            '孕妇',
            '哺乳期妇女',
        ],
    },
    // {
    //     title: 'Li05轻脂瘦身管理计划',
    //     duration: '（14天/28天/84天）',
    //     suitable: [
    //         '超重/肥胖人群、体脂率高人群、亚健康人群（长期疲劳、熬夜、睡眠质量差、上班久坐、便秘、腹泻、免疫力低下、长期饮酒、血糖/血脂临界值）以及有其他减脂瘦身、健康减重需求的人群。',
    //     ],
    //     unsuitable: [
    //         '严重感染或有免疫缺陷的人群',
    //         '婴幼儿、孕妇、哺乳期妇女',
    //     ],
    // },
    {
        title: 'Li05降脂减重管理计划',
        key: 'lipid-reduction',
        duration: '（28天/84天）',
        suitable: [
            '超重/腹胖（内脏脂肪高、血脂异常）人群、代谢受损人群（血糖/血脂/血压升高、胰岛素抵抗、轻中度脂肪肝、代谢综合征）、轻中度肠道功能紊乱人群以及有其他体重管理需求的人群。',
        ],
        unsuitable: [
            '严重感染或有免疫缺陷的人群',
            '婴幼儿、孕妇、哺乳期妇女',
            '麸质过敏者',
        ],
    },
    {
        title: 'Li05糖脂代谢综合管理计划',
        key: 'metabolic-improvement',
        duration: '（28天/84天）',
        suitable: [
            '重度肥胖、重度脂肪肝、酒精性肝病、肝纤维化、高脂血症、2型糖尿病等糖脂代谢紊乱人群以及重度肠道功能紊乱人群，依据医生建议使用。',
        ],
        unsuitable: [
            '严重感染或有免疫缺陷的人群',
            '婴幼儿、孕妇、哺乳期妇女',
            '麸质过敏者',
        ],
    },
])

function loadImages(images: string[], onComplete: () => void) {
    isImagesLoaded.value = false
    if (images.length === 0) {
        isImagesLoaded.value = true
        onComplete()
        return
    }

    let loadedImages = 0

    // 检查每个图片的加载状态
    images.forEach((imageSrc) => {
        const img = new Image()
        img.onload = () => {
            loadedImages++
            if (loadedImages === images.length) {
                isImagesLoaded.value = true
                onComplete()
            }
        }
        img.onerror = () => {
            loadedImages++
            if (loadedImages === images.length) {
                isImagesLoaded.value = true
                onComplete()
            }
        }
        img.src = imageSrc
    })

    // 设置一个最大等待时间（5秒）
    setTimeout(() => {
        if (loadedImages < images.length) {
            isImagesLoaded.value = true
            onComplete()
        }
    }, 5000)
}

function setActiveTab(key: string) {
    const { closeLoading } = useLoading()
    activeTab.value = key

    nextTick(() => {
        const tabContainer = tabContainerRef.value
        const clickedTab = tabContainer?.querySelector(`button[data-tab="${key}"]`) as HTMLElement
        if (tabContainer && clickedTab) {
            const tabRect = clickedTab.getBoundingClientRect()
            const containerRect = tabContainer.getBoundingClientRect()
            const scrollLeft = tabContainer.scrollLeft + (tabRect.left + tabRect.width / 2 - (containerRect.left + containerRect.width / 2))
            tabContainer.scrollTo({
                left: scrollLeft,
                behavior: 'smooth',
            })
        }

        setTimeout(() => {
            if (contentRef.value) {
                const rect = contentRef.value.getBoundingClientRect()
                const scrollTop = window.pageYOffset + rect.top
                window.scrollTo({
                    top: scrollTop - 40,
                    behavior: 'smooth',
                })

                // 获取当前标签页的所有图片并加载
                const currentTab = tabs.value.find(tab => tab.key === key)
                if (currentTab) {
                    loadImages(currentTab.images, closeLoading)
                } else {
                    closeLoading()
                }
            } else {
                closeLoading()
            }
        }, 50)
    })
}

function handleScroll() {
    isSticky.value = window.scrollY > 0
}

async function handleCheckPlan(plan: any) {
    const wx = await useWxBridge({})

    wx?.miniProgram.navigateTo({
        url: `/pages/order/new-pay?planId=${plan.key}`,
    })
}

onMounted(() => {
    window.addEventListener('scroll', handleScroll)
    // 页面首次加载时，加载默认tab的图片
    const currentTab = tabs.value.find(tab => tab.key === activeTab.value)
    if (currentTab) {
        loadImages(currentTab.images, () => {})
    }
})

onUnmounted(() => {
    window.removeEventListener('scroll', handleScroll)
})
</script>

<template>
    <div class="relative bg-#F4F5F7 pb-16px bg-[url('~/assets/images/handbook/background.png'),url('~/assets/images/handbook/footer-background.png')] bg-no-repeat bg-[position:top,bottom] bg-[size:100%_545px,100%_640px] flex flex-col items-center">
        <div class="absolute w-356px h-320px -top-92px left-20px bg-[url('~/assets/images/handbook/Li05.svg')] bg-no-repeat bg-center bg-[length:100%_100%]"></div>

        <div class="sticky top-0 w-full mt-200px px-16px" :class="{ 'backdrop-blur-8px bg-white/50': isSticky }">
            <div ref="tabContainerRef" class="relative overflow-x-auto flex gap-16px [&::-webkit-scrollbar]:hidden [-ms-overflow-style:none] [scrollbar-width:none]">
                <button
                    v-for="(tab, index) in tabs"
                    :key="tab.key"
                    :data-tab="tab.key"
                    class="tab-button my-8px relative flex items-center justify-center transition-colors duration-200 whitespace-nowrap"
                    :class="[
                        activeTab === tab.key
                            ? 'text-[#00AC97] text-18px font-500 custom-underline'
                            : 'text-[#333333] text-13px',
                    ]"
                    @click="setActiveTab(tab.key)"
                >
                    <div v-if="index !== 0" class="absolute w-4px h-4px rounded-full bg-[#00AC97] left-[-10px] top-[50%] transform -translate-y-1/2"></div>
                    {{ tab.name }}
                </button>
            </div>
        </div>

        <div ref="contentRef" class="w-full px-16px">
            <div class="w-full">
                <div v-if="activeTab" class="w-full flex flex-col items-center gap-16px">
                    <img
                        v-for="(image, index) in tabs.find(tab => tab.key === activeTab)?.images"
                        :key="index"
                        :src="image"
                        class="w-full h-auto"
                    />

                    <div v-if="activeTab === 'screening' && isImagesLoaded" class="w-100% flex flex-col items-center rounded-10px overflow-hidden">
                        <div class="bg-#E3FBF6 w-100% flex h-60px justify-between items-center px-16px">
                            <div class="flex flex-col justify-center">
                                <div class="text-#8AD2C2 text-12px">Part.1 认知与筛查</div>
                                <div class="text-#333333 text-16px font-450">5. 创建适合你的体重管理计划</div>
                            </div>
                            <div class="w-38px h-42px bg-[url('~/assets/images/handbook/icon-question-mark.png')] bg-no-repeat bg-center bg-[length:100%_100%]"></div>
                        </div>

                        <div class="bg-white p-16px w-full flex flex-col gap-16px">
                            <div v-for="(plan, index) in managementPlans" :key="index" class="w-full flex flex-col gap-16px" @click="handleCheckPlan(plan)">
                                <div class="flex items-center">
                                    <span class="w-10px h-10px mr-4px inline-block bg-[url('~/assets/images/handbook/icon-play.svg')] bg-no-repeat bg-center bg-[length:100%_100%]"></span>
                                    <label class="text-#00AC97 font-450 text-12px">{{ plan.title }}</label>
                                    <label class="text-11px">{{ plan.duration }}</label>
                                </div>

                                <div class="w-full flex gap-2px pl-16px bg-white items-stretch" :class="[{ 'flex-col': plan.suitable.length === 1 }]">
                                    <div class="flex flex-col gap-2px flex-1">
                                        <div class="h-23px text-12px text-#00AC97 flex items-center justify-center bg-#00AC9726">✓ 适用人群</div>
                                        <div class="p-16px bg-#00FFE00D text-11px text-#333333 text-center h-full flex flex-col justify-center">
                                            <template v-for="(item, idx) in plan.suitable" :key="idx">
                                                {{ item }}<br v-if="idx !== plan.suitable.length - 1" />
                                            </template>
                                        </div>
                                    </div>

                                    <div class="flex flex-1 flex-col gap-2px">
                                        <div class="h-23px text-12px text-#FFA41F flex items-center justify-center bg-#FFA41F26">x 不适用人群</div>
                                        <div class="p-16px bg-#FFA41F0D text-11px text-#333333 text-center h-full flex flex-col justify-center">
                                            <template v-for="(item, idx) in plan.unsuitable" :key="idx">
                                                {{ item }}<br v-if="idx !== plan.unsuitable.length - 1" />
                                            </template>
                                        </div>
                                    </div>
                                </div>

                                <div class="w-full flex justify-center">
                                    <van-button plain type="primary" round size="small" class="flex justify-center w-110px">
                                        点击查看详情
                                        <span class="w-8px h-8px ml-4px inline-block bg-[url('~/assets/images/handbook/icon-click.svg')] bg-no-repeat bg-center bg-[length:100%_100%]"></span>
                                    </van-button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <shared-safe-buttom />
    </div>
</template>
