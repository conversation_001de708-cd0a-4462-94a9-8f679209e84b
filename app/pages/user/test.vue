<script setup lang="ts">
import { computed } from 'vue'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

const angle = ref(0)
const isDragging = ref(false)
const orbitRadius = 75
const arcLength = Math.PI * (7.6 / 12) // 8 hours arc
const endpointRadius = 12

// 添加计算属性来转换角度为时间
const currentTime = computed(() => {
    // 将角度转换为24小时制时间
    // angle 为 0 时对应 6 点，angle 为 1.5 时对应 12 点
    // 所以需要将角度值映射到 6-18 的范围
    const time = (angle.value / (Math.PI * 2)) * 24 + 6
    // 使用取模运算确保时间在 0-24 范围内
    const normalizedTime = time > 24 ? time - 24 : time
    const hours = Math.floor(normalizedTime)
    const minutes = Math.round((normalizedTime % 1) * 60)
    return {
        hours,
        minutes,
    }
})

function isOnOrbit(x: number, y: number) {
    const distance = Math.sqrt(x * x + y * y)
    const tolerance = 30 // 可接受的误差范围，根据实际体验调整
    return Math.abs(distance - orbitRadius) <= tolerance
}

function calculateAngle(event: MouseEvent | TouchEvent) {
    const container = (event.currentTarget as HTMLElement).closest('svg')
    if (!container) return

    const rect = container.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2

    let clientX, clientY
    if (event instanceof MouseEvent) {
        clientX = event.clientX
        clientY = event.clientY
    } else {
        const touch = event.touches[0]
        if (!touch) return
        clientX = touch.clientX
        clientY = touch.clientY
    }

    const deltaX = clientX - centerX
    const deltaY = clientY - centerY

    // 如果不在轨道上且不是正在拖动，则直接返回
    if (!isDragging.value && !isOnOrbit(deltaX, deltaY)) {
        return
    }

    let newAngle = Math.atan2(deltaY, deltaX)
    if (newAngle < 0) newAngle += 2 * Math.PI

    angle.value = newAngle
}

function startDrag(event: MouseEvent | TouchEvent) {
    const container = (event.currentTarget as HTMLElement).closest('svg')
    if (!container) return

    const rect = container.getBoundingClientRect()
    const centerX = rect.left + rect.width / 2
    const centerY = rect.top + rect.height / 2

    let clientX, clientY
    if (event instanceof MouseEvent) {
        clientX = event.clientX
        clientY = event.clientY
    } else {
        const touch = event.touches[0]
        if (!touch) return
        clientX = touch.clientX
        clientY = touch.clientY
    }

    const deltaX = clientX - centerX
    const deltaY = clientY - centerY

    // 只有在轨道上点击时才开始拖动
    if (isOnOrbit(deltaX, deltaY)) {
        isDragging.value = true
        calculateAngle(event)
    }
}

function onDrag(event: MouseEvent | TouchEvent) {
    if (!isDragging.value) return
    calculateAngle(event)
}

function endDrag() {
    isDragging.value = false
}

function getArcEndpoints() {
    const startAngle = angle.value
    const endAngle = angle.value + arcLength

    return {
        start: {
            x: orbitRadius * Math.cos(startAngle),
            y: orbitRadius * Math.sin(startAngle),
        },
        end: {
            x: orbitRadius * Math.cos(endAngle),
            y: orbitRadius * Math.sin(endAngle),
        },
    }
}

function getArcPath() {
    const { start, end } = getArcEndpoints()
    const largeArcFlag = 0
    const sweepFlag = 1

    return `M ${start.x} ${start.y} A ${orbitRadius} ${orbitRadius} 0 ${largeArcFlag} ${sweepFlag} ${end.x} ${end.y}`
}
</script>

<template>
    <div class="time-display">
        当前时间：{{ currentTime }}
    </div>
    <div
        class="orbit-container"
    >
        <svg
            width="100%"
            height="100%"
            :viewBox="`${-orbitRadius - 20} ${-orbitRadius - 20} ${orbitRadius * 2 + 40} ${orbitRadius * 2 + 40}`"
            @mousedown="startDrag"
            @mousemove="onDrag"
            @mouseup="endDrag"
            @mouseleave="endDrag"
            @touchstart="startDrag"
            @touchmove="onDrag"
            @touchend="endDrag"
        >
            <!-- Orbit Circle -->
            <circle
                :r="orbitRadius"
                cx="0"
                cy="0"
                fill="none"
                stroke="#F2F4F7"
                :style="{
                    strokeWidth: '12px',
                }"
            />

            <circle
                r="60"
                cx="0"
                cy="0"
                fill="#E4FAF9"
            />

            <!-- Moving Arc -->
            <path
                :d="getArcPath()"
                fill="none"
                stroke="#00AC97"
                :style="{
                    strokeWidth: '12px',
                }"
                stroke-linecap="round"
            />

            <!-- Endpoint Circles -->
            <circle
                :cx="getArcEndpoints().start.x"
                :cy="getArcEndpoints().start.y"
                :r="endpointRadius"
                fill="#00AC97"
            />
            <circle
                :cx="getArcEndpoints().end.x"
                :cy="getArcEndpoints().end.y"
                :r="endpointRadius"
                fill="#00AC97"
            />
            <text
                :x="getArcEndpoints().start.x - 5"
                :y="getArcEndpoints().start.y + 3"
                fill="#fff"
                :style="{
                    fontSize: '12px',
                }"
            >
                起
            </text>
            <text
                :x="getArcEndpoints().end.x - 5"
                :y="getArcEndpoints().end.y + 4"
                fill="#fff"
                :style="{
                    fontSize: '12px',
                }"
            >
                终
            </text>
            <rect
                x="0"
                y="-55"
                fill="#00AC97"
                :style="{
                    width: '1px',
                    height: '3px',
                }"
            />
            <text
                x="-7"
                y="-37"
                fill="#4E5969"
                :style="{
                    fontSize: '12px',
                    fontWeight: '600',
                }"
            >
                24
            </text>

            <rect
                x="0"
                y="51"
                fill="#00AC97"
                :style="{
                    width: '1px',
                    height: '3px',
                }"
            />
            <text
                x="-6"
                y="45"
                fill="#4E5969"
                :style="{
                    fontSize: '12px',
                    fontWeight: '600',
                }"
            >
                12
            </text>

            <rect
                x="52"
                y="0"
                fill="#00AC97"
                :style="{
                    width: '3px',
                    height: '1px',
                }"
            />
            <text
                x="40"
                y="5"
                fill="#4E5969"
                :style="{
                    fontSize: '12px',
                    fontWeight: '600',
                }"
            >
                6
            </text>

            <rect
                x="-55"
                y="0"
                fill="#00AC97"
                :style="{
                    width: '3px',
                    height: '1px',
                }"
            />
            <text
                x="-49"
                y="5"
                fill="#4E5969"
                :style="{
                    fontSize: '12px',
                    fontWeight: '600',
                }"
            >
                18
            </text>
        </svg>
    </div>
</template>

<style scoped>
.orbit-container {
  display: flex;
  justify-content: center;
  align-items: center;
  touch-action: none;
  user-select: none;
  width: 200px;
  height: 200px;
}
</style>
