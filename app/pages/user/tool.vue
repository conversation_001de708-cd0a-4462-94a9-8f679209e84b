<script setup lang="ts">
import jjjc from '@/assets/images/service/jjjc.png'
import bgjd from '@/assets/images/service/bgjd.png'
import jzbd from '@/assets/images/service/jzbd.png'
import ysms from '@/assets/images/service/ysms.png'
import zfgcp from '@/assets/images/service/zfgcp.png'
import gxwhcp from '@/assets/images/service/gxwhcp.png'

definePageMeta({
    meta: {
        tabbar: true,
        layout: {
            customBg: 'bg-#F4F5F7',
        },
    },
})

const { noPay } = storeToRefs(useUserStateStore())

const banners = computed(() => {
    const handbookOrKnowledge = noPay.value
        ? {
                title: '知识中心',
                icon: jzbd,
                url: '/user/knowledge',
            }
        : {
                title: '减重宝典',
                icon: jzbd,
                url: '/user/handbook',
            }

    return [
        {
            title: '居家检测',
            icon: jjjc,
            url: '',
        },
        {
            title: '报告解读',
            icon: bgjd,
            url: '',
        },
        handbookOrKnowledge,
        {
            title: '饮食模式',
            icon: ysms,
            url: '/user/checkin/diet-pattern',
        },
    ]
})

const hotTools = [
    {
        title: 'BMI计算器',
        icon: 'i-custom:tools-bmijsq',
        url: '/user/tools/bmi',
    },
    {
        title: '标准体重',
        icon: 'i-custom:tools-bztzcx',
        url: '/user/tools/standard-weight',
    },
    {
        title: '代谢评估',
        icon: 'i-custom:tools-jcdxpg',
        url: '/user/tools/basal-metabolism',
    },
    {
        title: '腰臀比评估',
        icon: 'i-custom:tools-ytbpg',
        url: '/user/tools/waist-to-hip',
    },
    {
        title: '食物查询',
        icon: 'i-custom:tools-swcx',
        url: '/user/tools/food-search',
    },
    {
        title: '运动消耗',
        icon: 'i-custom:tools-ydxh',
        iconSize: 'w-22px h-22px m-5px',
        url: '/user/tools/sport-consume',
    },
]

const chronicDiseaseAssessments = [
    {
        title: '脂肪肝',
        subtitle: '适用人群为12-85岁',
        src: zfgcp,
        url: '/user/tools/liver-fat',
    },
    {
        title: '肝纤维化',
        subtitle: '非酒精性脂肪性肝病纤维化评分',
        src: gxwhcp,
        url: '/user/tools/liver-fibrosis',
    },
]

function handleNavigate(url: string) {
    if (url) {
        navigateTo(url)
    } else {
        showFailToast('敬请期待')
    }
}
</script>

<template>
    <div
        class="min-h-100vh px-12px pt-16px pb-64px flex flex-col gap-12px overflow-auto"
        style="background: linear-gradient(180deg, #C9FFFA 0%, #F4F5F7 100%) 0 0/100% 259px no-repeat, #F4F5F7;"
    >
        <div class="flex h-73px">
            <div
                v-for="item in banners" :key="item.title"
                class="flex flex-1 flex-col justify-between items-center"
                @click="handleNavigate(item?.url)"
            >
                <img :src="item.icon" class="w-48px h-48px" />
                <div class="text-14px text-#1D2229 font-600">{{ item.title }}</div>
            </div>
        </div>

        <div
            class="flex flex-col gap-8px rd-10px bg-white p-12px"
            style="box-shadow: 0px 3px 3.2px 0px #00000021;"
        >
            <div class="text-14px font-600 text-#1D2229 vertical-bar h-27px lh-27px">慢病评估</div>
            <div class="flex flex-col gap-8px">
                <div
                    v-for="item in chronicDiseaseAssessments"
                    :key="item.title"
                    class="w-327px h-127px rd-10px pt-12px pl-16px pr-10px pb-16px flex justify-between items-center relative"
                    style="background: linear-gradient(180deg, #E5FCFF 0%, #F2FEFE 100%);box-shadow: 0px 3px 3.5px 0px #00AC973B inset;"
                >
                    <div class="flex flex-col justify-between h-full">
                        <div class="flex flex-col h-59px">
                            <div class="flex items-center text-22px font-600 text-#1D2229 !h-40px lh-40px">
                                <div class="text-#00AC97">{{ item.title }}</div>
                                测评
                            </div>
                            <div class="text-12px text-#333333 font-light border-[0.5px] border-#333333 !h-19px lh-19px px-2px">
                                {{ item.subtitle }}
                            </div>
                        </div>
                        <van-button
                            type="primary" round
                            class="!h-26px !text-14px !w-90px"
                            style="box-shadow: 1.5px 1.5px 1.6px 0px #A0C8D2;"
                            @click="handleNavigate(item.url)"
                        >
                            立即评估
                        </van-button>
                    </div>
                    <img :src="item.src" class="w-96px h-94px absolute right-8px bottom-10px" />
                </div>
            </div>
        </div>

        <div
            class="flex flex-col gap-8px rd-10px bg-white p-12px"
            style="box-shadow: 0px 3px 3.2px 0px #00000021;"
        >
            <div class="text-14px font-600 text-#1D2229 vertical-bar h-27px lh-27px">热门工具</div>
            <div class="grid grid-cols-4">
                <div
                    v-for="item in hotTools"
                    :key="item.title"
                    class="flex flex-col items-center justify-between cursor-pointer h-61px"
                    @click="handleNavigate(item.url)"
                >
                    <div class="w-40px h-40px" :class="[item.icon]"></div>
                    <div class="text-12px text-#1D2229 text-center">{{ item.title }}</div>
                </div>
            </div>
        </div>
    </div>
</template>
