<script setup lang="ts">
useHead({
    title: '设置',
})

const { reset } = useUserStore()

async function handleLogout() {
    reset()

    const wx = await useWxBridge({})

    const dataToBase64 = encodeMessage({
        type: 'user:logout',
        data: 'logout',
    })

    wx?.miniProgram.redirectTo({
        url: `/pages/index/index?message=${dataToBase64}`,
    })
}

async function handleMessageSubscribe() {
    const wx = await useWxBridge({})

    wx?.miniProgram.navigateTo({
        url: '/pages/subscribe/index',
    })
}
</script>

<template>
    <div class="py-16px flex flex-col justify-between h-full">
        <div>
            <van-cell-group inset>
                <van-cell title="个人资料" is-link to="/user/profile" />
                <van-cell title="消息通知" is-link @click="handleMessageSubscribe" />
            </van-cell-group>

            <van-cell-group inset class="mt-16px!">
                <van-cell title="用户服务协议" is-link to="/agreements/user" />
                <van-cell title="隐私政策" is-link to="/agreements/privacy" />
            </van-cell-group>
        </div>

        <div class="flex justify-center items-center">
            <van-button type="primary" plain round class="!w-207px !h-50px !text-15px" @click="handleLogout">退出登录</van-button>
        </div>
    </div>
</template>
