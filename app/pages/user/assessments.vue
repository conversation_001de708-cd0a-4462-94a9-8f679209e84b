<script setup lang="ts">
const { data, status } = useAPI<QuestionList[]>('/user/question/list')
</script>

<template>
    <base-suspense :status="status">
        <div p-16px space-y-16px>
            <shared-assessment-list-item
                v-for="item in data?.results"
                :key="item.id"
                :image="formatResource(item.questionCoverPicture)"
                :title="item.questionTitle"
                :participants-avatar="['https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg', 'https://fastly.jsdelivr.net/npm/@vant/assets/cat.jpeg']"
                :question-data="item"
            />
        </div>
    </base-suspense>
</template>
