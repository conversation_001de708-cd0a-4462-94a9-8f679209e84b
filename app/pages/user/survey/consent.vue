<script setup lang="ts">
import destr from 'destr'

import type { AsyncDataRequestStatus } from '#app'

const route = useRoute()
let surveyId = route.query.surveyId as string
const resultId = route.query.resultId as string
const interpretationId = route.query.interpretationId as string
const questionType = route.query.type as string
const register = route.query.register as string | undefined
definePageMeta({
    meta: {
        layout: {
            customBg: 'background-gradient',
        },
    },
})

useHead({
    title: '开始评估',
})

const checked = ref(false)

const data = ref<QuestionMeta>()
const status = ref<AsyncDataRequestStatus>('pending')

async function getSurvey() {
    if (register) {
        const state = sessionStorage.getItem('state')
        const { results } = await useWrapFetch<BaseResponse<QuestionMeta>>('/user/question', {
            params: {
                state,
            },
        })

        surveyId = String(results.id)

        data.value = results
        status.value = 'success'
    } else {
        const { results } = await useWrapFetch<BaseResponse<QuestionMeta>>(`/question/get/${surveyId}`)

        data.value = results
        status.value = 'success'
    }
}

getSurvey()

const metaData = computed(() => {
    if (!data.value)
        return

    const title = data.value?.title
    const content = destr<Record<string, any>>(data.value?.content)

    const counts = content.questionCount
    const completionTime = content.completionTime

    return {
        title,
        counts,
        completionTime,
    }
})

const linkToUrl = computed(() => {
    if (register) {
        if (Number(surveyId) === EXTERNAL_PROCESS_ID) {
            return '/external-user-register'
        }

        return '/user-register'
    }

    return `/user/survey/submit?surveyId=${surveyId}&resultId=${resultId}&type=${questionType}&interpretationId=${interpretationId}`
})
</script>

<template>
    <base-suspense :status>
        <div px-32px flex="~ col" justify-between h-full>
            <div mt-46px>
                <div v-if="Number(surveyId) === EXTERNAL_PROCESS_ID" font-700 text-32px class="font-dinpro" text="primary-6">
                    <span block leading-43px>Weight Management</span>
                    <span block leading-43px>Assessment</span>
                    <span block leading-43px>Questionnaire</span>
                </div>

                <div v-else font-700 text-36px class="font-dinpro" text="primary-6">
                    <span block leading-43px>Fatty Liver</span>
                    <span block leading-43px>Assessment</span>
                    <span block leading-43px>Questionnaire</span>
                </div>

                <div text-24px font-500 mt-4px :class="Number(surveyId) === EXTERNAL_PROCESS_ID ? 'w-200px' : 'w-180px'" text="#1D2229">
                    {{ metaData?.title }}
                </div>

                <div h-5px w-40px my-16px rd-5px bg="#1D2229"></div>

                <div text="16px t-5" tracking-0.1em>
                    <div>
                        您将获得：
                    </div>

                    <div v-if="Number(surveyId) === EXTERNAL_PROCESS_ID">
                        ① 通过测评了解自己的<span class="custom-underline">健康状况</span>
                        <br />
                        ② 识别并管理健康风险因素
                        <br />
                        ③ 针对自身健康状况的<span class="custom-underline">体重管理方案</span>
                        <br />
                        <div class="text-16px font-600 mt-40px">参考指南：</div>
                        <span text="t-3 12px">
                            <v-balancer class="!leading-20px">
                                《体重管理指导原则(2024年版)》
                                <br />
                                《肥胖症诊疗指南(2024年版)》
                            </v-balancer>
                        </span>
                    </div>

                    <div v-else>
                        ① 通过测评了解自己的<span class="custom-underline">健康状况</span>
                        <br />
                        ② 识别并管理健康风险因素
                        <br />
                        ③ 针对自身健康状况，给出对应的<span class="custom-underline">健康指导</span><span class="custom-underline">建议</span>
                        <br />
                        <div class="text-16px font-600 mt-40px">参考指南：</div>
                        <span text="t-3 12px">
                            <v-balancer class="!leading-20px">
                                EASL–EASD–EASO Clinical Practice Guidelines on the
                                management of metabolic dysfunction-associated steatotic
                                liver disease (MASLD)
                            </v-balancer>
                        </span>
                    </div>
                </div>
            </div>

            <div flex="~ col" items-center>
                <van-checkbox v-model="checked" mb-8px icon-size="18px">
                    <div text="12px center t-5">
                        我已阅读并同意《
                        <nuxt-link to="/agreements/privacy">
                            <span class="text-primary-6">SLMC隐私协议</span>
                        </nuxt-link>
                        》
                    </div>
                </van-checkbox>

                <nuxt-link :replace="!register" :to="linkToUrl">
                    <van-button round class="!w-295px" :disabled="!checked" type="primary" block>开始评估</van-button>
                </nuxt-link>

                <span text="11px t-3 center" mt-12px>
                    当前评估主要面向成年人，未成年人测评结果仅供参考<br />测评结果不能代替医生诊断
                </span>
            </div>
        </div>
    </base-suspense>
</template>

<style lang="scss">
.background-gradient {
    background: linear-gradient(180deg, #F2FFFD 0%, #FFFFFF 100%);
}
</style>
