<script setup lang="ts">
import type { AsyncDataRequestStatus } from '#app'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

const route = useRoute()
const id = route.query.id

useHead({
    title: '生成结果中',
})

const status = ref<AsyncDataRequestStatus>('pending')

const { data } = await useAPI<EvaluateQuestion>(`/user/getSecondaryEvaluationByReportInterpretationId/${id}`)

onMounted(() => {
    setTimeout(() => {
        status.value = 'success'
        useHead({
            title: '评估完成',
        })
    }, 2500)
})
</script>

<template>
    <base-suspense type="square" :status="status">
        <div flex="~ col" gap-20px h-full relative>
            <div absolute top-20px ml-16px class="w-[calc(100%-32px)]">
                <div bg-white p-16px rd-4px>
                    <div flex justify-center>
                        <div class="i-custom-finished-2 h-73px w-73px">
                        </div>
                    </div>

                    <div flex items-center gap-4px mt-16px>
                        <div w-4px h-16px bg-primary-6></div>
                        <div text-t-5>
                            第一阶段评估已完成
                        </div>
                    </div>

                    <div text-t-5 font-500 text-16px mt-4px>
                        脂肪肝健康管理评估
                    </div>
                </div>

                <div text-t-5 font-500 rd-25px style="box-shadow: 0px 4px 12px 0px rgba(0, 64, 119, 0.06);" bg-white p-16px>
                    评估进度

                    <div bg-fill-3 h-1px my-10px w-full>
                    </div>

                    <div relative>
                        <div bg-primary-1 h-16px w-full rd-10px>
                        </div>

                        <div text-9px text-white text-right pr-10px bg-primary-6 h-16px rd-10px absolute leading-16px top-0 left-0 class="w-50%">
                            50%
                        </div>
                    </div>

                    <div flex justify-between mt-5px>
                        <span text-t-4 text-11px font-400>
                            初步评估
                        </span>

                        <span text-t-4 text-11px font-400>
                            深度评估
                        </span>
                    </div>

                    <div bg="#00AC971A" px-16px py-8px rd-10px font-400 mt-8px backdrop-blur-4px>
                        <div flex items-center>
                            <span text-t-5 text-12px>预计</span>
                            <span text-primary-6 text-16px mx-4px>2</span>
                            <span text-t-5 text-12px>分钟</span>
                        </div>

                        <div text="10px t-3">
                            在初步评估基础上对您的身体做详细了解
                        </div>
                    </div>
                </div>

                <div text="t-3 10px center" mt-10px>
                    初步评估已完成，可直接查看结果，深度评估将出具详细报告！！
                </div>
            </div>

            <div absolute bottom-20px ml-32px class="w-[calc(100%-64px)]">
                <div flex mt-50px gap-20px>
                    <nuxt-link replace :to="`/user/assessment/${id}/evaluate`" w-full>
                        <van-button round class="h-44px !bg-primary-1 !text-primary-6 !border-none" type="primary" block>查看结果</van-button>
                    </nuxt-link>

                    <nuxt-link replace w-full :to="`/user/survey/submit?surveyId=${data?.results.secondaryEvaluationQuestionId}&resultId=${data?.results.secondaryEvaluationQuestionResultId}&type=SECONDARY_EVALUATION&interpretationId=${id}`">
                        <van-button round type="primary" block>深度评估</van-button>
                    </nuxt-link>
                </div>
            </div>
        </div>
    </base-suspense>
</template>

<style lang="scss" scoped>
.background-gradient {
    background: linear-gradient(180deg, #DAFFFB 0%, #FFFFFF 100%);
}
</style>
