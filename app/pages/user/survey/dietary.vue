<script setup lang="ts">
import dietaryContent from '@/assets/json/dietary.json'

const content = JSON.stringify(dietaryContent)
const surveyRef = useTemplateRef('surveyRef')

const router = useRouter()
async function handleSubmit(data: Record<string, string>) {
    const { results } = await useWrapFetch<BaseResponse<boolean>>('/user/saveDietaryQuestionnaire', {
        method: 'post',
        body: {
            questionResult: JSON.stringify(data),
            questionTitle: dietaryContent.title,
        },
    })

    if (results) {
        showSuccessToast('提交成功')
        router.go(-1)
    }
}

async function getSurvey() {
    const { closeLoading } = useLoading()
    try {
        const { results } = await useWrapFetch<BaseResponse<{
            questionResult: string
        }>>('/user/getDietaryQuestionnaire')

        if (results) {
            surveyRef.value?.renderSurvey(results.questionResult)
        } else {
            surveyRef.value?.renderSurvey()
        }
    } catch (error) {
        console.error(error)
    } finally {
        closeLoading()
    }
}

onMounted(() => {
    getSurvey()
})
</script>

<template>
    <div h-full>
        <shared-survey ref="surveyRef" :content @submit="handleSubmit" />
    </div>
</template>
