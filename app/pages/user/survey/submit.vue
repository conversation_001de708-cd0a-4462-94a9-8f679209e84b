<script setup lang="ts">
import { showConfirmDialog } from 'vant'

const content = ref('')

const route = useRoute()
const surveyId = route.query.surveyId as string
// const resultId = route.query.resultId as string
const surveyRef = useTemplateRef('surveyRef')
const surveyResult = ref('')
const questionType = route.query.type as QuestionType

let interpretationId: number | undefined
let resultId: number | undefined

// const interpretationId = route.query.interpretationId === 'null' ? null : route.query.interpretationId

if (Number(route.query.interpretationId)) {
    interpretationId = Number(route.query.interpretationId)
}

if (Number(route.query.resultId)) {
    resultId = Number(route.query.resultId)
}

async function init() {
    const { closeLoading } = useLoading()
    try {
        const { results } = await useWrapFetch<BaseResponse<QuestionMeta>>(`/question/get/${surveyId}`)
        content.value = results.content

        // 深度评估 判断是否展示饮酒相关题目
        if (questionType === 'SECONDARY_EVALUATION') {
            const { results: evaluateQuestion } = await useWrapFetch<BaseResponse<EvaluateQuestion>>(`/user/getSecondaryEvaluationByReportInterpretationId/${interpretationId}`)

            const firstQuestionResultId = evaluateQuestion.preliminaryEvaluationQuestionResultId

            const { results: firstQuestionResult } = await useWrapFetch<BaseResponse<QuestionResult>>(`/question-result/${firstQuestionResultId}`)

            const parsedResult = JSON.parse(firstQuestionResult.questionResult)

            // 不饮酒
            if (parsedResult['12'] !== '1') {
                // 删除饮酒相关题目
                const _content = JSON.parse(content.value)
                _content.pages.splice(2, 1)
                content.value = JSON.stringify(_content)
            }
        }

        const { results: archiveResults } = await useWrapFetch<BaseResponse<Archives>>('/user/preliminaryArchive')

        const questionResult: Record<string, any> = {}

        if (archiveResults) {
            if (archiveResults.archiveHeight) {
                questionResult.height = Number(archiveResults.archiveHeight)
            }

            if (archiveResults.archiveWeight) {
                questionResult.weight = Number(archiveResults.archiveWeight)
            }

            if (archiveResults.waist) {
                questionResult.waist = Number(archiveResults.waist)
            }
        }

        // if (surveyId && Number.isInteger(Number(resultId))) {
        //     const { results: _surveyResult } = await useWrapFetch<BaseResponse<QuestionResult>>(`/question-result/${resultId}`)
        //     surveyResult.value = _surveyResult.questionResult
        // }

        await nextTick()

        surveyRef.value?.renderSurvey(Object.keys(questionResult).length > 0 ? JSON.stringify(questionResult) : '')
    } catch (error) {
        console.log(error)
    } finally {
        closeLoading()
    }
}

init()

async function handleSubmit(data: Record<string, string>) {
    try {
        // const _resultId = resultId === 'null' ? undefined : resultId
        // let resultInterpretationId = interpretationId

        if (resultId) {
            // 更新问卷
            const { results } = await useWrapFetch<BaseResponse<string>>('/user/saveQuestionAgain', {
                method: 'post',
                body: {
                    questionResult: JSON.stringify(data),
                    questionTitle: JSON.parse(content.value).title,
                    questionId: Number(surveyId),
                    reportInterpretationId: interpretationId,
                    type: questionType,
                },
            })

            if (results) {
                showSuccessToast('提交成功')
            }
        } else {
            // 首次保存问卷
            const { results } = await useWrapFetch<BaseResponse<string>>('/user/saveFusionQuestionnaireResult', {
                method: 'post',
                body: {
                    questionId: Number(surveyId),
                    questionResult: JSON.stringify(data),
                    questionTitle: JSON.parse(content.value).title,
                    reportInterpretationId: interpretationId,
                },
            })

            if (results) {
                interpretationId = Number(results)
                showSuccessToast('提交成功')
            }
        }

        // 保存档案内容
        if (questionType === 'PRELIMINARY_EVALUATION') {
            const { userInfo, token: _token, role } = storeToRefs(useUserStore())

            const archiveData: Record<string, string> = {}

            if (data['1']) {
                archiveData.sex = data['1'] === '1' ? '男' : '女'
                userInfo.value!.gender = data['1'] === '1' ? '男' : '女'
            }

            if (data['2']) {
                archiveData.age = data['2']
                userInfo.value!.age = Number(data['2'])
            }

            if (data['3'] || data.height) {
                archiveData.archiveHeight = data['3'] || data.height || ''
            }

            if (data['4'] || data.weight) {
                archiveData.archiveWeight = data['4'] || data.weight || ''
            }

            archiveData.waist = data.waist || ''

            archiveData.fatRate = data.fat_rate || ''

            if (data['5'] || data.bmi) {
                archiveData.archiveBmi = data['5'] || data.bmi || ''
            }

            if (data.age) {
                archiveData.age = data.age
                userInfo.value!.age = Number(data.age)
            }

            if (data.gender) {
                archiveData.sex = data.gender
                userInfo.value!.gender = data.gender
            }

            await useWrapFetch<BaseResponse<boolean>>('/user/preliminaryArchive', {
                method: 'post',
                body: archiveData,
            })
        }

        if (Number(surveyId) === EXTERNAL_PROCESS_ID) {
            navigateTo('/user/external/report-uploader', { replace: true })
        } else {
            navigateTo(`/user/assessment/${interpretationId}/evaluate?type=${questionType}`, { replace: true })
        }
    } catch (error) {
        console.log(error)
    }
}

onBeforeRouteLeave(async (to, from, next) => {
    if (to.name === 'user-assessment-interpretationId-evaluate'
        && ((to.query.type === 'SECONDARY_EVALUATION' && questionType === 'SECONDARY_EVALUATION')
            || (to.query.type === 'PRELIMINARY_EVALUATION' && questionType === 'PRELIMINARY_EVALUATION'))
    ) {
        next()
        return
    }

    if (to.name === 'user-external-report-uploader') {
        next()
        return
    }

    try {
        await showConfirmDialog({
            title: '离开评估',
            message: '我们强烈推荐您一次性完成本次评估，获得更完整的体验',
            closeOnPopstate: false,
            confirmButtonText: '继续评估',
            cancelButtonText: '确认返回',
        })

        next(false)
    } catch (error) {
        next()
    }
})
</script>

<template>
    <div h-full>
        <shared-survey ref="surveyRef" :content @submit="handleSubmit" />
    </div>
</template>
