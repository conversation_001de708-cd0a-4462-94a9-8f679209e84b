<script setup lang="ts">
import sportContent from '@/assets/json/sport.json'

const content = JSON.stringify(sportContent)
const surveyRef = useTemplateRef('surveyRef')

const router = useRouter()
async function handleSubmit(data: Record<string, string>) {
    const { results } = await useWrapFetch<BaseResponse<boolean>>('/user/saveSportQuestionnaire', {
        method: 'post',
        body: {
            questionResult: JSON.stringify(data),
            questionTitle: sportContent.title,
        },
    })

    if (results) {
        showSuccessToast('提交成功')
        if (!localStorage.getItem('isSetSportPreference')) {
            localStorage.setItem('isSetSportPreference', '1')
        }
        await router.replace({ path: '/user/recommend/sport', query: { from: 'surveySport' } })
    }
}

async function getSurvey() {
    const { closeLoading } = useLoading()
    try {
        const { results } = await useWrapFetch<BaseResponse<{
            questionResult: string
        }>>('/user/getSportQuestionnaire')

        if (results) {
            surveyRef.value?.renderSurvey(results.questionResult)
        } else {
            surveyRef.value?.renderSurvey()
        }
    } catch (error) {
        console.error(error)
    } finally {
        closeLoading()
    }
}

onMounted(() => {
    getSurvey()
})
</script>

<template>
    <div h-full>
        <shared-survey ref="surveyRef" :content @submit="handleSubmit" />
    </div>
</template>
