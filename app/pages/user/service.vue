<script setup lang="ts">
definePageMeta({
    meta: {
        tabbar: true,
        layout: {
            customBg: 'bg-#F4F5F7',
        },
    },
})

const { noPay } = storeToRefs(useUserStateStore())

const { data, status } = useAPI<QuestionList[]>('/user/question/list')
</script>

<template>
    <user-service-no-pay v-if="noPay" :data="data?.results ?? []" :status="status" />
    <user-service-pay v-else :data="data?.results ?? []" :status="status" />
</template>
