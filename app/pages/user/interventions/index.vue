<script setup lang="ts">
useHead({
    title: '我的干预',
})

const { data, status } = useAPI<NutritionProgram[]>(`/nutrition-program/user/getByToken`)
</script>

<template>
    <base-suspense :status>
        <div v-if="data?.results.length" p-16px>
            <nuxt-link v-for="program in data?.results" :key="program.nutritionProgramId" :to="`/user/interventions/${program.nutritionProgramId}`">
                <div rd-4px p-16px bg-white>
                    <div>
                        <span text="t-5 16px" font-500>
                            {{ program.projectName }}
                        </span>

                        <div overflow-x-scroll class="scrollbar-hide" flex flex-1 gap-10px mt-5px>
                            <van-image
                                v-for="(product) in program.productContent"
                                :key="product.projectProductId"
                                class="w-70px h-70px flex-shrink-0 rd-4px overflow-hidden"
                                fit="cover" :src="formatResource(product.pictureId)"
                            />
                        </div>
                    </div>
                </div>
            </nuxt-link>
        </div>

        <van-empty v-else description="暂无干预项目" />
    </base-suspense>
</template>
