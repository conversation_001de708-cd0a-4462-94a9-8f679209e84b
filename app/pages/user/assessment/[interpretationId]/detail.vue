<script setup lang="ts">
import { useRouteQuery } from '@vueuse/router'

const route = useRoute()
// const _active = route.query.active ? Number(route.query.active) : 0

const active = useRouteQuery<string>('active')

const reportDetail = ref<ReportInterpretation>()
const interpretationId = route.params.interpretationId

async function getReport() {
    const { closeLoading } = useLoading()
    try {
        const { results } = await useWrapFetch<BaseResponse<ReportInterpretation>>(`/report-interpretation/detail/${interpretationId}`)
        reportDetail.value = results
    } catch (error) {
        closeLoading()
    } finally {
        closeLoading()
    }
}

getReport()
</script>

<template>
    <van-tabs v-if="reportDetail" v-model:active="active">
        <van-tab name="0" title="填写记录">
            <div class="h-[calc(100vh-50px)] overflow-auto">
                <manager-survey-detail-results :report-detail="reportDetail" />
            </div>
        </van-tab>
        <van-tab name="1" title="初步评估">
            <manager-survey-detail-reports show-level :report-detail="reportDetail" />
        </van-tab>
        <van-tab name="2" title="临床检查">
            <manager-survey-detail-clinical-examination />
        </van-tab>
        <van-tab name="3" title="临床检验">
            <manager-survey-detail-clinical-examination />
        </van-tab>
        <van-tab name="4" title="健康解读">
            <user-assessment-interpretation :report-detail="reportDetail" @refresh="getReport" />
        </van-tab>
        <van-tab name="5" title="健康方案">
            <user-assessment-nutrition-plan :report-detail="reportDetail" />
        </van-tab>
    </van-tabs>

    <shared-full-loading v-else />
</template>

<style lang="scss" scoped>
:deep(.vue-pdf-embed) {
    --uno: h-full;
}
</style>
