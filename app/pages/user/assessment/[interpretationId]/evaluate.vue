<script setup lang="ts">
import Girl from '@/assets/images/assessment/girl.svg'
import Boy from '@/assets/images/assessment/boy.svg'
import { useSurveyDetail } from '~/utils/survey/useSurveyDetail'

useHead({
    title: '评估报告',
})

const { userInfo } = useUserStore()

const peopleBg = ref('')

const route = useRoute()
const interpretationId = route.params.interpretationId as string
const questionType = route.query.type as QuestionType

const bmi = ref<number | null>(null)

const { data: initData, status } = await useAsyncData('init', async () => {
    const [evaluateQuestion, firstEvaluateResult, secondaryEvaluationReport] = await Promise.all([
        useWrapFetch<BaseResponse<EvaluateQuestion>>(`/user/getSecondaryEvaluationByReportInterpretationId/${interpretationId}`),
        useWrapFetch<BaseResponse<EvaluateResult>>(`/user/getHealthReportByReportInterpretationId/${interpretationId}`),
        useWrapFetch<BaseResponse<EvaluateResult>>(`/user/getSecondaryEvaluationReportByReportInterpretationId/${interpretationId}`),
    ])

    const [preliminary, secondary] = await Promise.all([
        useSurveyDetail(evaluateQuestion.results.preliminaryEvaluationQuestionId, evaluateQuestion.results.preliminaryEvaluationQuestionResultId, 'preliminary'),
        useSurveyDetail(evaluateQuestion.results.secondaryEvaluationQuestionId, evaluateQuestion.results.secondaryEvaluationQuestionResultId, 'secondary'),
    ])

    const gender = preliminary.find(item => item.title?.trim() === '性别')?.result
    const weight = preliminary.find(item => item.type?.trim() === 'addon-weight')?.result
    const height = preliminary.find(item => item.type?.trim() === 'addon-height')?.result

    peopleBg.value = gender === 'male' ? `url(${Boy})` : `url(${Girl})`

    bmi.value = Number.parseFloat(preliminary.find(item => item.specialType === 'bmi')?.result || '0')

    const suggestions = [
        ...preliminary.filter(item => item.title === '是否饮酒' && item.suggestion),
        ...secondary.filter(item => item.suggestion),
    ]

    return {
        evaluateQuestion,
        firstEvaluateResult,
        suggestions,
        secondaryEvaluationReport,
        gender,
        weight,
        height,
    }
})

const isSecondary = computed(() => {
    if (questionType === 'PRELIMINARY_EVALUATION')
        return false

    else if (questionType === 'SECONDARY_EVALUATION')
        return true

    else
        return !!initData.value?.evaluateQuestion.results.secondaryEvaluationQuestionResultId
})

const formatedBmi = computed(() => {
    let fat = ''
    const bmiValue = bmi.value ? Number.parseFloat(bmi.value.toString()) : 0
    if (bmiValue < 18.5) {
        fat = '低体重状态'
    } else if (bmiValue >= 18.5 && bmiValue < 24) {
        fat = '正常体重'
    } else if (bmiValue >= 24 && bmiValue < 28) {
        fat = '体重超重'
    } else if (bmiValue >= 28 && bmiValue < 32.5) {
        fat = '轻度肥胖症'
    } else if (bmiValue >= 32.5 && bmiValue < 37.5) {
        fat = '中度肥胖症'
    } else if (bmiValue >= 37.5 && bmiValue < 50) {
        fat = '重度肥胖症'
    } else if (bmiValue >= 50) {
        fat = '极重度肥胖症'
    }
    return {
        value: bmi.value ? Number.parseFloat(bmi.value.toString()).toFixed(1) : '0',
        fat,
    }
})
</script>

<template>
    <base-suspense :status>
        <div class="relative scrollbar-hide overflow-auto h-screen">
            <div class="evaluate-bg pl-24px pt-32px">
                <p text-t-4 text-12px>
                    hi~，{{ userInfo?.name }}
                </p>

                <p text-t-5 text-20px font-600 leading-36px>
                    {{ isSecondary ? '评估报告已完成' : '初步评估已完成' }}
                </p>

                <div v-if="isSecondary" flex items-baseline gap-6px text-t-5>
                    <span text-12px>BMI 指数</span>
                    <span text-24px>
                        {{ formatedBmi.value }}
                    </span>

                    <span text-12px>
                        {{ formatedBmi.fat }}
                    </span>
                </div>
            </div>

            <img v-if="!isSecondary" src="@/assets/images/assessment/first-bg.png" alt="" srcset="" absolute top-15px right-24px w-82px h-84px />
            <div v-else class="people-bg"></div>

            <user-evaluate-second v-if="isSecondary" :interpretation-id="interpretationId" :init-data="initData" />

            <user-evaluate-first v-else :interpretation-id="interpretationId" :formated-bmi :init-data="initData" />
        </div>

        <user-sign-overlay />
    </base-suspense>
</template>

<style scoped>
.evaluate-bg {
    background: linear-gradient(180deg, rgba(0, 172, 151, 0.12) 0%, rgba(0, 172, 151, 0) 100%);
    height: 187px;
    width: 100%;
}

.people-bg {
    background-image: v-bind(peopleBg);
    background-size: 100% 100%;
    height: 220px;
    width: 76px;
    position: absolute;
    z-index: 10;
    top: 24px;
    right: 24px;
}
</style>
