<script setup lang="ts">
useHead({
    title: '评估记录',
})

const allMenus = [
    {
        name: '评估报告',
        key: 'evaluate',
        icon: 'i-custom-user-menu-1',
    },
    {
        name: '报告解读',
        key: 'analyse',
        icon: 'i-custom-user-menu-2',
    },
    {
        name: '健康方案',
        key: 'plan',
        icon: 'i-custom-user-menu-3',
    },
    {
        name: '填写记录',
        key: 'records',
        icon: 'i-custom-user-menu-4',
    },
    {
        name: '临床检查',
        key: 'examine',
        icon: 'i-custom-user-menu-5',
    },
    {
        name: '检验指标',
        key: 'inspection',
        icon: 'i-custom-user-menu-6',
    },
]

const route = useRoute()
const interpretationId = route.params.interpretationId as string
const { results } = await useWrapFetch<BaseResponse<EvaluateQuestion>>(`/user/getSecondaryEvaluationByReportInterpretationId/${interpretationId}`)

// const { results: evaluateDetail } = await useWrapFetch<BaseResponse<EvaluateResult>>(`/user/getHealthReportByReportInterpretationId/${interpretationId}`)

// function dynamicBgClass(riskLevelOfFattyLiver: string) {
//     return {
//         'bg-success-6': riskLevelOfFattyLiver === '低风险',
//         'bg-warning-6': riskLevelOfFattyLiver === '中风险',
//         'bg-danger-6': riskLevelOfFattyLiver === '高风险',
//     }
// }
</script>

<template>
    <div class="evaluate-bg relative">
        <div rd-10px p-16px>
            <img absolute top-18px right-16px z-100 src="@/assets/images/background/doctor.png" w-113px h-174px alt="" srcset="" />

            <div flex items-baseline mt-16px>
                <div w-150px text="20px t-5" font-600>
                    代谢相关脂肪性肝病风险评估
                </div>

                <!-- <div :class="dynamicBgClass(evaluateDetail.riskLevelOfFattyLiver)" text-10px w-39px relative bottom-5px h-18px text-center rd-2px leading-18px text-white>
                    {{ evaluateDetail.riskLevelOfFattyLiver }}
                </div> -->
            </div>

            <div relative rd-10px p-16px pt-12px style="background: linear-gradient(131.89deg, rgba(194, 201, 214, 0.14) 8.47%, rgba(194, 201, 214, 0.08) 88.34%),linear-gradient(75.11deg, rgba(255, 255, 255, 0) 64.36%, rgba(0, 255, 194, 0.2) 100%);">
                <div flex items-center gap-8px>
                    <div text="t-4 14px">
                        初步评估
                    </div>

                    <div class="i-custom-finished-3 w-16px h-16px"></div>
                </div>

                <div flex items-center gap-8px>
                    <div text="t-4 14px">
                        深度评估
                    </div>

                    <div v-if="!!results.secondaryEvaluationQuestionResultId" class="i-custom-finished-3 w-16px h-16px"></div>
                </div>
            </div>

            <div mt-16px mb-36px>
                <nuxt-link :to="`/user/survey/submit?surveyId=${results.secondaryEvaluationQuestionId}&resultId=${results.secondaryEvaluationQuestionResultId}&type=SECONDARY_EVALUATION&interpretationId=${interpretationId}`">
                    <van-button type="primary" :plain="!!results.secondaryEvaluationQuestionResultId" round class="!w-90px !h-37px !mr-14px">
                        深度评估
                    </van-button>
                </nuxt-link>

                <nuxt-link :to="`/user/survey/consent?surveyId=${results.preliminaryEvaluationQuestionId}&resultId=${results.preliminaryEvaluationQuestionResultId}&type=PRELIMINARY_EVALUATION&interpretationId=${interpretationId}`">
                    <van-button type="primary" plain round class="!w-90px !h-37px">
                        重新开始
                    </van-button>
                </nuxt-link>
            </div>

            <div flex items-center gap-4px mb-16px>
                <div class="i-custom-entrance w-17px h-19px"></div>
                <div text="t-5 16px" font-600>
                    快捷入口
                </div>
            </div>

            <div flex flex-wrap justify-between gap-16px>
                <nuxt-link
                    v-for="menu in allMenus" :key="menu.key"
                    :to="`/user/assessment/${interpretationId}/${menu.key}`"
                    flex flex-col items-center justify-center
                    gap-8px w-103px h-114px rd-10px p-16px bg-white
                >
                    <div w-32px h-32px rd-10px bg-white flex items-center justify-center>
                        <div :class="menu.icon" class="w-56px h-56px flex-shrink-0"></div>
                    </div>
                    <div mt-8px text-t-4>
                        {{ menu.name }}
                    </div>
                </nuxt-link>
            </div>
        </div>
    </div>
</template>

<style scoped>
.card-bg {
    background: linear-gradient(131.89deg, rgba(194, 201, 214, 0.14) 8.47%, rgba(194, 201, 214, 0.08) 88.34%),
    linear-gradient(75.11deg, rgba(255, 255, 255, 0) 64.36%, rgba(0, 255, 194, 0.2) 100%);
    --uno: px-16px py-12px rd-10px;
}

.evaluate-bg {
    background: linear-gradient(180deg, rgba(0, 172, 151, 0.12) 0%, rgba(0, 172, 151, 0) 100%);
    height: 187px;
    width: 100%;
}
</style>
