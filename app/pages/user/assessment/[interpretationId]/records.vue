<script setup lang="ts">
import { type SurveyDetail, useSurveyDetail } from '~/utils/survey/useSurveyDetail'
import BMI from '@/components/user/bmi-line.vue'
import Ethanol from '@/components/user/ethanol.vue'

import type { AsyncDataRequestStatus } from '#app'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white overflow-y-auto',
        },
    },
})

const status = ref<AsyncDataRequestStatus>('pending')
const route = useRoute()
const interpretationId = route.params.interpretationId as string

const surveyList = ref<SurveyDetail[]>([])

async function init() {
    try {
        const { results } = await useWrapFetch<BaseResponse<EvaluateQuestion>>(`/user/getSecondaryEvaluationByReportInterpretationId/${interpretationId}`)

        const preliminary = await useSurveyDetail(results.preliminaryEvaluationQuestionId, results.preliminaryEvaluationQuestionResultId, 'preliminary')

        let secondary: SurveyDetail[] = []
        if (results.secondaryEvaluationQuestionResultId) {
            secondary = await useSurveyDetail(results.secondaryEvaluationQuestionId, results.secondaryEvaluationQuestionResultId, 'secondary')
        }

        surveyList.value = [...(preliminary || []), ...(secondary || [])]
    } catch (error) {
        console.error(error)
    } finally {
        status.value = 'success'
    }
}

function formatResult(result: string) {
    if (Number(result)) {
        return Number.parseFloat(result).toFixed(1)
    }

    return result
}

function formatTitle(title: string) {
    if (title === '选择身高体重') {
        return '身高'
    }

    return title
}

onMounted(() => {
    init()
})

const componentMap: Record<string, any> = {
    bmi: BMI,
    ethanol: Ethanol,
}
</script>

<template>
    <base-suspense :status="status">
        <div p-16px flex flex-col gap-16px>
            <div v-for="(item, index) in surveyList" :key="index" bg-fill-1 rd-10px px-16px py-8px mt-4px>
                <p text="13px t-4">
                    {{ index + 1 }}. {{ formatTitle(item.title) }}
                </p>
                <component :is="componentMap[item.specialType!]" v-if="item.specialType" only-show-line :value="item.result" :root-bmi="item.result" />

                <p v-else text="15px primary-6" font-500 my-4px>
                    {{ item.title === ' 年龄' ? item.result : formatResult(item.result) }} {{ item.unit }}
                </p>

                <div v-if="item.suggestion">
                    <div flex items-center gap-4px>
                        <div class="i-custom-dot-orange w-22px h-14px"></div>

                        <p text="13px t-5" font-600>
                            解读及建议
                        </p>
                    </div>

                    <div text="12px t-4" mt-4px>
                        <span v-if="item.comment" block v-html="item.comment"></span>
                        <span v-if="item.suggestion" block v-html="item.suggestion"></span>
                    </div>
                </div>
            </div>
        </div>
    </base-suspense>
</template>
