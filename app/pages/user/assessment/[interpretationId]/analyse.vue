<script setup lang="ts">
import { Swiper, SwiperSlide } from 'swiper/vue'
import 'swiper/css'

useHead({
    title: '报告解读',
})

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

const route = useRoute()
// const interpretationId = route.params.interpretationId as string

// const { data, status } = await useAPI<EvaluateResult>(`/user/getHealthReportByReportInterpretationId/${interpretationId}`)

const { bmi: bmiData } = useBmiTips()

const bmiResult = computed(() => {
    let bmi = 0

    if (bmiData.value) {
        bmi = Number.parseFloat(bmiData.value.toString())
    }

    let offset: string = ((bmi / 37) * 100).toFixed(1)

    if (Number(offset) > 100) {
        offset = '100'
    }

    if (bmi < 18.5) {
        return {
            value: bmi.toFixed(1),
            tag: '偏低 ↓',
            offset,
        }
    }

    if (bmi >= 24) {
        return {
            value: bmi.toFixed(1),
            tag: '偏高 ↑',
            offset,
        }
    }

    return {
        value: bmi.toFixed(1),
        tag: '',
        offset,
    }
})

const currentSlide = ref(0)

function onSlideChange(v: any) {
    currentSlide.value = v.activeIndex
}
</script>

<template>
    <div pb-20px>
        <div h-240px pt-10px px-16px style="background: linear-gradient(180deg, rgba(0, 172, 151, 0.12) 0%, rgba(0, 172, 151, 0) 100%);">
            <div flex justify-between mt-13px>
                <div>
                    <div font-600 text="24px #333">
                        查看您的<br />肝脏健康度报告
                    </div>

                    <div text="10px #C2C9D6" mt-10px>
                        此报告根据文件评估结果智能生成
                    </div>
                </div>

                <img src="@/assets/images/background/doctor-2.png" w-135px h-130px alt="" srcset="" />
            </div>

            <div
                style="
                  background: linear-gradient(69.53deg, rgba(255, 255, 255, 0) 59.41%, rgba(255, 212, 0, 0.2) 98.11%), #ffffff;
                  box-shadow: 0px 6px 10px 0px #F4F6F6;
                  background-repeat: no-repeat;
                  background-position: top right;
                  background-size: 60% 60%;
                "
                rd-20px p-16px relative bottom-15px
            >
                <div rd-full w-16px h-16px bg="#FFD400" absolute top-18px right-17px></div>
                <div text="16px #333" font-600>
                    单纯性脂肪肝-轻度
                </div>

                <div line-clamp-2 text="12px t-4" mt-5px>
                    单纯性脂肪肝是一种常见的肝脏疾病。主要是由于各种原因导致肝细胞内脂肪堆积过多，但尚未引起肝脏炎症和纤维化。其发病与不良饮食习惯、缺乏运动、肥胖、酗酒等因素有关。
                    一般来说，单纯性脂肪肝通常没有明显症状，但如果不加以控制，可能会进展为脂肪性肝炎、肝纤维化甚至肝硬化。
                    应及时调整生活方式，如合理饮食、增加运动、控制体重、避免饮酒等，以逆转脂肪肝，保护肝脏健康。
                </div>
            </div>
        </div>

        <div px-16px mt-16px>
            <div flex justify-between items-center mb-16px>
                <div text="15px #333" font-600>
                    评估结果
                </div>

                <base-dots :count="2" :current="currentSlide" />
            </div>

            <swiper @slide-change="onSlideChange">
                <swiper-slide>
                    <div flex flex-wrap justify-between gap-y-10px>
                        <user-result-card type="bmi">
                            <template #tag>
                                <span v-if="bmiResult.tag" text="12px danger-6" font-600>{{ bmiResult.tag }}</span>
                            </template>

                            <template #result>
                                <user-inspections-bmi :bmi-result />
                            </template>
                        </user-result-card>
                        <user-result-card type="bloodFat">
                            <!-- <template #result>
                                    <user-inspections-blood-fat />
                                </template> -->
                        </user-result-card>
                        <user-result-card type="bloodPressure">
                            <!-- <template #result>
                                    <user-inspections-blood-pressure />
                                </template> -->
                        </user-result-card>
                        <user-result-card type="acid">
                            <!-- <template #result>
                                    <user-inspections-acid />
                                </template> -->
                        </user-result-card>
                    </div>
                </swiper-slide>
                <swiper-slide>
                    <div flex flex-wrap justify-between gap-13px>
                        <user-result-card type="artery">
                            <template #result>
                                <!-- <user-inspections-artery /> -->
                            </template>
                        </user-result-card>
                        <user-result-card type="bloodSugar" />
                        <user-result-card type="bloodAcid" />
                        <user-result-card type="cap" />
                    </div>
                </swiper-slide>
            </swiper>

            <div flex justify-between items-center mt-20px mb-16px>
                <div text="15px #333" font-600>
                    健康建议
                </div>
            </div>

            <div flex justify-around>
                <div flex flex-col items-center>
                    <div class="i-custom-suggestion-liver-hardness w-36px h-36px"></div>
                    <div text="10px t-4 center" w-50px>
                        建议进行<br />肝硬度筛查
                    </div>
                </div>
                <div flex flex-col items-center>
                    <div class="i-custom-suggestion-intestinal w-36px h-36px"></div>
                    <div text="10px t-4 center" w-80px>
                        建议进行<br />肠道微生态检测
                    </div>
                </div>
                <div flex flex-col items-center>
                    <div class="i-custom-suggestion-life-style w-36px h-36px"></div>
                    <div text="10px t-4 center" w-80px>
                        建议调整<br />生活方式
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
