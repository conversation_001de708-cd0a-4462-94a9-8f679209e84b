<script setup lang="ts">
const mode = ref<'view' | 'edit'>('view')

const { userInfo } = storeToRefs(useUserStore())

const { reset } = useUserStore()
const profile = ref<HostProfileForm>({ ...userInfo.value! })

function handleEdit() {
    mode.value = 'edit'
    profile.value = { ...userInfo.value! }
}

function handleCancel() {
    mode.value = 'view'
    profile.value = { ...userInfo.value! }
}

const profileRef = useTemplateRef('profileRef')

async function handleSave() {
    const { closeLoading } = useLoading({
        message: '保存中',
    })

    try {
        if (!useProfileFormValidate(profile.value))
            return

        let _headImgUrl = ''
        if (profileRef.value?.imgList.length) {
            const formData = new FormData()
            formData.append('file', profileRef.value.imgList[0]!.file!)

            const { results: headImgUrl } = await useWrapFetch<BaseResponse<string>>('/v1/file/upload', {
                body: formData,
                method: 'post',
            })

            _headImgUrl = headImgUrl
        }

        if (_headImgUrl) {
            profile.value.headImage = _headImgUrl
        }

        const params: Record<string, string> = {
            name: profile.value.name,
        }

        if (_headImgUrl) {
            params.headImgUrl = _headImgUrl
        }

        await useWrapFetch<BaseResponse<boolean>>('/user/update-info', {
            params,
            method: 'get',
        })

        if (profile.value.idCard) {
            await useWrapFetch<BaseResponse<string>>('/user/rewIdCard', {
                method: 'PUT',
                params: {
                    idCard: profile.value.idCard,
                },
            })
        }

        userInfo.value = {
            ...profile.value,
        }

        mode.value = 'view'
        showSuccessToast('保存成功')
    } catch (error) {
    } finally {
        closeLoading()
    }
}

async function handleLogout() {
    reset()

    const wx = await useWxBridge({})

    const dataToBase64 = encodeMessage({
        type: 'user:logout',
        data: 'logout',
    })

    wx?.miniProgram.redirectTo({
        url: `/pages/index/index?message=${dataToBase64}`,
    })
}
</script>

<template>
    <div h-full overflow-auto p-16px pb-0>
        <div class="h-full bg-white p-16px flex flex-col">
            <manager-profile ref="profileRef" v-model:mode="mode" v-model:profile="profile" />

            <div class="pb-40px flex justify-center space-x-24px">
                <template v-if="mode === 'view'">
                    <div flex gap-16px w-full items-center justify-center>
                        <van-button round type="primary" class="!w-165px !h-50px" block @click="handleEdit">修改信息</van-button>
                        <!-- <van-button round class="!w-165px" block @click="handleLogout">退出登录</van-button> -->
                    </div>
                </template>
                <template v-else>
                    <div flex gap-16px>
                        <van-button round type="primary" class="w-132px" @click="handleSave">保存</van-button>
                        <van-button round class="w-132px bg-primary-1! text-primary-6! border-none!" @click="handleCancel">取消</van-button>
                    </div>
                </template>
            </div>
        </div>
    </div>
</template>
