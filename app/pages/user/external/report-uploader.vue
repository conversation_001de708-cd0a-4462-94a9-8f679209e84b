<script setup lang="ts">
import { showImagePreview } from 'vant'
import { useRouter } from 'vue-router'

import type { UploaderBeforeRead, UploaderFileListItem } from 'vant/es/uploader/types'

const MAX_PICTURES = 5

const router = useRouter()
useHead({
    title: '上传报告',
})

const reportPictures = ref<UploaderFileListItem[]>([])
const uploadedFileIds = ref<string[]>([])

const hasUploadedPictures = computed(() => reportPictures.value.length > 0)

async function uploadFile(file: File) {
    const { closeLoading } = useLoading({
        message: '上传中...',
    })
    try {
        const formData = new FormData()
        formData.append('file', file)

        const { results } = await useWrapFetch<BaseResponse<string>>('/v1/file/upload', {
            body: formData,
            method: 'post',
            headers: {
                Accept: 'application/json',
            },
        })
        if (results) {
            uploadedFileIds.value.push(results)
        }
        return formatResource(results)
    } catch (error) {
        return ''
    } finally {
        closeLoading()
    }
}

async function handleBeforeRead(file: File | File[]): Promise<File | File[] | undefined> {
    try {
        if (!file || (Array.isArray(file) && file.length === 0)) {
            showToast('无效的文件')
            return undefined
        }

        const files = Array.isArray(file) ? file : [file]

        if (reportPictures.value.length + files.length > MAX_PICTURES) {
            showToast(`最多只能添加${MAX_PICTURES}张图片`)
            return undefined
        }

        for (const f of files) {
            if (!f || !(f instanceof File)) {
                showToast('无效的文件')
                continue
            }

            const fileMbSize = f.size / 1024 / 1024

            if (fileMbSize > 10) {
                showToast('图片大小不能超过10MB')
                return undefined
            }

            try {
                let _file = f
                if (fileMbSize >= 3.8) {
                    _file = await compressImage(f)
                }
                const url = await uploadFile(_file)
                if (url) {
                    reportPictures.value.push({ url })
                }
            } catch (error) {
                showToast(`处理文件 ${f.name} 失败`)
                return undefined
            }
        }

        return file
    } catch (error) {
        showToast('上传图片失败')
        return undefined
    }
}

function handleGenerateReport(isUploaded: boolean) {
    if (isUploaded) {
        if (uploadedFileIds.value.length === 0) {
            showToast('请先上传报告图片')
            return
        }
        router.push({
            path: '/user/external/report-generate',
            query: {
                fileIds: uploadedFileIds.value,
            },
        })
    } else {
        router.push({
            path: '/user/external/report-generate',
            query: {
                skipRequest: 1,
            },
        })
    }
}

function handlePreview(file: UploaderFileListItem) {
    showImagePreview([file.url!])
}

function handleDelete(file: UploaderFileListItem) {
    const index = reportPictures.value.findIndex(item => item.url === file.url)
    if (index > -1) {
        uploadedFileIds.value.splice(index, 1)
    }
    reportPictures.value = reportPictures.value.filter(item => item.url !== file.url)
}
</script>

<template>
    <div class="relative min-h-[100vh] p-16px bg-[url('~/assets/images/external/bg-report-uploader.svg')] bg-no-repeat bg-top bg-[length:100%_269px] flex flex-col items-center bg-#F4F5F7">
        <div class="w-full h-68px absolute top-16x bg-[url('~/assets/images/external/bg-report-uploader-text.svg')] bg-no-repeat bg-center bg-[length:100%_100%]"></div>
        <div class="flex flex-col w-100% h-fit bg-white rounded-10px p-16px gap-8px mt-80px">
            <van-uploader
                :class="!hasUploadedPictures ? 'my-uploader' : ''"
                :model-value="reportPictures"
                multiple
                :max-count="MAX_PICTURES"
                preview-size="68"
                :before-read="(handleBeforeRead as UploaderBeforeRead)"
                @preview="handlePreview"
                @delete="handleDelete"
            >
                <div
                    class="w-68px h-68px bg-#F2F4F7 border-1px border-#E5E7EB rd-10px flex items-center justify-center"
                    :style="{
                        width: hasUploadedPictures ? '68px' : '100%',
                    }"
                >
                    <div v-if="!hasUploadedPictures" class="w-24px h-24px bg-[url('@/assets/icons/checkin/checkin-camera.svg')] bg-no-repeat bg-center bg-contain"></div>
                    <div v-else class="i-custom-plus w-25px h-25px text-#C9CDD4"></div>
                </div>
            </van-uploader>

            <div class="text-13px font-weight-400 text-#86909C">
                请上传以下内容（照片或截图），最多{{ MAX_PICTURES }}张：<br />
                血常规、人体成分<br />
                生化检查（包括肝功能、肾功能、血糖、血脂）
            </div>
        </div>
        <div class="flex flex-col items-center justify-center w-100% h-fit bg-white rounded-10px p-16px gap-8px mt-8px mb-16px">
            <div class="w-100% h-27px flex justify-center items-center gap-4px">
                <div class="w-14px h-13px bg-[url('~/assets/images/external/report-camera.svg')] bg-no-repeat bg-center bg-[length:100%_100%]"></div>
                拍摄示例
            </div>
            <div class="text-14px font-weight-400 text-#86909C">
                请将边框完整、图文清晰的单份检查、检验报告图片或者10M以内的体检报告文件发送给我。我将提供专业的医学解读，并且给出针对性的健康建议。
            </div>
            <div class="flex w-100% h-121px bg-#F2F4F7 rd-10px">
                <div class="flex-1 flex flex-col items-center justify-center gap-4px">
                    <div class="flex items-center justify-center gap-4px">
                        <div class="i-custom:check-circle-fill w-16px h-16px"></div>平整放置
                    </div>
                    <div class="w-109px h-76px bg-[url('~/assets/images/external/place-flat.svg')] bg-no-repeat bg-center bg-[length:100%_100%]"></div>
                </div>
                <div class="flex-1 flex flex-col items-center justify-center gap-4px">
                    <div class="flex items-center justify-center gap-4px">
                        <div class="i-custom:check-circle-fill w-16px h-16px"></div>完整拍摄
                    </div>
                    <div class="w-109px h-76px bg-[url('~/assets/images/external/full-placement.svg')] bg-no-repeat bg-center bg-[length:100%_100%]"></div>
                </div>
            </div>
        </div>
        <div class="flex flex-col items-center justify-center gap-8px">
            <van-button type="primary" :disabled="uploadedFileIds.length === 0" round class="w-240px h-50px" @click="handleGenerateReport(true)">
                立即生成体重管理方案
            </van-button>
            <van-button type="primary" plain round class="w-240px h-50px" @click="handleGenerateReport(false)">
                暂不上传，直接生成健康方案
            </van-button>
        </div>
    </div>
</template>

<style scoped>
:deep(.my-uploader .van-uploader__input-wrapper),
:deep(.my-uploader) {
    width: 100% !important;
}

:deep(.van-uploader) {
    --van-uploader-border-radius: 10px;
    --van-uploader-delete-icon-size: 24px;
    --van-uploader-delete-background: #11111126;

}

:deep(.van-uploader__preview-image) {
    border: 1px solid #F2F4F7
}

:deep(.van-uploader__preview-delete--shadow) {
    border-top-right-radius: 10px;
}
</style>
