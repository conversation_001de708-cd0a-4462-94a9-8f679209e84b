<script setup lang="ts">
import { dietary, sport, sportNotices } from '@/utils/weight-management-constants'

const route = useRoute()
const router = useRouter()

const parsedResult = ref<ParsedResult>({} as ParsedResult)
const physicalCondition = ref<PhysicalCondition>({} as PhysicalCondition)
const dietRecommendationData = ref<DietPlan>()
const sportsAbilityRating = ref<SportsAbilityLevel>('初级')
const healthProgramData = ref<HealthProgramIndex>()
const decodedResults = ref<DecodedResults>({} as DecodedResults)
const isViewAll = ref(false)
const { filteredLifeStyles, parseQuestionData } = useLifeStyle()
const caloricIntake = ref<number>(0)

const labelMap = {
    problem: { zh: '临床问题', en: 'Problem' },
    etiology: { zh: '病因', en: 'Etiology' },
    symptoms: { zh: '临床表现', en: 'Symptoms' },
}

function parsePesData(pes: string) {
    const result: Record<string, string> = {}
    pes.split(',').forEach((pair) => {
        const [key, ...rest] = pair.split(/[：:]/)
        if (key && rest.length > 0) {
            result[key.trim()] = rest.join(':').trim()
        }
    })
    return result
}

const bmiNormalRange = computed(() => {
    if (parsedResult.value.age >= 18 && parsedResult.value.age <= 65) {
        return '18.5-23.9'
    } else if (parsedResult.value.age >= 65 && parsedResult.value.age <= 80) {
        return '20-26.9'
    } else if (parsedResult.value.age > 80) {
        return '22-26.9'
    }
    return ''
})

const weightNormalRange = computed(() => {
    const height = parsedResult.value.height
    const bmiRange = bmiNormalRange.value
    if (!height || !bmiRange) return ''

    const [minBMI = 18.5, maxBMI = 23.9] = bmiRange.split('-').map(Number)
    const heightInMeters = height / 100

    const minWeight = Math.round(minBMI * heightInMeters * heightInMeters)
    const maxWeight = Math.round(maxBMI * heightInMeters * heightInMeters)

    return `${minWeight}-${maxWeight}`
})

const sportNoticeType = computed(() => {
    // 获取体重范围
    const weightRange = weightNormalRange.value || '18.5-23.9'
    const [minWeight = 18.5, maxWeight = 23.9] = weightRange.split('-').map(Number)
    const currentWeight = parsedResult.value.weight

    // 判断体重是否正常
    const weightStatus = (currentWeight >= minWeight && currentWeight <= maxWeight) ? '正常' : '异常'
    // 返回对应类型
    return `${weightStatus}-${sportsAbilityRating.value}`
})

const waistNormalRange = computed(() => {
    if (parsedResult.value.gender === '男') {
        return '<85'
    } else if (parsedResult.value.gender === '女') {
        return '<80'
    }
    return ''
})

const fatAreaNormalRange = computed(() => {
    if (parsedResult.value.gender === '男') {
        return '10-20'
    } else if (parsedResult.value.gender === '女') {
        return '15-25'
    }
    return ''
})

const bmiText = computed(() => {
    const bmi = parsedResult.value.bmi
    const bmiRanges = [
        { max: 18.5, text: '低体重状态' },
        { max: 24, text: '正常体重状态' },
        { max: 28, text: '超重状态' },
        { max: 32, text: '肥胖状态' },
        { max: Infinity, text: '重度肥胖状态' },
    ]

    const result = bmiRanges.find(range => bmi < range.max)
    return result?.text
})

const waterIntake = computed(() => {
    const conditions = parsedResult.value?.conditions || []
    const gender = parsedResult.value?.gender
    if (conditions.includes('尿酸升高或痛风')) {
        return 2000
    } else if (gender === '男') {
        return 1700
    } else {
        return 1500
    }
})

const ageToBMI = computed(() => {
    const age = parsedResult.value.age
    if (age >= 80) return 22
    if (age >= 65) return 20
    if (age >= 18 && age <= 64) return 18.5
    return 18.5
})

const diseases = computed(() => {
    const conditions = parsedResult.value?.conditions || []
    return conditions.includes('none') ? '' : conditions.join('、')
})

const recommendedPattern = computed((): DietaryPlanType => {
    const pattern = dietRecommendationData.value?.dietMode
    return pattern?.replace('饮食模式', '') as DietaryPlanType
})

const splitSuggestions = computed(() => {
    const text = healthProgramData.value?.evaluation_suggestions || ''
    const parts = text.split(/(建议您)/)
    return {
        before: parts[0] || '',
        suggestion: parts.slice(1).join('') || '',
    }
})

const getPatternName = computed(() => {
    return dietRecommendationData.value?.dietMode.startsWith('低碳水化合物膳食') ? dietRecommendationData.value?.dietMode.replace('膳食', '') : dietRecommendationData.value?.dietMode
})

async function handleBackHome() {
    router.replace('/user/checkin')
}

function handleViewAll() {
    if (isViewAll.value) {
        // 如果当前是展开状态，先添加关闭动画
        const content = document.querySelector('.content-wrapper')
        if (content) {
            content.classList.add('closing')
            // 等待动画结束后再隐藏内容
            setTimeout(() => {
                isViewAll.value = false
            }, 600) // 动画持续时间
        }
    } else {
        // 如果当前是折叠状态，直接展开
        isViewAll.value = true
    }
}

function parseHealthProgramResults(results: HealthProgramIndex) {
    healthProgramData.value = results
    sportsAbilityRating.value = results.sportRating as SportsAbilityLevel
    parsedResult.value = JSON.parse(results.weightManagementQuestionnaire)
    parseQuestionData(results.weightManagementQuestionnaire)
    dietRecommendationData.value = results.dietPlan
    physicalCondition.value = results?.physicalCondition || {} as PhysicalCondition
}

async function getHealthProgramData(planId?: string) {
    const { closeLoading } = useLoading()
    try {
        const url = planId
            ? `/api/healthProgram/getHealthProgramPlan/${planId}`
            : '/api/healthProgram/getHealthProgramIndex'
        const { results, state } = await useWrapFetch<BaseResponse<HealthProgramIndex>>(url)
        if (state === 200 && results) {
            parseHealthProgramResults(results)
        }
    } catch (error) {
        console.error('获取健康计划数据失败:', error)
    } finally {
        closeLoading()
    }
}

async function getCaloricIntake() {
    try {
        const { results, state } = await useWrapFetch<BaseResponse<string>>('/api/user/getCaloricIntake')
        if (state === 200 && results) {
            caloricIntake.value = Number(results)
        }
    } catch (error) {
        console.error('获取支付状态失败:', error)
    }
}

async function getState() {
    try {
        const { results, state } = await useWrapFetch<BaseResponse<string>>('/api/user/getState')
        if (state === 200 && results) {
            decodedResults.value = JSON.parse(atob(results))
        }
    } catch (error) {
        console.error('获取支付状态失败:', error)
    }
}

async function handleSetWeightManagementPlan() {
    try {
        const { bmi } = parsedResult.value
        // 根据BMI值和肠鸣症状判断体重管理计划类型
        let planId = ''
        const bowelSounds = parsedResult.value.bowel_sounds
        const mildBowelSounds = ['一过性肠鸣', '短暂偶发的肠鸣不适']
        const severeBowelSounds = '频发的长时间的肠鸣，严重影响社会活动'

        if (bmi > 37.5 || bowelSounds === severeBowelSounds) {
            planId = 'metabolic-improvement' // 糖脂代谢综合管理计划
        } else if (bmi > 24 || mildBowelSounds.includes(bowelSounds)) {
            planId = 'lipid-reduction' // 降脂减重管理计划
            // } else if (bmi <= 24 || !bowelSounds) {
            //     planId = 'fasting' // 轻断食体重管理计划
        }

        const wx = await useWxBridge({})
        wx?.miniProgram.redirectTo({
            url: `/pages/order/new-pay?planId=${planId}&showPay=1`,
        })
    } catch (error) {
        console.error('设置体重管理计划失败:', error)
    }
}

onMounted(() => {
    const planId = route.query.planId as string
    if (planId) {
        getHealthProgramData(planId)
    } else {
        const healthResults = JSON.parse(localStorage.getItem('healthResults') || '{}')
        if (healthResults) {
            parseHealthProgramResults(healthResults)
        }
    }
    getState()
    getCaloricIntake()
})

onBeforeUnmount(() => {
    localStorage.removeItem('healthResults')
})
</script>

<template>
    <div class="w-full min-h-screen flex flex-col bg-#f6f8fa px-16px pt-32px gap-16px break-all"
        :class="bmiText === '低体重状态' ? 'pb-0px' : !route.query.planId ? 'pb-80px' : 'pb-16px'">
        <div class="w-full h-60px flex flex-col justify-center">
            <div class="text-#505D5B text-20px font-400 h-24px lh-24px">
                Dear
            </div>
            <div class="text-#00AC97 text-32px font-600 h-36px lh-36px">
                {{ parsedResult?.name }}
            </div>
        </div>

        <div v-if="healthProgramData?.pes" class="flex flex-col gap-16px text-#4E5969 text-14px font-400">
            <div
                v-for="(section, key) in parsePesData(healthProgramData?.pes)"
                :key="key"
            >
                <div class="flex items-center text-#1D2229 text-15px font-600">
                    <div class="i-custom:problem inline-block w-16px h-16px mr-4px"></div>
                    {{ labelMap[key as keyof typeof labelMap].zh }}({{ labelMap[key as keyof typeof labelMap].en }})
                </div>
                <div class="text-#4E5969 text-14px font-400">{{ section }}</div>
            </div>
        </div>

        <div v-if="healthProgramData?.evaluation_suggestions" class="text-#4E5969 text-14px font-400">
            <span>{{ splitSuggestions.before }}</span>
        </div>
        <div v-if="healthProgramData?.evaluation_suggestions" class="text-#4E5969 text-14px font-400">
            <span>{{ splitSuggestions.suggestion }}</span>
        </div>

        <template v-if="bmiText === '低体重状态'">
            <div class="w-full flex items-center justify-center flex-col gap-8px">
                <div class="text-#505D5B text-14px font-300">
                    您好！根据对您的整体健康状况评估，您目前处于
                    <span class="text-#00AC97 text-14px font-600 underline decoration-#00AC97">
                        {{ bmiText }}
                    </span>
                    ，具有健康风险，建议您咨询医生或营养师获得专业指导，并且遵循良好的生活方式。
                </div>
            </div>

            <div class="w-full bg-white rd-lt-12px rd-rt-12px flex-1 p-16px">
                <user-weight-graph class="user-weight-graph" :bmi="Number(parsedResult.bmi)"
                    :show-description="false" />

                <div class="text-#A5A6A6 text-14px font-300">
                    体质指数（BMI）是评估全身性肥胖的通用标准，其计算方式为：体重（kg）除以身高（m）的平方。在我国成年人群中，BMI
                    低于 18.5 kg/m2 为 {{ bmiText }}。
                </div>
            </div>
        </template>

        <div v-if="bmiText !== '低体重状态'" class="w-full h-fit bg-white p-16px flex flex-col gap-16px rd-12px">
            <div class="flex items-center gap-4px">
                <img src="@/assets/images/external/icon-weight.svg" class="w-16px h-12px" />
                <div class="text-#00AC97 text-16px font-500">
                    当前身体状态
                </div>
            </div>
            <div class="flex justify-between">
                <div class="flex flex-col justify-center items-center">
                    <div class="text-#505D5B text-14px font-300">
                        <span class="text-#1D2229 text-24px font-600">{{ parsedResult.weight || '-' }}</span>
                        <span class="text-##1D2229 text-13px font-400">kg</span>
                    </div>
                    <div class="text-#4E5969 text-12px font-400">
                        体重
                    </div>
                </div>
                <div class="flex flex-col justify-center items-center">
                    <div class="text-#505D5B text-14px font-300">
                        <span class="text-#1D2229 text-24px font-600">{{ physicalCondition?.waistCircumference
                            || parsedResult.waist || '-' }}</span>
                        <span class="text-##1D2229 text-13px font-400">cm</span>
                    </div>
                    <div class="text-#4E5969 text-12px font-400">
                        腰围
                    </div>
                </div>
                <div class="flex flex-col justify-center items-center">
                    <div class="text-#505D5B text-14px font-300">
                        <span class="text-#1D2229 text-24px font-600">{{ physicalCondition?.bodyFatRate
                            || parsedResult.fat_rate
                            || '-' }}</span>
                        <span class="text-##1D2229 text-13px font-400">%</span>
                    </div>
                    <div class="text-#4E5969 text-12px font-400">
                        体脂率
                    </div>
                </div>
                <div class="flex flex-col justify-center items-center">
                    <div class="text-#505D5B text-14px font-300">
                        <span class="text-#1D2229 text-24px font-600">{{ physicalCondition?.visceralFatArea || '-'
                            }}</span>
                        <span class="text-##1D2229 text-13px font-400">cm²</span>
                    </div>
                    <div class="text-#4E5969 text-12px font-400">
                        内脂脂肪面积
                    </div>
                </div>
            </div>

            <div class="flex items-center gap-4px">
                <img src="@/assets/images/external/icon-weight.svg" class="w-16px h-12px" />
                <div class="text-#00AC97 text-16px font-500">
                    建议体重管理目标
                </div>
            </div>
            <div class="flex gap-4px">
                <div class="flex flex-1 flex-col justify-center items-center bg-#E4FAF9 rd-10px py-8px">
                    <div class="text-#4E5969 text-12px font-400">
                        体重
                    </div>
                    <div class="text-#1D2229 text-16px font-600">{{ weightNormalRange }}</div>
                    <div class="text-#868F9C text-10px font-400">kg并维持</div>
                </div>
                <div class="flex flex-1 flex-col justify-center items-center bg-#E4FAF9 rd-10px py-8px">
                    <div class="text-#4E5969 text-12px font-400">
                        腰围
                    </div>
                    <div class="text-#1D2229 text-16px font-600">{{ waistNormalRange }}</div>
                    <div class="text-#868F9C text-10px font-400">cm并维持</div>
                </div>
                <div class="flex flex-1 flex-col justify-center items-center bg-#E4FAF9 rd-10px py-8px">
                    <div class="text-#4E5969 text-12px font-400">
                        体脂率
                    </div>
                    <div class="text-#1D2229 text-16px font-600">{{ fatAreaNormalRange }}</div>
                    <div class="text-#868F9C text-10px font-400">%并维持</div>
                </div>
                <div class="flex flex-1 flex-col justify-center items-center bg-#E4FAF9 rd-10px py-8px">
                    <div class="text-#4E5969 text-12px font-400">
                        内脂面积
                    </div>
                    <div class="text-#1D2229 text-16px font-600">{{ '<80' }}</div>
                            <div class="text-#868F9C text-10px font-400">cm²并维持</div>
                    </div>
                </div>

                <div class="flex bg-#F5F6F7 rd-6px p-4px text-#4E5969 text-12px font-500 gap-4px">
                    <img src="@/assets/images/external/icon-trumpet.svg" class="w-12px h-12px mt-[2px]" />
                    重要性由重至轻分别为内脏脂肪面积＞腰围＞体脂率＞体重
                </div>

                <div class="flex items-center gap-4px">
                    <img src="@/assets/images/external/icon-plan.svg" class="w-20px h-20px" />
                    <div class="text-#00AC97 text-16px font-500">
                        方案简述
                    </div>
                </div>
                <div class="flex flex-col gap-8px">
                    <div class="flex gap-8px items-center">
                        <img src="@/assets/images/external/icon-step.svg" class="w-12px h-12px" />
                        <span class="text-#505D5B text-16px font-500">调整饮食模式</span>
                    </div>
                    <div class="flex flex-col w-full relative">
                        <div
                            class="text-#505D5B text-14px font-500 flex justify-center items-center bg-#FFA41F26 h-26px">
                            {{ getPatternName }}
                        </div>
                        <div class="absolute top-26px bottom-0 left-1/4 w-[0.5px] bg-#FFB852"></div>
                        <div v-for="(food, index) in dietary.adjust[recommendedPattern]" :key="food.category"
                            class="flex bg-#FFA41F0D"
                            :class="{ 'border-b-0.5px border-b-dashed border-b-#E0EEED': index !== dietary.adjust[recommendedPattern].length - 1 }">
                            <div class="flex justify-center items-center w-1/4 text-#505D5B text-10px font-500">
                                {{ food.category }}
                            </div>
                            <div class="flex-1 p-8px text-#505D5B text-10px font-300">
                                {{ food.items }}
                            </div>
                        </div>
                    </div>
                    <div
                        class="w-full h-30px flex items-center bg-#E4FAF9 gap-8px rd-6px pl-8px text-#00AC97 text-12px font-300">
                        <img src="@/assets/images/external/icon-announcement.svg" class="w-15px h-15px" />
                        建议参考 SLMC 管家每日推荐进行每日配餐
                    </div>
                </div>

                <div class="flex flex-col gap-8px">
                    <div class="flex gap-8px items-center">
                        <img src="@/assets/images/external/icon-step.svg" class="w-12px h-12px" />
                        <span class="text-#505D5B text-16px font-500">坚持规律运动</span>
                    </div>
                </div>
                <div class="flex gap-4px">
                    <div class="flex-1 flex flex-col items-center justify-between min-w-0">
                        <img src="@/assets/images/external/sport-steps.svg" class="w-92px h-124px" />
                        <div class="flex flex-col flex-1 justify-between w-full">
                            <div class="text-#4E5969 text-14px font-400 w-full text-center">日常活动</div>
                            <div class="text-#1D2229 text-16px font-500 w-full text-center">6000</div>
                            <div
                                class="h-25px px-8px py-4px bg-#F2FFFD rd-24px flex items-center justify-center text-#505D5B text-14px font-400 w-full">
                                步/日
                            </div>
                        </div>
                    </div>
                    <div class="flex-1 flex flex-col items-center justify-between min-w-0">
                        <img src="@/assets/images/external/aerobic-exercise.svg" class="w-92px h-124px" />
                        <div class="flex flex-col flex-1 justify-between w-full">
                            <div class="text-#4E5969 text-14px font-400 w-full text-center">有氧运动</div>
                            <div class="text-#1D2229 text-16px font-500 w-full text-center">
                                {{
                                    dietary.regularMovement[sportNoticeType]?.aerobic }}
                            </div>
                            <div
                                class="h-25px px-8px py-4px bg-#F2FFFD rd-24px flex items-center justify-center text-#505D5B text-14px font-400 w-full">
                                {{ dietary.regularMovement[sportNoticeType]?.aerobicStrength }}
                            </div>
                        </div>
                    </div>
                    <div class="flex-1 flex flex-col items-center justify-between min-w-0">
                        <img src="@/assets/images/external/resistance-motion.svg" class="w-92px h-124px" />
                        <div class="flex flex-col flex-1 justify-between w-full">
                            <div class="text-#4E5969 text-14px font-400 w-full text-center">抗阻运动</div>
                            <div class="text-#1D2229 text-16px font-500 w-full text-center">
                                {{
                                    dietary.regularMovement[sportNoticeType]?.resistance }}
                            </div>
                            <div
                                class="h-25px px-8px py-4px bg-#F2FFFD rd-24px flex items-center justify-center text-#505D5B text-14px font-400 w-full">
                                {{ dietary.regularMovement[sportNoticeType]?.resistanceStrength }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="flex flex-col gap-8px">
                    <div class="flex gap-8px items-center">
                        <img src="@/assets/images/external/icon-step.svg" class="w-12px h-12px" />
                        <span class="text-#505D5B text-16px font-500">优化生活方案</span>
                    </div>
                </div>
                <div class="grid grid-cols-3 gap-4px">
                    <div v-for="lifeStyle in filteredLifeStyles" :key="lifeStyle.name"
                        class="flex flex-col items-center justify-center">
                        <div :class="lifeStyle.icon" class="w-32px h-32px"></div>
                        <div class="text-#4E5969 text-14px font-400">{{ lifeStyle.name }}</div>
                    </div>
                </div>
                <div
                    class="w-full h-fit flex items-center bg-#E4FAF9 gap-8px rd-6px p-8px text-#00AC97 text-12px font-300">
                    <img src="@/assets/images/external/icon-announcement.svg" class="w-15px h-15px" />
                    温馨提示： 本计划基于健康研究用于体重和代谢管理，不替代医生建议。如有慢性病、服药、孕期等情况，请先咨询医生。
                </div>

                <div class="flex flex-col items-center justify-center gap-8px" @click="handleViewAll">
                    <div class="text-#1D2229 text-14px font-500">点击{{ isViewAll ? '收起' : '展开' }}完整方案</div>
                    <img src="@/assets/images/external/icon-swipe-down.svg"
                        class="w-24px h-24px transition-transform duration-300" :class="{ 'rotate-180': isViewAll }" />
                </div>
            </div>

            <div v-if="isViewAll" class="content-wrapper flex flex-col gap-16px" :class="{ closing: !isViewAll }">
                <div class="w-full h-fit bg-white p-16px flex flex-col gap-16px rd-12px">
                    <template v-if="bmiText === '正常体重状态'">
                        <div class="w-full flex items-center justify-center flex-col gap-8px">
                            <div class="text-#505D5B text-14px font-300">
                                根据评估问卷和医学检查报告，综合分析相关病史、生活方式风险及BMI、腰围、体脂率、血压、血脂、血糖、肝肾功、腹部彩超等结果，目前体重处于正常范围，建议均衡饮食，适量运动，维持健康体重。
                            </div>
                            <div class="text-#505D5B text-14px font-300">
                                若有进一步塑形或减脂需求，可参照如下体重管理方案，建议连续使用不超过3个月，BMI不可降至{{ ageToBMI }}以下。
                            </div>
                            <div v-if="diseases"
                                class="text-#00AC97 text-14px font-600 underline decoration-#00AC97 w-full text-left">
                                {{ diseases }}
                            </div>
                            <div v-if="diseases" class="text-#505D5B text-14px font-300">
                                多种慢病发病风险高，建议进行体重管理，特制定如下个体化体重管理方案，请认真阅读并严格执行。
                            </div>
                        </div>
                    </template>

                    <template v-if="bmiText && ['超重状态', '肥胖状态', '重度肥胖状态'].includes(bmiText)">
                        <div class="w-full flex flex-col gap-8px">
                            <div class="text-#505D5B text-14px font-300">
                                根据评估问卷和医学检查报告，综合分析相关病史、生活方式风险及BMI、腰围、体脂率、血压、血脂、血糖、肝肾功、腹部彩超等结果，目前存在
                            </div>
                            <div class="text-#00AC97 text-14px font-600 underline decoration-#00AC97">
                                <span>{{ bmiText }}</span><span v-if="diseases">、{{ diseases }}</span>
                            </div>
                            <div class="text-#505D5B text-14px font-300">
                                多种慢病发病风险高，建议进行体重管理，特制定如下个体化体重管理方案，请认真阅读并严格执行。
                            </div>
                        </div>
                    </template>

                    <template v-if="bmiText && ['正常体重状态', '超重状态', '肥胖状态', '重度肥胖状态'].includes(bmiText)">
                        <div class="w-full h-fit bg-white flex flex-col gap-16px rd-12px">
                            <div class="flex flex-col gap-8px">
                                <div class="flex gap-8px items-center">
                                    <img src="@/assets/images/external/icon-step.svg" class="w-12px h-12px" />
                                    <span class="text-#00AC97 text-16px font-500">Step.1</span>
                                    <span class="text-#505D5B text-16px font-500">体重管理前准备</span>
                                </div>
                                <div class="grid grid-cols-2 gap-16px">
                                    <div class="flex flex-col items-center gap-8px">
                                        <div class="h-100px flex items-center justify-center">
                                            <img src="@/assets/images/external/weight-scale.png"
                                                class="w-75px h-75px object-contain" />
                                        </div>
                                        <div class="text-#333333 text-14px font-400 text-center w-full">
                                            体重秤<br />
                                            <span class="text-#505D5B text-12px">（最好是体脂秤）</span>
                                        </div>
                                    </div>
                                    <div class="flex flex-col items-center gap-8px">
                                        <div class="h-100px flex items-center justify-center">
                                            <img src="@/assets/images/external/food-scale.png"
                                                class="w-61px h-83px object-contain" />
                                        </div>
                                        <div class="text-#333333 text-14px font-400 text-center w-full">
                                            食物秤
                                        </div>
                                    </div>
                                    <div class="flex flex-col items-center gap-8px">
                                        <div class="h-100px flex items-center justify-center">
                                            <img src="@/assets/images/external/tape-measure.png"
                                                class="w-90px h-87px object-contain" />
                                        </div>
                                        <div class="text-#333333 text-14px font-400 text-center w-full">
                                            卷尺<br />
                                            <span class="text-#505D5B text-12px">（最好是腰围尺）</span>
                                        </div>
                                    </div>
                                    <div class="flex flex-col items-center gap-8px">
                                        <div class="h-100px flex items-center justify-center">
                                            <img src="@/assets/images/external/weight-manager.png"
                                                class="w-80px h-97px object-contain" />
                                        </div>
                                        <div class="text-#333333 text-14px font-400 text-center w-full">
                                            SLMC体重管家<br />
                                            小程序
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-col gap-8px">
                                <div class="flex gap-8px items-center">
                                    <img src="@/assets/images/external/icon-step.svg" class="w-12px h-12px" />
                                    <span class="text-#00AC97 text-16px font-500">Step.2</span>
                                    <span class="text-#505D5B text-16px font-500">饮食方案</span>
                                </div>
                                <div class="flex flex-col gap-8px">
                                    <div class="text-#A5A6A6 text-12px font-300">
                                        本方案基于相关病史、超重/肥胖情况及医学检查结果的综合评估而制定，请严格执行，做到正确选择食物、称量，切不可为了追求减重速度而过度节食！
                                    </div>
                                    <div class="flex flex-col gap-8px">
                                        <div class="text-#00AC97 text-14px font-600">1. 限制能量摄入</div>
                                        <div
                                            class="flex flex-col justify-center items-center rd-10px w-full h-89px bg-gradient-to-b from-[#e6f7f5] to-[#fafdfd] border-solid border-1px border-#e5f7f5">
                                            <div class="text-#00AC97 flex items-end">
                                                <div class="text-40px font-600 leading-none">{{ caloricIntake }}</div>
                                                <div class="text-20px font-400 leading-none">kcal</div>
                                            </div>
                                            <div class="text-#333333 text-14px font-400">建议每日能量摄入</div>
                                        </div>
                                        <div class="text-#505D5B text-14px font-300">
                                            根据性别、年龄、体重、身高和身体活动等制定
                                        </div>
                                    </div>

                                    <div class="flex flex-col gap-8px">
                                        <div class="text-#00AC97 text-14px font-600">2. 调整食物构成</div>
                                        <div
                                            class="flex flex-col justify-center items-center rd-10px w-full h-89px bg-gradient-to-b from-[#e6f7f5] to-[#fafdfd] border-solid border-1px border-#e5f7f5">
                                            <div v-if="recommendedPattern" class="text-#00AC97 text-26px font-600">
                                                {{ getPatternName }}
                                            </div>
                                            <div class="text-#333333 text-14px font-400">根据健康优先级，建议选择</div>
                                        </div>
                                        <div class="text-#505D5B text-14px font-300 w-full text-left">
                                            目前存在<span v-if="bmiText !== '正常体重状态'">{{ bmiText }}</span><span
                                                v-if="diseases">、{{ diseases }}</span>
                                        </div>
                                    </div>

                                    <div class="flex flex-col gap-8px">
                                        <div v-if="recommendedPattern" class="text-#00AC97 text-14px font-600">
                                            3. {{
                                                recommendedPattern }}饮食模式原则/特点
                                        </div>
                                        <div v-if="recommendedPattern && dietary.principles"
                                            class="text-#505D5B text-14px font-300">
                                            {{
                                                dietary.principles[recommendedPattern] }}
                                        </div>
                                    </div>

                                    <div class="flex flex-col gap-8px">
                                        <div v-if="recommendedPattern" class="text-#00AC97 text-14px font-600">
                                            4. {{
                                                recommendedPattern }}饮食模式的食物宜忌
                                        </div>
                                        <div
                                            v-if="recommendedPattern && dietary.recommendedFoods && dietary.forbiddenFoods">
                                            <div class="flex flex-col w-full relative">
                                                <div
                                                    class="text-#505D5B text-14px font-500 flex justify-center items-center bg-#00AC971A h-26px">
                                                    推荐食物
                                                </div>
                                                <div class="absolute top-26px bottom-0 left-1/4 w-[0.5px] bg-#00AC97">
                                                </div>
                                                <div v-for="(food, index) in dietary.recommendedFoods[recommendedPattern]"
                                                    :key="food.category" class="flex bg-#4BFFE90D"
                                                    :class="{ 'border-b-0.5px border-b-dashed border-b-#E0EEED': index !== dietary.recommendedFoods[recommendedPattern].length - 1 }">
                                                    <div
                                                        class="flex justify-center items-center w-1/4 text-#505D5B text-10px font-500">
                                                        {{ food.category }}
                                                    </div>
                                                    <div class="flex-1 p-8px text-#505D5B text-10px font-300">
                                                        {{ food.items }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="flex flex-col w-full relative">
                                                <div
                                                    class="text-#505D5B text-14px font-500 flex justify-center items-center bg-#FFA41F26 h-26px">
                                                    限制/禁忌食物
                                                </div>
                                                <div class="absolute top-26px bottom-0 left-1/4 w-[0.5px] bg-#FFB852">
                                                </div>
                                                <div v-for="(food, index) in dietary.forbiddenFoods[recommendedPattern]"
                                                    :key="food.category" class="flex bg-#FFA41F0D"
                                                    :class="{ 'border-b-0.5px border-b-dashed border-b-#E0EEED': index !== dietary.forbiddenFoods[recommendedPattern].length - 1 }">
                                                    <div
                                                        class="flex justify-center items-center w-1/4 text-#505D5B text-10px font-500">
                                                        {{ food.category }}
                                                    </div>
                                                    <div class="flex-1 p-8px text-#505D5B text-10px font-300">
                                                        {{ food.items }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="flex flex-col gap-8px">
                                        <div v-if="recommendedPattern" class="text-#00AC97 text-14px font-600">
                                            5. {{
                                                recommendedPattern }}饮食模式的一日饮食示例
                                        </div>
                                        <div
                                            v-if="recommendedPattern && dietary.examples && dietary.examples[recommendedPattern]">
                                            <div class="flex flex-col w-full relative">
                                                <div class="absolute top-0 bottom-0 left-1/4 w-[0.5px] bg-#00AC97">
                                                </div>
                                                <div v-for="(food, index) in dietary.examples[recommendedPattern].dayFoods"
                                                    :key="food.category" class="flex bg-#4BFFE90D"
                                                    :class="{ 'border-b-0.5px border-b-dashed border-b-#E0EEED': index !== dietary.examples[recommendedPattern].dayFoods.length - 1 }">
                                                    <div
                                                        class="flex justify-center items-center w-1/4 text-#505D5B text-10px font-500">
                                                        {{ food.category }}
                                                    </div>
                                                    <div class="flex-1 p-8px text-#505D5B text-10px font-300">
                                                        {{ food.items }}
                                                    </div>
                                                </div>
                                            </div>
                                            <ul
                                                class="w-full text-#505D5B text-14px font-300 list-disc marker:text-#505D5B mt-8px pl-16px">
                                                <li v-for="(item, index) in dietary.examples[recommendedPattern].fullDay"
                                                    :key="index">
                                                    {{ item }}
                                                </li>
                                                <li class="font-500">您可以进入小程序中获取食物替换建议，以丰富膳食种类、均衡营养摄入。</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>

                                <!-- <div class="w-243px h-32px bg-#00AC97 rounded-18px flex items-center justify-center text-white text-14px font-400 mx-auto my-16px">
                        更多饮食方案点击"饮食打卡"查看
                    </div> -->
                            </div>
                            <div class="flex flex-col gap-8px">
                                <div class="flex gap-8px items-center">
                                    <img src="@/assets/images/external/icon-step.svg" class="w-12px h-12px" />
                                    <span class="text-#00AC97 text-16px font-500">Step.3</span>
                                    <span class="text-#505D5B text-16px font-500">运动方案</span>
                                </div>
                                <div class="flex flex-col gap-8px">
                                    <div class="text-#A5A6A6 text-12px font-300">
                                        本方案基于年龄、超重/肥胖情况、既往运动能力、相关病史及医学检查报告等的综合评估而制定，建议运动循序渐进，注重长期坚持，切不可为了追求减重速度而过量运动！
                                    </div>
                                    <div class="flex flex-col gap-8px">
                                        <div class="text-#00AC97 text-14px font-600">1. 日常活动</div>
                                        <div
                                            class="flex flex-col justify-center items-center rd-10px w-full h-89px bg-gradient-to-b from-[#e6f7f5] to-[#fafdfd] border-solid border-1px border-#e5f7f5">
                                            <div class="text-#00AC97 flex items-end">
                                                <div class="text-40px font-500 leading-none">6000</div>
                                                <div class="text-20px font--400 leading-none mb-2px">步</div>
                                            </div>
                                            <div class="text-#333333 text-14px font-400">保证每日日常活动量</div>
                                        </div>
                                        <div class="text-#505D5B text-14px font-300">
                                            增加日常生活中的身体活动，如步行、骑车、外出购物、做家务等，减少静坐时间，连续静坐时间不超过30分钟，每30分钟起身活动3-5分钟。
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-8px">
                                        <div class="text-#00AC97 text-14px font-600">2. 主动运动</div>
                                        <div
                                            class="flex flex-col justify-center items-center rd-10px w-full h-89px bg-gradient-to-b from-[#e6f7f5] to-[#fafdfd] border-solid border-1px border-#e5f7f5">
                                            <div class="text-#00AC97 flex items-end">
                                                <div class="text-40px font-500 leading-none">
                                                    {{ sportsAbilityRating
                                                    }}运动方案
                                                </div>
                                            </div>
                                            <div class="text-#333333 text-14px font-400">根据个体情况，建议选择</div>
                                        </div>
                                        <div class="text-#505D5B text-14px font-300 w-full text-left">
                                            目前存在<span v-if="bmiText !== '正常体重状态'">{{ bmiText }}</span><span
                                                v-if="diseases">、{{ diseases }}</span>
                                        </div>
                                    </div>

                                    <div class="flex flex-col gap-8px">
                                        <div class="text-#00AC97 text-14px font-600">
                                            3. {{ sportsAbilityRating }}运动方案的原则
                                        </div>
                                        <div class="text-#505D5B text-14px font-300">
                                            综合年龄、超重/肥胖情况、相关病史及医学检查结果、既往运动能力
                                        </div>
                                        <div v-if="sportsAbilityRating && sport[sportsAbilityRating]"
                                            class="flex flex-col w-full relative">
                                            <div class="absolute top-0 bottom-0 left-1/4 w-[0.5px] bg-#00AC97"></div>

                                            <div v-for="(food, index) in sport[sportsAbilityRating]"
                                                :key="food.category" class="flex bg-#4BFFE90D relative"
                                                :class="{ 'border-b-0.5px border-b-dashed border-b-#E0EEED': index !== sport[sportsAbilityRating].length - 1 }">
                                                <div
                                                    class="flex justify-center items-center w-1/4 text-#505D5B text-10px font-500">
                                                    {{ food.category }}
                                                </div>
                                                <div class="flex-1 p-8px text-#505D5B text-10px font-300">
                                                    {{ food.items }}
                                                </div>
                                                <template v-if="food.items2">
                                                    <div
                                                        class="absolute top-0 bottom-0 left-[62.5%] w-[0.5px] bg-#00AC97">
                                                    </div>
                                                    <div class="flex-1 p-8px text-#505D5B text-10px font-300">
                                                        {{ food.items2 }}
                                                    </div>
                                                </template>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex flex-col gap-8px">
                                        <div class="text-#00AC97 text-14px font-600">4. 注意事项</div>
                                        <ul
                                            class="w-full text-#505D5B text-12px font-300 list-disc marker:text-#505D5B pl-16px">
                                            <li v-for="(item, index) in sportNotices" :key="index">
                                                {{ item }}
                                            </li>
                                        </ul>
                                    </div>
                                    <!-- <div class="w-243px h-32px bg-#00AC97 rounded-18px flex items-center justify-center text-white text-14px font-400 mx-auto my-16px">
                            更多运动方案点击"运动打卡"查看
                        </div> -->
                                </div>
                                <div class="flex flex-col gap-8px">
                                    <div class="flex gap-8px items-center">
                                        <img src="@/assets/images/external/icon-step.svg" class="w-12px h-12px" />
                                        <span class="text-#00AC97 text-16px font-500">Step.4</span>
                                        <span class="text-#505D5B text-16px font-500">行为管理</span>
                                    </div>
                                    <div class="flex flex-col gap-8px">
                                        <div class="flex flex-col gap-8px">
                                            <ul
                                                class="w-full text-#505D5B text-12px font-300 list-disc marker:text-#505D5B pl-16px">
                                                <li>避免吸烟、饮酒；</li>
                                                <li>早睡早起，规律作息，每天保证7~8小时睡眠； </li>
                                                <li>干预前可正常饮食，避免暴饮暴食或过度节食，规律三餐； </li>
                                                <li>每天应少量多次、足量饮水，保证饮水量达{{ waterIntake }}mL以上。</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                                <div class="flex flex-col gap-8px">
                                    <div class="flex gap-8px items-center">
                                        <img src="@/assets/images/external/icon-step.svg" class="w-12px h-12px" />
                                        <span class="text-#00AC97 text-16px font-500">Step.5</span>
                                        <span class="text-#505D5B text-16px font-500">检测与互动</span>
                                    </div>
                                    <div class="flex flex-col gap-8px">
                                        <div class="flex flex-col gap-8px">
                                            <div class="text-#00AC97 text-14px font-600">1. 测体重</div>
                                            <div class="text-#505D5B text-14px font-300">
                                                每天早上空腹称重，注意在相同状态下（晨起空腹、排空大小便、着同一轻薄衣物、同一体重秤）进行。
                                            </div>
                                            <div
                                                class="h-133px w-full bg-#fff rd-10px p-16px flex flex-col justify-between border-solid border-1px border-#F3F3F3">
                                                <div class="flex justify-between">
                                                    <div>
                                                        <div class="text-t-5 font-600 text-15px">
                                                            体重
                                                        </div>

                                                        <div class="text-t-3 text-12px">
                                                            06:30 更新
                                                        </div>
                                                    </div>

                                                    <div class="i-custom:checkin-add w-26px h-26px">
                                                    </div>
                                                </div>

                                                <div class="flex justify-between items-end">
                                                    <div class="flex items-end gap-4px">
                                                        <div class="text-24px leading-none font-800 font-ddinpro">
                                                            78.5
                                                        </div>
                                                        <div class="text-t-3">
                                                            公斤
                                                        </div>
                                                    </div>

                                                    <div class="w-32px h-32px flex-shrink-0 i-custom:checkin-weight-3">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex flex-col gap-8px">
                                            <div class="text-#00AC97 text-14px font-600">2. 测腰围</div>
                                            <div class="text-#505D5B text-14px font-300">第一天及每周（隔7天）测一次腰围。</div>
                                            <div class="flex justify-center items-center">
                                                <img src="@/assets/images/external/icon-waistline-normal.svg"
                                                    class="w-268px h-111px" />
                                            </div>
                                            <div class="flex flex-col">
                                                <div class="w-full h-24px lh-24px font-600 text-14px text-#505D5B">
                                                    测量方法：
                                                </div>
                                                <div class="w-full font-300 text-14px text-#505D5B">
                                                    测腰围为软尺绕肋骨下缘最低点与髂嵴上缘的中点水平一周的围度，测试时自然呼吸不憋气。
                                                </div>
                                            </div>
                                            <div
                                                class="h-133px w-full bg-#fff rd-10px p-16px flex flex-col justify-between border-solid border-1px border-#F3F3F3">
                                                <div class="flex justify-between">
                                                    <div>
                                                        <div class="text-t-5 font-600 text-15px">
                                                            腰围
                                                        </div>

                                                        <div class="text-t-3 text-12px">
                                                            06:30 更新
                                                        </div>
                                                    </div>

                                                    <div class="i-custom:checkin-add w-26px h-26px">
                                                    </div>
                                                </div>

                                                <div class="flex justify-between items-end">
                                                    <div class="flex items-end gap-4px">
                                                        <div class="text-24px leading-none font-800 font-ddinpro">
                                                            0
                                                        </div>
                                                        <div class="text-t-3">
                                                            cm
                                                        </div>
                                                    </div>

                                                    <div class="w-32px h-32px flex-shrink-0 i-custom:checkin-waist">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex flex-col gap-8px">
                                            <div class="text-#00AC97 text-14px font-600">3. 饮食</div>
                                            <div class="text-#505D5B text-14px font-300">
                                                每日饮食可参照推荐的个体化饮食方案，运动可参照推荐的个体化饮食方案进行，并记录于小程序上。
                                            </div>
                                            <div
                                                class="h-133px w-full bg-#fff rd-10px p-16px flex flex-col justify-between border-solid border-1px border-#F3F3F3">
                                                <div class="flex justify-between">
                                                    <div>
                                                        <div class="text-t-5 font-600 text-15px">
                                                            运动
                                                        </div>

                                                        <div class="text-t-3 text-12px">
                                                            06:30 更新
                                                        </div>
                                                    </div>

                                                    <div class="i-custom:checkin-add w-26px h-26px">
                                                    </div>
                                                </div>

                                                <div class="flex justify-between items-end">
                                                    <div class="flex items-end gap-4px">
                                                        <div class="text-24px leading-none font-800 font-ddinpro">
                                                            0
                                                        </div>
                                                        <div class="text-t-3">
                                                            大卡
                                                        </div>
                                                    </div>

                                                    <div class="w-32px h-32px flex-shrink-0 i-custom:checkin-sport-3">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="flex flex-col gap-8px">
                                            <div class="text-#00AC97 text-14px font-600">4. 状态</div>
                                            <div class="text-#505D5B text-14px font-300">
                                                保持乐观向上心态、勇于分享、积极寻求家庭成员及社交圈的鼓励和支持等有助于长期坚持，提高体重管理效果。
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="bmiText !== '正常体重状态'" class="flex flex-col gap-8px">
                                    <div class="flex gap-8px items-center">
                                        <img src="@/assets/images/external/icon-step.svg" class="w-12px h-12px" />
                                        <span class="text-#00AC97 text-16px font-500">Step.6</span>
                                        <span class="text-#505D5B text-16px font-500">复查</span>
                                    </div>
                                    <div class="text-#505D5B text-14px font-300">
                                        每月复查体脂率、血脂、血糖、肝肾功等指标，便于前后对比，并建议根据最新检查结果在"SLMC体重管家"小程序内重新评估，调整体重管理方案（如高蛋白饮食和低碳水化合物膳食饮食连续使用时长不超过3个月、达到体重建议目标后需制定维持体重的方案）。
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                </div>

                <div class="w-full h-fit bg-white flex flex-col rd-12px">
                    <img src="@/assets/images/external/references.svg" class="w-345px h-636px" />
                </div>
            </div>
        </div>

        <div v-if="!route.query.planId"
            class="safe-area-height fixed bottom-0 w-full z-11 flex justify-center items-center">
            <!-- v-if="bmiText === '低体重状态' || decodedResults?.noPay || decodedResults?.planId" -->
            <van-button type="primary" round class="w-240px h-50px text-white text-16px font-500 mx-auto my-16px" @click="handleBackHome">
                开启减重之旅
            </van-button>
            <!-- <van-button v-else type="primary" round class="w-240px h-50px text-white text-16px font-500 mx-auto my-16px"
                @click="handleSetWeightManagementPlan">
                立即设置体重管理计划
            </van-button> -->
        </div>
</template>

<style scoped>
.safe-area-height {
    height: calc(56px + constant(safe-area-inset-bottom));
    height: calc(56px + env(safe-area-inset-bottom))
}

@keyframes slideDown {
    from {
        max-height: 0;
        opacity: 0;
    }

    to {
        max-height: 50000px;
        opacity: 1;
    }
}

@keyframes slideUp {
    from {
        max-height: 50000px;
        opacity: 1;
    }

    to {
        max-height: 0;
        opacity: 0;
    }
}

.content-wrapper {
    overflow: hidden;
    animation: slideDown 0.6s ease-in-out forwards;
}

.content-wrapper.closing {
    animation: slideUp 0.6s ease-in-out forwards;
}

/* 修改组件布局样式 */
:deep([flex][gap-30px][overflow-x-auto]) {
    display: flex !important;
    flex-wrap: wrap !important;
    gap: 10px !important;
    overflow: visible !important;
    justify-content: center !important;
}

:deep([flex][gap-30px][overflow-x-auto] > div) {
    width: calc((100% - 30px) / 4) !important;
    flex: 0 0 auto !important;
}
</style>
