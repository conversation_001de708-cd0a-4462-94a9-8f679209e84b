<script setup lang="ts">
definePageMeta({
    layout: false,
})

useHead({
    meta: [
        {
            name: 'viewport',
            content: 'width=device-width, initial-scale=1.0, maximum-scale=3.0, minimum-scale=1.0, viewport-fit=cover, user-scalable=yes',
        },
    ],
    title: '居家检测',
})

const route = useRoute()
const reportPath = computed(() => (route.params.all as string[]).join('/'))
</script>

<template>
    <div class="bg-fill-1 h-full">
        <shared-pdf-viewer :src="reportPath" />
    </div>
</template>
