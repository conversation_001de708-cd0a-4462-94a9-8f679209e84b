<script setup lang="ts">
definePageMeta({
    middleware: 'h5-auth',
})

useHead({
    title: '居家检测',
})

const { searchValue, filteredTestItems } = useHomeTesting()
</script>

<template>
    <div class="relative">
        <div style="background: linear-gradient(180deg, #DBFFFB 0%, #F4F5F7 100%);" class="h-[270px] absolute top-0 left-0 right-0">
        </div>

        <div class="relative p-16px custom-van-search">
            <van-search
                v-model="searchValue"
                placeholder="请输入检验/检查项目以检索"
                shape="round"
            />

            <div class="bg-white rounded-10px p-16px mt-16px h-[calc(100vh-100px)] flex flex-col justify-between">
                <template v-if="filteredTestItems.length > 0">
                    <div class="flex flex-col gap-16px">
                        <nuxt-link v-for="item in filteredTestItems" :key="item.name" :to="`/h5/home-testing/${item.id}/scan`" class="flex justify-between items-center">
                            <div class="flex items-end gap-10px">
                                <img :src="item.img" class="w-80px h-80px" alt="" srcset="" />

                                <div>
                                    <div class="text-15px font-600 text-t-5">
                                        {{ item.name }}
                                    </div>

                                    <div class="text-13px text-t-3">
                                        {{ item.desc }}
                                    </div>

                                    <div class="flex gap-10px mt-10px">
                                        <div v-for="advantage in item.advantage" :key="advantage" class="bg-primary-6 rd-20px text-11px text-white h-20px px-8px leading-20px">
                                            {{ advantage }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="i-radix-icons:chevron-right text-20px text-t-4"></div>
                        </nuxt-link>
                    </div>

                    <div class="flex flex-col justify-center items-center">
                        <img src="@/assets/images/common/qrcode-4.png" class="w-150px h-150px" />
                        <div class="text-13px text-t-3 mt-10px">
                            扫码加入小程序，获取更多健康服务
                        </div>
                    </div>
                </template>

                <template v-else>
                    <van-empty description="暂无检测项目" />
                </template>
            </div>
        </div>
    </div>
</template>

<style lang="scss">
.custom-van-search {
    .van-search {
        background:unset;
        padding:0;

    }

    .van-search__field {
        padding:0 18px;
        background-color: #fff;
        border-radius: 20px;
        border: 1px solid #00AC97;
        height: 43px;
    }

    .van-search__content {
        padding:0;
    }

    .van-icon:before {
        color: #00AC97;
        font-weight: 600;
    }
}
</style>
