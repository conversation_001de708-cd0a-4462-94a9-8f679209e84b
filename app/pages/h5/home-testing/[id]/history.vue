<script setup lang="ts">
import dayjs from 'dayjs'

useHead({
    title: '居家检测',
})
definePageMeta({
    middleware: 'h5-auth',
})

interface Report {
    createTime: string
    customOrderId: string
    id: number
    iz_deleted: number
    orderNum: string
    pdfUrl: string
    samId: string
    sampleInfoJson: string
    seqType: number
    status: number
    updateTime: string
}

const { openId, phoneNumber } = useH5User()

const { data } = useAPI<Report[]>(`/gene-test/program/list-order/${openId}/${phoneNumber}`)

const { testItems } = useHomeTesting()

function handleViewPdf(pdfUrl: string) {
    if (!pdfUrl)
        return

    if (isIOS()) {
        // 添加返回事件监听
        const handleVisibilityChange = () => {
            if (document.visibilityState === 'visible') {
                setTimeout(() => {
                    window.dispatchEvent(new Event('resize'))
                }, 100)
                document.removeEventListener('visibilitychange', handleVisibilityChange)
            }
        }
        document.addEventListener('visibilitychange', handleVisibilityChange)

        navigateTo(`${window.location.origin}/api/v1/file/preview?objectName=${pdfUrl}`, {
            external: true,
        })
    } else {
        navigateTo(`/h5/pdf-preview/${pdfUrl}`)
    }
}
</script>

<template>
    <div class="relative">
        <div v-if="data?.results.length" style="background: linear-gradient(180deg, #DBFFFB 0%, #F4F5F7 100%);" class="h-[270px] absolute top-0 left-0 right-0">
        </div>

        <template v-if="data?.results.length">
            <div v-for="item in data?.results" :key="item.id" class="relative p-16px" @click="handleViewPdf(item.pdfUrl)">
                <div class="bg-white rd-10px px-16px py-12px">
                    <div class="flex justify-between items-center">
                        <div class="text-16px font-600">
                            {{ testItems.find(test => test.id === item.seqType)?.name || '未知检测' }}
                        </div>

                        <div v-if="item.status === 0" class="text-primary-6" @click="navigateTo('/h5/home-testing/send')">
                            立即寄送
                        </div>
                    </div>

                    <div class="flex justify-between items-center mt-8px">
                        <div v-if="item.status === 0" class="w-62px h-20px bg-t-3 rd-20px text-11px text-white flex items-center justify-center">
                            报告未出
                        </div>

                        <div v-else class="w-62px h-20px bg-primary-6 rd-20px text-11px text-white flex items-center justify-center">
                            报告完成
                        </div>

                        <div class="text-12px text-t-3">
                            采样时间：{{ dayjs(item.createTime).format('YYYY-MM-DD') }}
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <van-empty v-else description="暂无数据" class="relative top-100px" />
    </div>
</template>
