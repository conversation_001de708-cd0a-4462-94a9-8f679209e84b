<script setup lang="ts">
import type WXSDK from 'weixin-js-sdk'

definePageMeta({
    middleware: 'h5-auth',
})

useHead({
    title: '居家检测',
})

const { testItems } = useHomeTesting()

const route = useRoute()

const id = route.params.id

const itemDesc = computed(() => {
    return testItems.value.find(item => item.id === Number(id))?.desc
})

const scanResult = ref('')

let wx: typeof WXSDK | null = null

async function handleScan() {
    wx?.scanQRCode({
        needResult: 1,
        scanType: ['qrCode', 'barCode'],
        success: (result) => {
            console.log(result)
            scanResult.value = result.resultStr.split(',')[1] || ''
        },
        fail: (err) => {
            console.log(err)
        },
    })
}

async function handleCompleteInfo() {
    if (scanResult.value) {
        const { results } = await useWrapFetch<BaseResponse<boolean>>(`/gene-test/check-scode?scode=${scanResult.value}`)

        if (results) {
            navigateTo(`/h5/home-testing/${id}/survey?barcode=${scanResult.value}`)
        } else {
            showToast('请输入正确的条码')
        }
    } else {
        showToast('请先扫码绑定样本')
    }
}

function handleHistory() {
    navigateTo(`/h5/home-testing/${id}/history`)
}

onMounted(async () => {
    if (isIOS()) {
        const url = new URL(location.href)
        if (!url.searchParams.has('reloaded')) {
            url.searchParams.set('reloaded', '1')
            location.replace(`${url.pathname}?${url.searchParams.toString()}`)
        }
    }

    wx = await useWxBridge({
        jsApiList: ['scanQRCode'],
    })
})
</script>

<template>
    <div class="relative">
        <div style="background: linear-gradient(180deg, #DBFFFB 0%, #F4F5F7 100%);" class="h-[270px] absolute top-0 left-0 right-0">
        </div>

        <div class="relative px-20px py-16px">
            <div class="flex justify-between">
                <div class="font-fangyuan font-700 text-24px">
                    扫码<span class="text-primary-6">上传试剂样本</span>
                </div>

                <img src="@/assets/images/home-testing/report.png" alt="" srcset="" class="w-126px h-34px relative top-7px" />
            </div>

            <div class="font-fangyuan font-700 text-16px">
                {{ itemDesc }}，功能医学深度解读报告
            </div>

            <div class="bg-white rounded-10px p-16px mt-16px flex flex-col gap-16px">
                <div class="flex items-center gap-8px">
                    <div class="i-custom:home-testing-title w-12px h-12px"></div>

                    <div class="font-600">
                        扫码绑定您的试剂样本
                    </div>
                </div>

                <div class="flex justify-center flex-col gap-8px items-center">
                    <div class="i-custom:home-testing-scan w-80px h-80px" @click="handleScan"></div>

                    <van-button type="primary" round plain block @click="handleScan">
                        立即扫码绑定样本
                    </van-button>

                    <div class="w-full relative">
                        <input v-model="scanResult" type="text" border="1px solid #4E5969" class="h-43px w-full rd-25px pl-40px" placeholder="手动输入/修改条码" />

                        <div class="absolute left-16px top-50% -translate-y-50%">
                            <div class="i-custom:home-testing-input-scan w-20px h-20px"></div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="flex flex-col justify-center w-100% h-fit bg-white rounded-10px p-16px gap-8px mt-8px mb-16px">
                <div class="w-100% h-27px flex justify-center items-center gap-4px">
                    <div class="w-14px h-13px bg-[url('~/assets/images/external/report-camera.svg')] bg-no-repeat bg-center bg-[length:100%_100%]"></div>
                    <span class="font-600">扫码示例</span>
                </div>
                <span class="text-14px text-left text-#86909C">
                    请按下图所示扫描采样管上的条码
                </span>
                <div class="flex w-100% h-121px bg-#F2F4F7 rd-10px">
                    <div class="flex-1 flex flex-col items-center justify-center gap-4px">
                        <div class="flex items-center justify-center gap-4px">
                            <div class="i-custom:check-circle-fill w-16px h-16px"></div>
                            <span class="font-600">平整放置</span>
                        </div>
                        <div class="w-109px h-76px bg-[url('~/assets/images/home-testing/tube.png')] bg-no-repeat bg-center bg-[length:100%_100%]"></div>
                    </div>
                    <div class="flex-1 flex flex-col items-center justify-center gap-4px">
                        <div class="flex items-center justify-center gap-4px">
                            <div class="i-custom:check-circle-fill w-16px h-16px"></div> <span class="font-600">完整拍摄</span>
                        </div>
                        <div class="w-109px h-76px bg-[url('~/assets/images/home-testing/scan.png')] bg-no-repeat bg-center bg-[length:100%_100%]"></div>
                    </div>
                </div>
            </div>

            <div class="flex justify-center gap-16px mt-16px">
                <van-button round class="border-none! bg-fill-3! w-160px! h-50px! font-600!" @click="handleHistory">
                    历史上传
                </van-button>

                <van-button type="primary" round class="border-none! w-160px! h-50px! font-600!" @click="handleCompleteInfo">
                    去完善个人信息
                </van-button>
            </div>
        </div>
    </div>
</template>
