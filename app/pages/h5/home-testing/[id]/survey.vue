<script setup lang="ts">
import test1 from '@/assets/json/home-testing/1.json'
import test2 from '@/assets/json/home-testing/2.json'

definePageMeta({
    middleware: 'h5-auth',
})

useHead({
    title: '居家检测',
})

const route = useRoute()

const content = JSON.stringify(route.params.id === '1' ? test1 : test2)
const surveyRef = useTemplateRef('surveyRef')

const barcode = route.query.barcode as string

const { openId, phoneNumber } = useH5User()

async function handleSubmit(data: Record<string, string>) {
    const { closeLoading } = useLoading({
        message: '提交中...',
    })

    const processedData = Object.keys(data).reduce((acc, key) => {
        if (key.endsWith('-Comment')) {
            const newKey = key.replace('-Comment', '_remark')
            acc[newKey] = data[key]
        } else {
            acc[key] = data[key]
        }
        return acc
    }, {} as Record<string, any>)

    const { state } = await useWrapFetch<BaseResponse<string>>('/gene-test/program/submit-order', {
        method: 'POST',
        body: {
            seqType: Number(route.params.id),
            samId: barcode,
            questionnaireContent: JSON.stringify(processedData),
            openid: openId,
            phone: phoneNumber,
        },
    })

    closeLoading()

    if (state === 200) {
        navigateTo(`/h5/home-testing/send`, { replace: true })
        return
    } else if (state === 44001) {
        showFailToast('条码已被使用')
    } else {
        showFailToast('订单生成失败')
        // navigateTo(`/h5/home-testing`, { replace: true })
    }
    surveyRef.value?.renderSurvey(JSON.stringify(processedData))
}

onMounted(() => {
    const result = {
        样本编码: barcode,
    }
    surveyRef.value?.renderSurvey(JSON.stringify(result))
})

onBeforeRouteLeave(async (to, from, next) => {
    try {
        if (to.path !== '/h5/home-testing/send' && to.path !== '/h5/home-testing') {
            await showConfirmDialog({
                title: '您的订单未完成',
                message: '是否立即退出',
                closeOnPopstate: false,
                confirmButtonText: '继续填写',
                cancelButtonText: '确认退出',
            })

            next(false)
        } else {
            next()
        }
    } catch (error) {
        next()
    }
})
</script>

<template>
    <div h-full>
        <shared-survey
            ref="surveyRef" :content
            @submit="handleSubmit"
        />
    </div>
</template>
