<script setup lang="ts">
useHead({
    title: 'SLMC健康平台',
})

const route = useRoute()

const code = route.query.code
let openId = ''
const phoneNumber = ref('')
const showForm = ref(true)

async function auth() {
    const { closeLoading } = useLoading({
        message: '登录中...',
    })
    try {
        const { results } = await useWrapFetch<BaseResponse<{
            openId: string
        }>>(`/oauth2/access-token?code=${code}`)

        if (results) {
            openId = results.openId
            const { results: bindingResults } = await useWrapFetch<BaseResponse<{
                phoneNumber: string
            } | null>>(`/openid-phone-binding/by-openid/${openId}`)

            if (bindingResults?.phoneNumber) {
                const { openId: h5OpenId, phoneNumber: h5PhoneNumber } = storeToRefs(useH5User())
                h5OpenId.value = openId
                h5PhoneNumber.value = bindingResults.phoneNumber

                navigateTo('/h5/home-testing', { replace: true })
            } else {
                showForm.value = true
            }
        } else {
            navigateTo('/h5/login', { replace: true })
        }
    } catch (error) {
        navigateTo('/h5/login', { replace: true })
    } finally {
        closeLoading()
    }
}

async function handleBind() {
    const phoneReg = /^1[3-9]\d{9}$/
    if (!phoneReg.test(phoneNumber.value)) {
        showToast('请输入正确的手机号')
        return
    }

    const { openId: h5OpenId, phoneNumber: h5PhoneNumber } = storeToRefs(useH5User())

    const { results } = await useWrapFetch<BaseResponse<boolean>>(`/openid-phone-binding/bind`, {
        method: 'POST',
        body: {
            openId,
            phoneNumber: phoneNumber.value,
        },
    })

    if (results) {
        h5OpenId.value = openId
        h5PhoneNumber.value = phoneNumber.value
        showToast('注册成功')

        navigateTo('/h5/home-testing', { replace: true })
    } else {
        showToast('注册失败')
        navigateTo('/h5/login', { replace: true })
    }
}

onMounted(() => {
    if (!openId) {
        auth()
    }
})
</script>

<template>
    <div v-if="showForm" class="h-screen w-screen relative" style="background: linear-gradient(180deg, #EEFFFD 0%, #FAFFFF 100%);">
        <div class="circle"></div>

        <div class="flex flex-col items-center justify-between h-[calc(100vh-100px)] gap-16px pt-100px">
            <div class="flex flex-col items-center justify-center">
                <img src="@/assets/images/manager/register-bg-2.png" class="w-185px h-185px" alt="" srcset="" />
                <div class="text-primary-6 text-20px font-600 relative bottom-20px">
                    SLMC健康平台
                </div>

                <div class="relative">
                    <input v-model="phoneNumber" class="custom-input" placeholder="请输入手机号" />
                    <div class="absolute top-15px left-15px i-custom-h5-phone w-20px h-20px"></div>
                </div>

                <div class="relative mt-16px hidden">
                    <input class="custom-input" placeholder="验证码" />
                    <div class="absolute top-15px left-15px i-custom-h5-code w-20px h-20px"></div>
                    <div class="absolute top-15px right-15px text-primary-6 text-15px">
                        发送验证码
                    </div>
                </div>
            </div>

            <van-button round type="primary" class="w-284px h-50px flex-shrink-0" @click="handleBind">立即注册/登录</van-button>
        </div>
    </div>
</template>

<style scoped>
.custom-input {
    border: 1px solid #E5E5E5;
    --uno: h-50px w-295px rd-10px pl-40px;
}
</style>
