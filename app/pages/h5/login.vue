<script setup lang="ts">
useHead({
    title: 'SLMC健康平台',
})

definePageMeta({
    middleware: 'h5-auth',
    layout: false,
})

const config = useRuntimeConfig()
const REDIRECT_URI = `${window.location.origin}/h5/redirect`
const authUrl = `https://open.weixin.qq.com/connect/oauth2/authorize?appid=${config.public.appId}&redirect_uri=${encodeURIComponent(REDIRECT_URI)}&response_type=code&scope=snsapi_userinfo&#wechat_redirect`

const checked = ref(false)
</script>

<template>
    <div class="h-screen w-screen relative" style="background: linear-gradient(180deg, #EEFFFD 0%, #FAFFFF 100%);">
        <div class="circle"></div>

        <div class="flex flex-col items-center justify-between h-[calc(100vh-100px)] pt-100px">
            <div class="flex flex-col items-center justify-center">
                <img src="@/assets/images/manager/register-bg-2.png" class="w-185px h-185px" alt="" srcset="" />
                <div class="text-primary-6 text-20px font-600 relative bottom-20px">
                    SLMC健康平台
                </div>
            </div>

            <div class="flex flex-col items-center justify-center gap-8px">
                <nuxt-link :to="authUrl" external>
                    <van-button :disabled="!checked" round type="primary" class="w-284px h-50px">微信授权登录</van-button>
                </nuxt-link>
                <van-checkbox v-model="checked" mb-8px icon-size="18px">
                    <div text="12px center t-5">
                        我已阅读并同意《
                        <nuxt-link to="/agreements/privacy">
                            <span class="text-primary-6">SLMC隐私协议</span>
                        </nuxt-link>
                        》
                    </div>
                </van-checkbox>
            </div>
        </div>
    </div>
</template>

<style scoped>
.circle {
    background-image: url('@/assets/images/home-testing/circle.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    --uno: absolute top-0 left-0 w-264px h-148px;
}
</style>
