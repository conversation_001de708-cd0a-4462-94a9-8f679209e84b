<script setup lang="ts">
import destr from 'destr'

useHead({
    title: '分享',
})

async function init() {
    const { closeLoading } = useLoading()
    try {
        const wxShareData = destr<ShareData>(localStorage.getItem('wx_share_data') || '{}')
        const wx = await useWxBridge({
            jsApiList: ['updateAppMessageShareData'],
        })

        if (!wx)
            return

        wx.ready(() => {
            wx.updateAppMessageShareData({
                title: wxShareData.title!,
                desc: wxShareData.desc!,
                link: wxShareData.link!,
                imgUrl: wxShareData.imgUrl!,
                success: () => {
                    closeLoading()
                },
                fail: (error) => {
                    console.log(error)
                    showFailToast('分享失败')
                    closeLoading()
                },
            })
        })
    } catch (error) {
        showFailToast('分享失败')
        console.error(error)
    } finally {
        closeLoading()
    }
}

init()
</script>

<template>
    <van-overlay show>
        <div class="i-custom-share-arrow h-88px w-60px absolute right-20px">
        </div>

        <div flex="~ col" mt-70px justify-center items-center>
            <div flex items-center>
                <div text="18px #fff" font-500>
                    点击右上角
                </div>

                <span class="i-custom-blue-dots ml-4px w-34px h-16px"></span>
            </div>

            <div text="18px #fff" font-500>
                将问卷邀请分享给客户
            </div>
        </div>
    </van-overlay>
</template>
