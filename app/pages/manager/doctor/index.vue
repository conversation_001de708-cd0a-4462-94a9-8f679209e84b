<script setup lang="ts">
import dayjs from 'dayjs'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

const menuText = useRouteQuery<string>('menuText', '')
const searchValue = useRouteQuery<string>('memberName', '')
const skeletonLoading = ref<boolean>(false)
const originalSubordinateMembers = ref<any[]>([])

useHead({
    title: computed(() => `下级${menuText.value}`),
})

const handleSearch = useDebounceFn(() => {
    getSubordinateMembers()
}, 300)

async function getSubordinateMembers() {
    try {
        skeletonLoading.value = true
        const { results } = await useWrapFetch<BaseResponse<any[]>>('/api/operator/getSubordinateMembers', {
            method: 'GET',
            params: {
                searchContent: searchValue.value.trim(),
            },
        })
        originalSubordinateMembers.value = results || []
    } catch (error) {
        console.error(`获取${menuText.value}失败:`, error)
        originalSubordinateMembers.value = []
    } finally {
        skeletonLoading.value = false
    }
}

onMounted(() => {
    getSubordinateMembers()
})

function handleClearSearch() {
    searchValue.value = ''
    getSubordinateMembers()
}
</script>

<template>
    <div class="flex flex-col p-16px pb-0px h-100vh">
        <van-search
            v-model="searchValue"
            shape="round"
            clearable
            clear-trigger="always"
            placeholder="搜索用户名"
            background="#FFFFFF"
            @search="handleSearch"
            @clear="handleClearSearch"
            @input="handleSearch"
        />

        <div class="flex-1 h-calc(100vh - 68px) overflow-y-auto relative mt-24px">
            <van-skeleton v-if="skeletonLoading">
                <template #template>
                    <div class="flex flex-1">
                        <div :style="{ flex: 1 }">
                            <van-skeleton-paragraph v-for="i in 12" :key="i" class="h-50px! rd-10px" />
                        </div>
                    </div>
                </template>
            </van-skeleton>
            <template v-else>
                <template v-if="originalSubordinateMembers.length > 0">
                    <div
                        v-for="(item, index) in originalSubordinateMembers"
                        :key="index"
                        class="flex flex-col gap-4px"
                    >
                        <div class="flex flex-col">
                            <div class="flex items-center gap-8px">
                                <div class="w-32px h-32px i-custom-default-avatar rd-full border-1px border-solid border-[#F2F4F7] bg-[#6AD9CB] overflow-hidden"></div>

                                <div class="flex flex-1 items-center justify-between">
                                    <span class="text-15px text-[#1D2229]">{{ item.subordinateMemberName }}</span>
                                    <span class="text-12px text-[#868F9C]">{{ dayjs(item.firstJoinTime).format('YYYY-MM-DD') }} 加入</span>
                                </div>
                            </div>

                            <div class="flex justify-between items-center bg-[#F2F4F7] rd-10px px-12px py-4px mt-4px">
                                <span class="text-12px text-[#868F9C]">贡献积分：{{ item?.totalPoints || 0 }}</span>
                                <span class="text-12px text-[#868F9C]">订单数：{{ item?.orderCount || 0 }}</span>
                            </div>
                        </div>

                        <div v-if="index !== originalSubordinateMembers.length - 1" class="h-1px bg-[#F2F4F7] ml-57px mt-8px mb-8px"></div>
                    </div>
                </template>

                <van-empty v-else :description="`暂无下级${menuText}数据`" class="w-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
            </template>
        </div>
    </div>
</template>

<style lang="scss" scoped>
:deep(.van-search) {
    padding: 0;
}

:deep(.van-field__left-icon .van-icon) {
    color: #4E5969;
}
</style>
