<script setup lang="ts">
definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

useHead({
    title: '我的客户',
})

const searchValue = useRouteQuery<string>('clientName', '')
const skeletonLoading = ref<boolean>(false)
const originalClientList = ref<any[]>([])

const handleSearch = useDebounceFn(() => {
    getCustomerList()
}, 300)

async function getCustomerList() {
    try {
        skeletonLoading.value = true
        const { results } = await useWrapFetch<BaseResponse<any[]>>('/api/operator/customerList', {
            method: 'GET',
            query: {
                searchContent: searchValue.value.trim(),
            },
        })
        originalClientList.value = results || []
    } catch (error) {
        console.error('获取客户列表失败:', error)
        originalClientList.value = []
    } finally {
        skeletonLoading.value = false
    }
}

onMounted(() => {
    getCustomerList()
})

function handleClearSearch() {
    searchValue.value = ''
    getCustomerList()
}

function handleToClientDetail(item: any) {
    if (!item.customerId) return

    navigateTo(`/manager/client/detail?customerId=${item.customerId}${item?.age ? `&age=${item.age}` : ''}${item?.gender ? `&gender=${item.gender}` : ''}${item?.customerName ? `&customerName=${item.customerName}` : ''}`)
}
</script>

<template>
    <div class="flex flex-col p-16px pb-0px h-100vh">
        <van-search
            v-model="searchValue"
            shape="round"
            clearable
            clear-trigger="always"
            placeholder="搜索用户名"
            background="#FFFFFF"
            @search="handleSearch"
            @clear="handleClearSearch"
            @input="handleSearch"
        />

        <div
            class="flex-1 h-calc(100vh - 68px) overflow-y-auto relative mt-24px"
        >
            <van-skeleton v-if="skeletonLoading">
                <template #template>
                    <div class="flex flex-1">
                        <div :style="{ flex: 1 }">
                            <van-skeleton-paragraph v-for="i in 12" :key="i" class="h-50px! rd-10px" />
                        </div>
                    </div>
                </template>
            </van-skeleton>
            <template v-else>
                <template v-if="originalClientList.length > 0">
                    <div v-for="(item, index) in originalClientList" :key="index" class="flex flex-col" @click="handleToClientDetail(item)">
                        <div class="flex items-center gap-8px">
                            <div class="w-49px h-49px i-custom-default-avatar rd-full border-1px border-solid border-[#F2F4F7] bg-[#6AD9CB] overflow-hidden"></div>
                            <div class="flex flex-col gap-2px">
                                <span class="text-15px text-[#1D2229]">{{ item.customerName }}</span>
                                <div class="flex items-center gap-8px">
                                    <span class="text-12px text-[#868F9C]">{{ $dayjs(item.firstJoinTime).format('YYYY-MM-DD') }} 加入</span>
                                </div>
                            </div>

                            <div class="w-16px h-16px i-custom-arrow-right-grey ml-auto"></div>
                        </div>

                        <div v-if="index !== originalClientList.length - 1" class="h-1px bg-[#F2F4F7] ml-57px mt-8px mb-8px"></div>
                    </div>
                </template>

                <van-empty v-else description="暂无用户数据" class="w-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
            </template>
        </div>
    </div>
</template>

<style lang="scss" scoped>
:deep(.van-search) {
    padding: 0;
}

:deep(.van-field__left-icon .van-icon) {
    color: #4E5969;
}

.food-item {
    border-bottom: 1px solid var(--van-gray-3);
}

.food-item:last-child {
    border-bottom: none;
}
</style>
