<script setup lang="ts">
definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

const customerId = useRouteQuery<string>('customerId', '')
const age = useRouteQuery<string>('age', '')
const gender = useRouteQuery<string>('gender', '')
const customerName = useRouteQuery<string>('customerName', '')
const loading = ref<boolean>(false)

useHead({
    title: customerName.value || '客户详情',
})

const clientInfo = ref<any>({})

async function getCustomerConsumptionDetail() {
    if (!customerId.value) return

    try {
        loading.value = true
        const { results } = await useWrapFetch<BaseResponse<any>>('/api/operator/getCustomerConsumptionDetail', {
            method: 'GET',
            params: {
                customerId: customerId.value,
            },
        })
        clientInfo.value = results || {}
    } catch (error) {
        console.error('获取客户详情失败:', error)
        clientInfo.value = {}
    } finally {
        loading.value = false
    }
}

onMounted(() => {
    getCustomerConsumptionDetail()
})
</script>

<template>
    <div>
        <div class="w-full h-108px bg-client-detail px-16px pt-24px pb-20px">
            <div class="w-full h-full flex items-center gap-8px">
                <div class="w-64px h-64px i-custom-default-avatar rd-full border-1px border-solid border-[#F2F4F7] bg-[#6AD9CB] overflow-hidden"></div>

                <div class="flex-1 flex flex-col justify-center">
                    <div class="text-15px text-[#1D2229]">
                        {{ clientInfo.customerName || '-' }}
                    </div>
                    <div class="flex items-center gap-8px mt-2px">
                        <span v-if="gender" class="text-12px text-[#868F9C]">
                            {{ gender }}
                        </span>
                        <span v-if="age" class="text-12px text-[#868F9C]">
                            {{ age }}岁
                        </span>
                    </div>
                </div>

                <div class="text-12px text-[#4E5969]">
                    {{ $dayjs(clientInfo.firstJoinTime).format('YYYY-MM-DD') || '-' }} 加入
                </div>
            </div>
        </div>
        <div class="w-full pt-16px px-16px">
            <div class="w-full rd-10px bg-[#F2F4F7] py-4px flex">
                <div class="flex-1 flex items-center gap-4px pl-16px">
                    <span class="text-12px text-[#868F9C]">累计支出</span>
                    <span class="text-18px font-600 text-[#1D2229]">{{ clientInfo.totalSpending || 0 }}</span>
                </div>
                <div class="flex-1 flex items-center gap-4px pl-16px">
                    <span class="text-12px text-[#868F9C]">购买次数</span>
                    <span class="text-18px font-600 text-[#1D2229]">{{ clientInfo.totalPurchases || 0 }}</span>
                </div>
            </div>

            <div class="w-full mt-16px flex flex-col gap-12px">
                <template v-if="loading">
                    <div class="text-center py-20px text-[#868F9C]">加载中...</div>
                </template>
                <template v-else-if="clientInfo.consumptionDetails && clientInfo.consumptionDetails.length > 0">
                    <template v-for="(item, index) in clientInfo.consumptionDetails" :key="index">
                        <div class="w-full flex items-center">
                            <div class="flex flex-col justify-center">
                                <div class="text-14px font-600 text-[#1D2229]">
                                    {{ item.productName || '-' }}
                                </div>
                                <div class="text-12px text-[#868F9C]">
                                    {{ $dayjs(item.payTime).format('YYYY-MM-DD') || '-' }}
                                </div>
                            </div>
                        </div>
                        <div
                            v-if="index !== clientInfo.consumptionDetails.length - 1"
                            class="w-full h-1px bg-[#F2F4F7]"
                        >
                        </div>
                    </template>
                </template>
                <template v-else>
                    <van-empty description="暂无购买记录" />
                </template>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.bg-client-detail {
    background-image: url('@/assets/images/manager/bg-client-detail.png');
}
</style>
