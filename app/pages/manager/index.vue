<script setup lang="ts">
import iconGreen from '@/assets/images/manager/home/<USER>'
import iconBlue from '@/assets/images/manager/home/<USER>'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

const MENU_KEYS = {
    CLIENT: 'client',
    POINTS: 'points',
    SUBORDINATES: 'subordinates',
    PROFILE: 'profile',
} as const
const NOT_OPEN_TYPES = ['DOCTOR', 'NUTRITIONIST']
const OPERATION_TYPE_MAP = {
    OPERATION: '运营',
    DOCTOR: '医生',
    NUTRITIONIST: '营养师',
    OTHER: '运营', // 其他
} as const

const { reset } = useUserStore()
const { userInfo, token: userToken, role } = storeToRefs(useUserStore())
const showInvitePopup = ref<boolean>(false)
const currentInviteType = ref<string>('user')
const userInvitationState = ref<string>('')

const customAvatarUrl = ref<string>('')

const currentRoleText = computed(() =>
    OPERATION_TYPE_MAP[userInfo.value?.operationType as keyof typeof OPERATION_TYPE_MAP] || '-',
)

const isCurrentTypeOpen = computed(() =>
    !NOT_OPEN_TYPES.includes(userInfo.value?.operationType as string),
)

const inviteCardList = ref<any[]>([
    {
        type: 'user',
        iconClass: 'blue',
        icon: iconBlue,
        title: '用户码',
        desc: '邀请患者加入体重管理',
        buttonText: '立即邀请',
        buttonClass: '!bg-#c3eaf9 text-#009DD5',
        qrcodeData: '',
        isOpen: true,
    },
    {
        type: computed(() => userInfo.value?.operationType || ''),
        iconClass: 'green',
        icon: iconGreen,
        title: computed(() => `${currentRoleText.value}码`),
        desc: computed(() => `邀请更多${currentRoleText.value}人员参与`),
        buttonText: '立即邀请',
        buttonClass: '!bg-#c0f1ed text-#00AC97',
        qrcodeData: '',
        isOpen: isCurrentTypeOpen,
    },
])

const navigationMenus = ref<any[]>([
    {
        key: MENU_KEYS.CLIENT,
        icon: 'i-custom-client',
        text: '我的客户',
        url: '/manager/client',
        value: 0,
        unit: '人',
        isVisible: true,
    },
    {
        key: MENU_KEYS.POINTS,
        icon: 'i-custom-points',
        text: '我的积分',
        url: '/manager/point',
        value: 0,
        unit: '分',
        isVisible: true,
    },
    {
        key: MENU_KEYS.SUBORDINATES,
        icon: 'i-custom-second-doctor',
        text: computed(() => `下级${currentRoleText.value}`),
        url: computed(() => `/manager/doctor?menuText=${currentRoleText.value}`),
        value: 0,
        unit: '人',
        isVisible: isCurrentTypeOpen,
    },
    {
        key: MENU_KEYS.PROFILE,
        icon: 'i-custom-profile',
        text: '个人信息',
        url: '/pages/profile/index',
        value: 0,
        unit: '',
        isVisible: true,
    },
])

const menuDataMapping = {
    [MENU_KEYS.CLIENT]: 'userNumber',
    [MENU_KEYS.POINTS]: 'rewardPoints',
    [MENU_KEYS.SUBORDINATES]: 'childOperationStaffNumber',
} as const

function updateMenuValues(apiData: any) {
    navigationMenus.value.forEach((menu) => {
        const apiField = menuDataMapping[menu.key as keyof typeof menuDataMapping]
        if (apiField && apiData[apiField] !== undefined) {
            menu.value = apiData[apiField] || 0
        }
    })
}

function updateInviteCards(apiData: any) {
    inviteCardList.value.forEach((card) => {
        card.qrcodeData = card.type === 'user'
            ? apiData.userInvitationQrcodeData
            : apiData.operationInvitationQrcodeData
    })
}

const isLoading = ref(false)
async function getOperationStaffInfo() {
    isLoading.value = true
    try {
        const { results } = await useWrapFetch<BaseResponse<any>>('/api/operator/get-operation-staff-info', {
            method: 'GET',
        })

        if (results) {
            updateMenuValues(results)
            updateInviteCards(results)
            userInvitationState.value = results?.userInvitationState || ''
        }
    } catch (error) {
        console.error('获取用户信息失败:', error)
    } finally {
        isLoading.value = false
    }
}

const currentQrcodeData = computed(() => {
    const currentCard = inviteCardList.value.find(card => card.type === currentInviteType.value)
    return currentCard?.qrcodeData || ''
})

// 引入logo替换功能
const { processQRCodeWithLogo, isProcessing: isImageProcessing } = useImageOverlay()

// 使用ref存储处理后的二维码图片
const qrcodeImageUrl = ref<string>('')

// 监听二维码数据变化，自动处理logo替换
watch([currentQrcodeData, currentInviteType], async ([qrData, inviteType]) => {
    if (!qrData) {
        qrcodeImageUrl.value = ''
        return
    }

    // 原始二维码数据
    let baseQRCode = ''
    if (qrData.startsWith('data:image/')) {
        baseQRCode = qrData
    } else {
        baseQRCode = `data:image/png;base64,${qrData}`
    }

    // 如果有自定义头像，进行logo替换
    if (customAvatarUrl.value) {
        try {
            const processedImage = await processQRCodeWithLogo(baseQRCode, {
                customLogoPath: customAvatarUrl.value,
                cacheKey: `${inviteType}_${qrData.slice(0, 50)}`,
            })
            qrcodeImageUrl.value = processedImage
        } catch (error) {
            console.error('Logo替换失败，使用原始二维码:', error)
            qrcodeImageUrl.value = baseQRCode
        }
    } else {
        // 没有自定义头像，直接显示原始二维码
        qrcodeImageUrl.value = baseQRCode
    }
}, { immediate: true })

function handleInviteAction(card: any) {
    currentInviteType.value = card.type
    if (!card.isOpen) {
        showFailToast('暂未开放')
        return
    }
    showInvitePopup.value = true
}

async function handleSwitchClient() {
    const phone = userInfo.value?.phone
    const state = userInvitationState.value
    const wx = await useWxBridge({})
    if (!phone || !state) {
        throw new Error('缺少参数')
    }

    try {
        const { results } = await useWrapFetch<BaseResponse<UserResponse>>('/open-api/v1/wx/phone/callback', {
            params: {
                phone,
                state,
            },
        })

        if (results) {
            if (results.status === 'init') {
                reset()
                sessionStorage.setItem('token', results.token)
                sessionStorage.setItem('headImage', results.wxOAuth2UserInfo.headImgUrl)
                sessionStorage.setItem('phone', phone)
                sessionStorage.setItem('state', state)
                sessionStorage.setItem('nickName', results.wxOAuth2UserInfo.nickname)
                sessionStorage.setItem('teamId', String(results.teamId) || '')
                navigateTo('/user/survey/consent?register=true')
            } // 已注册，直接登录
            else if (results.status === 'login') {
                reset()
                userInfo.value = {
                    headImage: results.wxOAuth2UserInfo.headImgUrl,
                    nickName: results.wxOAuth2UserInfo.nickname,
                    name: results.name,
                    phone: results.phone,
                    idCard: results.idCard,
                    duties: results.duties,
                    teamId: results.teamId,
                    gender: results.sex,
                    age: results.age,
                    operationType: results.operationType,
                }
                userToken.value = results.token
                role.value = results.role

                if (results.role === 'user') {
                    let redirectUrl = ''
                    // 问卷未完成，跳转问卷
                    if (results.questionIsFinished === 0) {
                        redirectUrl = `/user/survey/consent?surveyId=${results.questionId}&questionType=PRELIMINARY_EVALUATION`
                    }
                    // 问卷已完成，跳转打卡
                    else {
                        redirectUrl = `/user/checkin`
                    }

                    const dataToBase64 = encodeMessage({
                        type: 'user:login',
                        redirectTo: redirectUrl,
                        data: 'login',
                        userStore: userInfo.value,
                        token: results.token,
                    })

                    wx?.miniProgram.redirectTo({
                        url: `/pages/index/index?message=${dataToBase64}`,
                    })
                } else {
                    throw new Error('非用户角色')
                }
            } else {
                throw new Error('不是初始化或登录状态')
            }
        }
    } catch (error) {
        console.error('切换客户端失败:', error)
    }
}

async function handleNavigateToMenu(menu: any) {
    if (menu.key === MENU_KEYS.PROFILE) {
        const wx = await useWxBridge({})
        wx?.miniProgram.redirectTo({
            url: menu.url,
        })
    } else {
        navigateTo(menu.url as string)
    }
}

onMounted(() => {
    getOperationStaffInfo()
})
</script>

<template>
    <div class="relative p-16px flex flex-col justify-between h-100vh">
        <div style="background: linear-gradient(180deg, #E8FFFC 0%, #FFFFFF 100%);" class="h-187px w-375px absolute top-0 left-0"></div>

        <div class="flex flex-col gap-24px">
            <div class="relative flex justify-between top-16px">
                <div v-for="card in inviteCardList" :key="card.iconClass" :class="card.iconClass" class="relative w-165px h-172px">
                    <img :src="card.icon" class="absolute left-10px w-135px h-135px -top-22px -left-15px" alt="" srcset="" />
                    <div class="mt-80px ml-16px">
                        <div class="text-t-1 font-600 text-20px">
                            {{ card.title }}
                        </div>

                        <div class="text-t-1 text-12px mb-12px">
                            {{ card.desc }}
                        </div>

                        <button
                            round class="!w-74px !h-22px rd-20px lh-22px" :class="card.buttonClass"
                            @click="handleInviteAction(card)"
                        >
                            {{ card.buttonText }}
                        </button>
                    </div>
                </div>
            </div>

            <div class="flex flex-col gap-12px mt-12px">
                <div
                    v-for="menu in navigationMenus.filter(menu =>
                        typeof menu.isVisible === 'boolean' ? menu.isVisible : menu.isVisible.value,
                    )"
                    :key="menu.key"
                    class="flex items-center gap-16px px-16px h-52px bg-#F2F4F7 rd-10px"
                    @click="handleNavigateToMenu(menu)"
                >
                    <div class="flex items-center gap-8px flex-1">
                        <div class="flex justify-center items-center pr-4px">
                            <div class="w-26px h-26px" :class="menu.icon">
                            </div>
                        </div>
                        <div class="text-15px text-#1D2129 font-400 tracking-4%">{{ menu.text }}</div>
                    </div>
                    <van-loading v-if="isLoading" size="20" color="#00AC97" />
                    <div v-else class="flex items-center gap-4px">
                        <div class="text-15px text-#4E5969">{{ `${menu.value}${menu.unit}` }}</div>
                        <div class="i-custom-arrow-right-grey w-16px h-16px"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="w-full flex justify-center items-center mb-24px">
            <van-button
                round class="!w-295px !h-50px !text-primary-6" color="#E4FAF9"
                @click="handleSwitchClient"
            >
                切换客户端
            </van-button>
        </div>

        <van-popup
            v-model:show="showInvitePopup"
            round
        >
            <div
                class="w-311px h-450px relative pt-12px no-select"
                :style="{ background: `linear-gradient(180deg, ${currentInviteType === 'user' ? '#00ABE9' : '#00C2AA'} 64.42%, #F2F4F7 64.43%)` }"
            >
                <div class="w-full h-27px lh-27px relative">
                    <div class="text-15px text-#FFFFFF font-600 absolute top-0 left-1/2 -translate-x-1/2">{{ currentInviteType === 'user' ? 'SLMC ' : currentRoleText }}邀请码</div>
                    <div
                        class="i-custom-btn-close w-10px h-10px absolute top-1/2 right-12px -translate-y-1/2 cursor-pointer"
                        @click="showInvitePopup = false"
                    >
                    </div>
                </div>
                <div class="w-full flex justify-center items-center flex-col">
                    <img src="@/assets/images/manager/bg-invite.png" class="w-222px h-154px" alt="" srcset="" />
                    <div class="w-253px h-253px bg-#FFFFFF rd-10px p-20px -mt-54px">
                        <div class="w-full h-full flex items-center justify-center">
                            <img
                                v-if="qrcodeImageUrl && !isImageProcessing"
                                :src="qrcodeImageUrl"
                                class="w-full h-full object-contain"
                                alt="邀请二维码"
                            />
                            <shared-unified-loading v-else text="邀请二维码加载中..." />
                        </div>
                    </div>
                </div>
                <div class="w-full text-15px text-#4E5969 text-center mt-14px">长按保存二维码</div>
            </div>
        </van-popup>
    </div>
</template>

<style scoped>
.green {
    background-image: url('@/assets/images/manager/home/<USER>');
    background-size: 100% 100%;
}

.blue {
    background-image: url('@/assets/images/manager/home/<USER>');
    background-size: 100% 100%;
}

.no-select {
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}
</style>
