<script setup lang="ts">
import dayjs from 'dayjs'

definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

useHead({
    title: '我的积分',
})

const pointData = ref<any>({
    totalPoints: 0,
    currentMonthPoints: 0,
    primaryPoints: [],
    secondaryPoints: [],
    tertiaryPoints: [],
})

const pointList = computed(() => {
    const allRecords = [
        ...pointData.value.primaryPoints,
        ...pointData.value.secondaryPoints,
        ...pointData.value.tertiaryPoints,
    ]

    const groupedByDate = allRecords.reduce((groups: Record<string, any[]>, record) => {
        const date = dayjs(record.payTime).format('YYYY年M月D日')
        if (!groups[date]) {
            groups[date] = []
        }
        groups[date].push(record)
        return groups
    }, {})

    return Object.entries(groupedByDate)
        .map(([date, records]) => ({ date, records }))
        .sort((a, b) => dayjs(b.date, 'YYYY年M月D日').valueOf() - dayjs(a.date, 'YYYY年M月D日').valueOf())
})

function formatNumber(num: number): string {
    return new Intl.NumberFormat('en-US').format(num)
}

async function getMyPointDetail() {
    const { closeLoading } = useLoading()
    try {
        const { results } = await useWrapFetch<BaseResponse<any>>('/api/operator/getMyPointDetail', {
            method: 'GET',
        })

        if (results) {
            pointData.value = results
        }
    } catch (error) {
        console.error('获取积分详情失败:', error)
    } finally {
        closeLoading()
    }
}

onMounted(() => {
    getMyPointDetail()
})
</script>

<template>
    <div>
        <div class="w-full h-120px bg-client-detail pl-16px pt-24px pr-20px">
            <div class="h-23px flex items-center gap-7px">
                <img src="@/assets/images/manager/star-point.png" class="w-20px h-20px" />
                <div class="text-#69604E text-13px">可用积分</div>
            </div>
            <div class="w-full h-48px flex justify-between items-center">
                <div class="text-32px font-700 font-ddinpro text-#1D2229">
                    {{ formatNumber(pointData.totalPoints) }}
                </div>
                <div class="flex gap-18px items-center">
                    <div class="flex flex-col text-center">
                        <div class="text-#69604E text-13px">本月收入</div>
                        <div class="text-#1D2229 text-16px font-700 font-ddinpro">
                            {{ formatNumber(pointData.currentMonthPoints) }}
                        </div>
                    </div>
                    <div class="flex flex-col text-center">
                        <div class="text-#69604E text-13px">本月支出</div>
                        <div class="text-#1D2229 text-16px font-700 font-ddinpro">
                            -
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="w-full pt-16px px-16px">
            <template v-if="pointList.length > 0">
                <div class="flex flex-col gap-24px">
                    <div v-for="dayGroup in pointList" :key="dayGroup.date">
                        <div class="w-full h-25px text-14px text-t-3 mb-8px lh-25px">
                            {{ dayGroup.date }}
                        </div>
                        <div class="flex flex-col gap-12px w-343px">
                            <div
                                v-for="(item, index) in dayGroup.records"
                                :key="item.recordId"
                                class="flex flex-col gap-4px"
                            >
                                <div class="flex items-center gap-8px">
                                    <div class="w-32px h-32px i-custom-default-avatar rd-full border-1px border-solid border-[#F2F4F7] bg-[#6AD9CB] overflow-hidden"></div>
                                    <div class="flex flex-1 items-center justify-between">
                                        <span class="text-15px text-[#1D2229]">{{ item.customerName }}</span>
                                        <span class="text-15px text-[#F98804] font-600">+ {{ item.points }}</span>
                                    </div>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span class="text-12px text-[#868F9C]">{{ item.productName }}</span>
                                    <span class="text-12px text-[#868F9C]">{{ dayjs(item.payTime).format('YYYY-MM-DD') }}</span>
                                </div>
                                <div
                                    v-if="index !== dayGroup.records.length - 1"
                                    class="h-1px bg-[#F2F4F7] ml-57px"
                                >
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </template>

            <template v-else>
                <van-empty description="暂无积分记录" class="w-full absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2" />
            </template>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.bg-client-detail {
    background-image: url('@/assets/images/manager/bg-point.png');
}
</style>
