<script setup lang="ts">
definePageMeta({
    meta: {
        layout: {
            customBg: 'bg-white',
        },
    },
})

useHead({
    title: '运营中心',
})

const phone = useRoute().query.phone as string

const form = ref({
    name: '',
    type: '',
    phone,
    email: '',
    institution: '',
    region: [],
})

const checked = ref(false)

const roleOptions = [
    { text: '运营', value: 'OPERATION' },
    { text: '医生', value: 'DOCTOR' },
    { text: '营养师', value: 'NUTRITIONIST' },
    { text: '其他', value: 'OTHER' },
]

async function handleRegister() {
    try {
        if (!form.value.name) {
            showToast('请输入姓名')
            return
        }

        if (!form.value.type) {
            showToast('请选择用户类型')
            return
        }

        if (!form.value.phone) {
            showToast('请输入联系电话')
            return
        }

        const { results } = await useWrapFetch<BaseResponse<any>>('/operator/renew-info', {
            method: 'POST',
            body: {
                name: form.value.name,
                type: roleOptions.find(item => item.text === form.value.type)?.value,
                phone: form.value.phone,
                email: form.value.email,
                institution: form.value.institution,
                region: form.value.region.join(','),
            },
        })

        if (results) {
            const { userInfo, token: userToken, role: _role } = storeToRefs(useUserStore())

            userInfo.value = {
                headImage: results.headImgUrl,
                nickName: results.nickname,
                phone: results.phone,
                name: results.name,
                idCard: results.idCard,
                duties: results.duties,
                teamId: results.teamId,
                operationType: results.operationType,
            }

            userToken.value = results.token
            _role.value = 'manager'

            const wx = await useWxBridge({})

            const dataToBase64 = encodeMessage({
                type: 'manager:login',
                data: 'login',
                userStore: userInfo.value,
                token: results.token,
            })

            wx?.miniProgram.redirectTo({
                url: `/pages/index/index?message=${dataToBase64}`,
            })
        } else {
            showToast('注册失败')
        }
    } catch (error) {
        console.error(error)
        showToast('注册失败')
    }
}
</script>

<template>
    <div class="relative">
        <img src="@/assets/images/manager/register-bg.png" class="w-full absolute top-0 left-0" alt="" srcset="" />

        <img src="@/assets/images/manager/register-bg-2.png" class="absolute top-20px right-0 w-145px h-145px" alt="" srcset="" />
        <div class="absolute top-34px w-full h-full px-20px">
            <div class="font-600 text-20px text-#333">欢迎加入</div>
            <div class="font-600 text-20px text-primary-6">SLMC管理平台</div>
        </div>

        <div class="bg-#fff/80 z-100 relative top-120px rd-t-10px px-20px pt-16px flex flex-col gap-16px">
            <manager-form-field v-model="form.name" type="input" label="姓名" placeholder="请输入您的用户名" />
            <manager-form-field v-model="form.type" type="select" label="用户类型" placeholder="请选择用户类型" :options="roleOptions" />
            <manager-form-field v-model="form.phone" type="input" input-type="tel" label="联系电话" placeholder="请输入您的手机号" />
            <manager-form-field v-model="form.email" type="input" label="联系邮箱（选填）" placeholder="请输入您的邮箱" />
            <manager-form-field v-model="form.institution" type="input" label="所属机构（选填）" placeholder="请输入所属机构" />
            <manager-form-field v-model:multi-area-value="form.region" type="area" label="主要地区（选填）" placeholder="请选择区域" />

            <div class="flex flex-col items-center gap-10px justify-center my-20px">
                <van-checkbox v-model="checked" icon-size="16px">
                    <span class="text-12px text-t-5">我已阅读并同意</span><span class="text-primary-6 text-12px" @click.stop="() => { navigateTo('/agreements/privacy') }">《SLMC隐私协议》</span>
                </van-checkbox>

                <van-button type="primary" class="w-full" :disabled="!checked" round @click="handleRegister">
                    立即注册
                </van-button>
            </div>
        </div>
    </div>
</template>
