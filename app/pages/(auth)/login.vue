<script setup lang="ts">
onMounted(async () => {
    try {
        const wx = await useWxBridge({})

        const dataToBase64 = encodeMessage({
            type: 'logout',
            data: 'logout',
        })

        wx?.miniProgram.redirectTo({
            url: `/pages/index/index?message=${dataToBase64}`,
        })
    } catch (error) {
        console.log(error)
    }
})
</script>

<template>
    <base-suspense status="pending" />
</template>
