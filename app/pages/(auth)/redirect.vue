<script setup lang="ts">
const route = useRoute()

const code = route.query.code as string
const state = route.query.state as string

sessionStorage.setItem('state', state)

const { userInfo, token: userToken, role } = storeToRefs(useUserStore())

async function init() {
    const { closeLoading } = useLoading()
    try {
        const { results, state: responseState } = await useWrapFetch<BaseResponse<UserResponse>>('/open-api/v1/wx/callback', {
            params: {
                code,
                state,
            },
        })

        // 选择角色
        if (responseState === 302) {
            navigateTo(`/choose-role?openid=${(results as unknown as string)}`, { replace: true })
        }

        if (results) {
            // 初始化 跳转注册页
            if (results.status === 'init') {
                sessionStorage.setItem('token', results.token)

                sessionStorage.setItem('headImage', results.wxOAuth2UserInfo.headImgUrl)
                sessionStorage.setItem('nickName', results.wxOAuth2UserInfo.nickname)
                sessionStorage.setItem('teamId', String(results.teamId) || '')
                if (results.role === 'user')
                    navigateTo('/user-register')

                if (results.role === 'manager')
                    navigateTo('/manager-register')
            }
            // 已注册，直接登录
            else if (results.status === 'login') {
                userInfo.value = {
                    headImage: results.wxOAuth2UserInfo.headImgUrl,
                    nickName: results.wxOAuth2UserInfo.nickname,
                    name: results.name,
                    phone: results.phone,
                    idCard: results.idCard,
                    duties: results.duties,
                    teamId: results.teamId,
                }
                userToken.value = results.token
                role.value = results.role

                if (results.role === 'user')
                    navigateTo('/user', { replace: true })

                if (results.role === 'manager')
                    navigateTo('/manager', { replace: true })
            }
        }
    } catch (error) {
        console.log(error)
        const token = sessionStorage.getItem('token')
        if (token)
            navigateTo('/user-register')
    } finally {
        closeLoading()
    }
}

init()
</script>
