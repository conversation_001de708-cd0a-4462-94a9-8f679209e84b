<script setup lang="ts">
import archiveSurveyContent from '@/utils/survey/archive'

const token = sessionStorage.getItem('token')!
const state = sessionStorage.getItem('state')
const phone = sessionStorage.getItem('phone') || ''
const doctorId = sessionStorage.getItem('doctorId') || ''
const surveyRef = useTemplateRef('surveyRef')
const { withSubmitLock, resetSubmitLock } = useSubmitLock()

let questionTitle = ''
const surveyContent = ref('')

useHead({
    title: '开始评估',
})

async function getSurvey() {
    try {
        if (state) {
            const { results } = await useWrapFetch<BaseResponse<QuestionMeta>>('/user/question', {
                params: {
                    state,
                },
            })

            const parsed = JSON.parse(results.content)

            questionTitle = results.title
            surveyContent.value = JSON.stringify(parsed)

            await nextTick()

            surveyRef.value?.renderSurvey()
        } else {
            showFailToast('缺少state参数')
            // navigateTo('/logout')
        }
    } catch (error) {
        console.log(error)
    }
}

getSurvey()

async function handleSubmit(questionData: Record<string, string>) {
    await withSubmitLock(async () => {
        const [, question] = await Promise.all([
            useWrapFetch('/user/preliminary-archive', {
                method: 'post',
                body: {
                    name: questionData.name,
                    archiveHeight: questionData.height || '',
                    archiveWeight: questionData.weight || '',
                    archiveBmi: questionData.bmi || '',
                    waist: questionData.waist || '',
                    sex: questionData.gender,
                    age: questionData.age || '',
                    fatRate: questionData.fat_rate || '',
                },
            }),
            useWrapFetch<BaseResponse<number>>('/user/question', {
                method: 'post',
                body: {
                    state,
                    questionTitle,
                    questionResult: JSON.stringify(questionData),
                },
            }),
        ])

        const { userInfo, token: _token, role } = storeToRefs(useUserStore())

        const headImage = sessionStorage.getItem('headImage')
        const nickName = sessionStorage.getItem('nickName')

        userInfo.value = {
            headImage: headImage || '',
            nickName: nickName || '',
            name: questionData.name!,
            idCard: '',
            phone,
            gender: questionData.gender,
            age: questionData.age as unknown as number,
            teamId: null,
            duties: '',
        }

        _token.value = token
        role.value = 'user'

        const wx = await useWxBridge({})

        const dataToBase64 = encodeMessage({
            type: 'user:login',
            redirectTo: `/user/external/report-uploader`,
            data: 'login',
            userStore: userInfo.value,
            token,
        })

        wx?.miniProgram.redirectTo({
            url: `/pages/index/index?message=${dataToBase64}`,
        })

        // navigateTo(`/user/assessment/${question.results}/evaluate?type=PRELIMINARY_EVALUATION`, { replace: true })
    }).catch((error) => {
        console.error(error)
        resetSubmitLock()
    })
}

onMounted(() => {
    toggleTheme('user')
})

// const isOverlayShow = ref(true)
</script>

<template>
    <div>
        <shared-survey ref="surveyRef" :content="surveyContent" @submit="handleSubmit" />
        <!-- <user-invite-overlay v-model="isOverlayShow" /> -->
    </div>
</template>
