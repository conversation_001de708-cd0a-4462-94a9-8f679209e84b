<script setup lang="ts">
import archiveSurveyContent from '@/utils/survey/archive'

const token = sessionStorage.getItem('token')!
const state = sessionStorage.getItem('state')
const phone = sessionStorage.getItem('phone') || ''
const doctorId = sessionStorage.getItem('doctorId') || ''
const surveyRef = useTemplateRef('surveyRef')
const { withSubmitLock, resetSubmitLock } = useSubmitLock()

let questionTitle = ''
const surveyContent = ref('')

useHead({
    title: '开始建档',
})

async function getSurvey() {
    try {
        if (state) {
            const { results } = await useWrapFetch<BaseResponse<QuestionMeta>>('/user/question', {
                params: {
                    state,
                },
            })

            const parsed = JSON.parse(results.content)

            // 有noId字段，不用填身份证
            const decodeState = JSON.parse(window.atob(state))
            if (!decodeState.noId) {
                parsed.pages.unshift(archiveSurveyContent.pages[1])
            }

            parsed.pages.unshift(archiveSurveyContent.pages[0])

            questionTitle = results.title
            surveyContent.value = JSON.stringify(parsed)

            await nextTick()

            surveyRef.value?.renderSurvey()
        } else {
            showFailToast('缺少state参数')
            navigateTo('/logout')
        }
    } catch (error) {

    }
}

getSurvey()

async function handleSubmit(data: Record<string, string>) {
    await withSubmitLock(async () => {
        const archiveData: Record<string, string> = {}
        const questionData: Record<string, string> = {}
        Object.entries(data).forEach(([key, value]) => {
            if (key.startsWith('archive_')) {
                archiveData[key] = value
            } else {
                questionData[key] = value
            }
        })

        const name = archiveData.archive_name!
        const idCard = archiveData.archive_idCard!

        const [, question] = await Promise.all([
            useWrapFetch('/user/preliminary-archive', {
                method: 'post',
                body: {
                    idCard,
                    name,
                    archiveHeight: questionData['3'] || '',
                    archiveWeight: questionData['4'] || '',
                    archiveBmi: questionData['5'] || '',
                    archiveIdCard: idCard,
                    sex: questionData['1'] === '1' ? '男' : '女',
                    age: questionData['2'] || '',
                },
            }),
            useWrapFetch<BaseResponse<number>>('/user/question', {
                method: 'post',
                body: {
                    state,
                    questionTitle,
                    questionResult: JSON.stringify(questionData),
                },
            }),
        ])

        const { userInfo, token: _token, role } = storeToRefs(useUserStore())

        const headImage = sessionStorage.getItem('headImage')
        const nickName = sessionStorage.getItem('nickName')

        userInfo.value = {
            headImage: headImage || '',
            nickName: nickName || '',
            name,
            idCard,
            duties: archiveData.archive_duties!,
            phone,
            teamId: null,
        }

        _token.value = token
        role.value = 'user'

        const wx = await useWxBridge({})

        const dataToBase64 = encodeMessage({
            type: 'user:login',
            redirectTo: `/user/assessment/${question.results}/evaluate?type=PRELIMINARY_EVALUATION&doctorId=${doctorId}`,
            data: 'login',
            userStore: userInfo.value,
            token,
        })

        wx?.miniProgram.redirectTo({
            url: `/pages/index/index?message=${dataToBase64}`,
        })
        // navigateTo(`/user/assessment/${question.results}/evaluate?type=PRELIMINARY_EVALUATION`, { replace: true })
        // 成功后不解锁，避免重复提交；失败会在 catch 中解锁
    }).catch((error) => {
        console.error(error)
        resetSubmitLock()
    })
}

onMounted(() => {
    toggleTheme('user')
})

// const isOverlayShow = ref(true)
</script>

<template>
    <div>
        <shared-survey ref="surveyRef" :content="surveyContent" @submit="handleSubmit" />
        <!-- <user-invite-overlay v-model="isOverlayShow" /> -->
    </div>
</template>
