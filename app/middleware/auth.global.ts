export default defineNuxtRouteMiddleware(async (to, from) => {
    const { token, role } = useUserStore()

    if (to.path.startsWith('/agreements') || to.path.startsWith('/h5')) {
        return
    }

    const noNeedAuthPaths = [
        '/login',
        '/logout',
        '/mp-redirect',
        '/redirect',
        '/choose-role',
        '/user-register',
        '/manager-register',
        '/user/survey/consent',
        '/user/test',
        '/external-user-register',
    ]

    if (!noNeedAuthPaths.includes(to.path)) {
        if (!token) {
            return navigateTo('/logout')
        }
        else {
            // 防止不同角色进入对方的页面
            if (role === 'manager' && to.path.startsWith('/user')) {
                return navigateTo('/manager')
            } else if (role === 'user' && to.path.startsWith('/manager')) {
                return navigateTo('/user/checkin')
            }
        }
    }

    if (noNeedAuthPaths.includes(to.path) && token && !['/user/survey/consent', '/mp-redirect', '/logout'].includes(to.path)) {
        return navigateTo(role === 'manager' ? '/manager' : '/user/checkin')
    }
})
