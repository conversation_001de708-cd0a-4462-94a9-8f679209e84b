{"compilerOptions": {"target": "es2017", "jsx": "preserve", "experimentalDecorators": true, "baseUrl": ".", "rootDir": ".", "module": "commonjs", "moduleResolution": "node", "paths": {"@/*": ["./src/*"]}, "resolveJsonModule": true, "typeRoots": ["node_modules/@types"], "allowJs": true, "strictNullChecks": true, "noImplicitAny": false, "noUnusedLocals": true, "noUnusedParameters": true, "outDir": "lib", "preserveConstEnums": true, "removeComments": false, "sourceMap": true, "allowSyntheticDefaultImports": true}, "include": ["./src", "./types", "components.d.ts"], "compileOnSave": false}