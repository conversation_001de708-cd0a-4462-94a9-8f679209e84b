FROM node:23-alpine

# Jenkins 参数定义
ARG NUXT_PUBLIC_APP_ID
ARG BRANCH_NAME

# 环境变量设置
ENV NUXT_PUBLIC_APP_ID=$NUXT_PUBLIC_APP_ID
ENV NUXT_PUBLIC_BRANCH=$BRANCH_NAME
ENV BRANCH_NAME=$BRANCH_NAME

# Install git
RUN sed -i 's/dl-cdn.alpinelinux.org/mirrors.aliyun.com/g' /etc/apk/repositories
RUN apk add git

WORKDIR /app

COPY package.json pnpm-lock.yaml .npmrc ./
RUN npm config set registry http://registry.npmmirror.com
RUN npm install -g pnpm
RUN pnpm install

COPY . .

# 根据BRANCH_NAME动态选择JSON文件
RUN if [ "$BRANCH_NAME" = "main" ]; then \
        echo "使用生产环境配置: plan-detail.prod.json"; \
        cp temp/plan-detail.prod.json public/no-cache/plan-detail.json; \
    else \
        echo "使用开发环境配置: plan-detail.dev.json"; \
        cp temp/plan-detail.dev.json public/no-cache/plan-detail.json; \
    fi

EXPOSE 3000

RUN pnpm build

CMD [ "pnpm", "start" ]
