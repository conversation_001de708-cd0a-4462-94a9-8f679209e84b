<script setup lang="ts">
import { computed } from 'vue'

export interface LoadingProps {
  size?: 'small' | 'medium' | 'large'
  rainbow?: boolean
  text?: string
  vertical?: boolean
}

const {
  size = 'large',
  rainbow = true,
  text = '',
  vertical = true,
} = defineProps<LoadingProps>()

const sizeConfig = computed(() => {
  const configs = {
    small: { container: '20px', square: '16px', dotSize: '5px', border: '1.5px', margin: '2.5px', borderExpanded: '2.5px' },
    medium: { container: '32px', square: '16px', dotSize: '8px', border: '2.5px', margin: '4px', borderExpanded: '4px' },
    large: { container: '40px', square: '20px', dotSize: '10px', border: '3px', margin: '5px', borderExpanded: '5px' },
  }
  return configs[size]
})
</script>

<template>
  <div class="flex justify-center items-center">
    <div
      class="loading-container"
      :class="[
        vertical ? 'flex-col' : 'flex-row',
        rainbow ? 'rainbow-root' : '',
      ]"
    >
      <div
        class="loading-animation"
        :style="{ width: sizeConfig.container, height: sizeConfig.container }"
      >
        <div
          v-for="i in 4"
          :key="i"
          class="loading-square"
          :class="`square-${i}`"
          :style="{
            'width': sizeConfig.square,
            'height': sizeConfig.square,
            '--dot-size': sizeConfig.dotSize,
            '--dot-border': sizeConfig.border,
            '--dot-margin': sizeConfig.margin,
            '--dot-border-expanded': sizeConfig.borderExpanded,
          }"
        />
      </div>

      <div
        v-if="text"
        class="loading-text"
        :class="[
          vertical ? 'mt-3' : 'ml-3',
          rainbow ? 'rainbow-text' : 'text-gray-600',
        ]"
      >
        {{ text }}
      </div>
    </div>
  </div>
</template>

<style scoped>
@keyframes rainbow {
  0% { --rainbow-color: #00a98e; }
  12.5% { --rainbow-color: #1ba3d6; }
  25% { --rainbow-color: #4f8bff; }
  37.5% { --rainbow-color: #8b5cf6; }
  50% { --rainbow-color: #e879f9; }
  62.5% { --rainbow-color: #f43f5e; }
  75% { --rainbow-color: #fb923c; }
  87.5% { --rainbow-color: #eab308; }
  100% { --rainbow-color: #00a98e; }
}

@keyframes square-to-dot-animation {
  15%, 25% {
    width: 0;
    height: 0;
    margin: var(--dot-margin);
    border-width: var(--dot-border-expanded);
    border-radius: 100%;
  }
  40% {
    width: var(--dot-size);
    height: var(--dot-size);
    margin: initial;
    border-width: var(--dot-border);
    border-radius: 15%;
  }
}

.rainbow-root {
  --rainbow-color: #00a98e;
  animation: rainbow 8s linear infinite;
  color: var(--rainbow-color);
}

.rainbow-text {
  color: var(--rainbow-color);
}

.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-animation {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  justify-content: center;
}

.loading-square {
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-square::before {
  box-sizing: content-box;
  width: var(--dot-size);
  height: var(--dot-size);
  content: "";
  border: var(--dot-border) solid var(--rainbow-color, #00AC97);
  border-radius: 15%;
  animation: square-to-dot-animation 2s linear infinite;
}

.square-1::before { animation-delay: 150ms; }
.square-2::before { animation-delay: 300ms; }
.square-3::before { animation-delay: 450ms; }
.square-4::before { animation-delay: 600ms; }

.loading-text {
  font-size: 14px;
  line-height: 1.2;
}
</style>
