<script setup lang="ts">
const props = defineProps<{
  options: {
    label: string
    value: string | number
    originalPrice?: number
    externalPrice?: number
    cover?: string
  }[]

  value: string | number
  cancelSelect?: boolean
  badge?: string
}>()

const emit = defineEmits<{
  (e: 'update:value', value: string | number): void
}>()

function handleSelect(value: string | number) {
  if (props.cancelSelect && value === props.value) {
    emit('update:value', 0)
  }
  else {
    emit('update:value', value)
  }
}
</script>

<template>
  <div v-for="option in options" :key="option.value" class="select-item relative"
    :class="[$attrs.class, { 'select-item--active': value === option.value }]" @click="handleSelect(option.value)">
    <slot :option="option" :active="value === option.value" />

    <div v-if="badge"
      class="absolute -top-[10px] right-0 bg-[#F53F3F] rounded-[4px] px-[6px] py-[2px] text-[10px] text-[#fff] font-[400]">
      {{ badge }}
    </div>
  </div>
</template>

<style>
.select-item {
  border: 1px solid #F2F4F7;
  border-radius: 10px;
  background-color: #F2F4F7;
  color: #4E5969;
  border-color: #E5E7EB;
  font-size: 13px;
}

.select-item--active {
  border-color: #00AC97 !important;
  color: #00AC97 !important;
  background-color: #E4FAF9 !important;
  font-weight: 600;
}
</style>
