<script setup lang="ts">
import Triangle from '@/assets/svgs/triangle.svg'

defineProps<{
  title: string
  contents: string[]
}>()
</script>

<template>
  <div class="content">
    <div class="content-title">
      {{ title }}
    </div>

    <div class="p-[8px] flex flex-col gap-[8px]">
      <div v-for="item in contents" :key="item" class="flex items-start gap-[4px]">
        <img :src="Triangle" class="w-[4px] h-[6px] relative top-[5px]" alt="" srcset="">
        <div class="text-[#4E5969] text-[11px]">
          {{ item }}
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.content {
  border: 1px solid #E4FAF9
}

.content-title {
  background-color: #E4FAF9;
  height: 26px;
  color: #00AC97;
  font-size: 12px;
  font-weight: 600;
  text-align: center;
  line-height: 26px;
}
</style>
