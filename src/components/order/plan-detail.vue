<script setup lang="ts">
import { BASE_URL } from '@/utils/mode'
import { useRouter } from '@tarojs/taro'
import { ref } from 'vue'

defineProps<{
  planDetail: any
  planId: string
}>()

const imagePrefix = `${BASE_URL}/no-cache/new-plan-images`
const showPay = ref(false)

if (useRouter().params.showPay) {
  showPay.value = true
}
</script>

<template>
  <div>
    <div v-if="planDetail" class="relative">
      <div v-if="showPay" class="text-[15px] font-[600] text-[#1D2229] mb-[12px]">
        套餐详情
      </div>

      <div>
        <img :src="`${imagePrefix}/${planId}/background.png`" alt="" srcset="" className="w-full h-auto"
          mode="widthFix">

        <img :src="`${imagePrefix}/${planId}/desc.png`" alt="" srcset="" className="w-full my-[10px] h-auto "
          mode="widthFix">

        <div className="flex flex-col gap-[10px]">
          <img v-for="(image, index) in planDetail.imageList" :key="index" :src="image" className="w-full h-auto"
            mode="widthFix" alt="" srcset="">
        </div>
      </div>

      <!-- <img :src="`${imagePrefix}/background.png`" alt="" srcset="" className="w-full -z-1 h-auto absolute top-0 left-0"
        mode="widthFix"> -->

      <!-- <img :src="`${imagePrefix}/${planId}/top-right.png`" className="relative -top-[35px] h-[201px] w-[357px]"
        mode="widthFix" alt="" srcset=""> -->

      <!-- <div class="relative -top-[190px]">
        <div class="pl-[16px]">
          <span className="text-[#00AC97] font-[600] text-[30px]">{{ planDetail.title1 }}</span>
          <span class="text-[#333] font-[600] text-[30px]">{{ planDetail.title2 }}</span>

          <div
            className="text-[#333] text-[12px] border-[1px] border-solid border-[#333] px-[7px] py-[2px] inline-flex">
            {{ planDetail.title3 }}
          </div>
        </div>

        <div class="text-[12px] leading-[18px] pl-[16px] mt-[30px] text-[#505D5B]">
          {{ planDetail.description }}
        </div>

        <div className="flex flex-col gap-[10px] mt-[10px]">
          <img v-for="(image, index) in planDetail.imageList" :key="index" :src="image" className="w-full h-auto"
            mode="widthFix" alt="" srcset="">
        </div>
      </div> -->
    </div>
    <div v-else className="h-[300px] flex items-center justify-center text-[#999]">
      加载中...
    </div>
  </div>
</template>
