<script setup lang="ts">
import { BASE_URL } from '@/utils/mode'
import { computed } from 'vue'

const props = defineProps<{
  planId: string
}>()

const imagePrefix = computed(() => {
  return `${BASE_URL}/no-cache/plan-images/${props.planId || 21}`
})

const planImages = computed(() => {
  const imageMap = {
    21: [
      '1.intro.svg',
      '2.plan-content.svg',
      '3.fasting.svg',
      '4.weight.svg',
      '5.suitable-people.svg',
      '6.qa.svg',
    ],
    22: [
      '1.intro.svg',
      '2.plan-content.svg',
      '3.probiotic.svg',
      '4.nutritionist.svg',
      '5.weight.svg',
      '6.suitable-people.svg',
      '7.qa.svg',
    ],
    23: [
      '1.intro.svg',
      '2.plan-content.svg',
      '3.probiotic.svg',
      '4.prebiotics.svg',
      '5.nutritionist.svg',
      '6.weight.svg',
      '7.suitable-people.svg',
      '8.qa.svg',
    ],
    24: [
      '1.intro.svg',
      '2.plan-content.svg',
      '3.sugar.svg',
      '4.probiotic.svg',
      '5.prebiotics.svg',
      '6.nutritionist.svg',
      '7.weight.svg',
      '8.suitable-people.svg',
      '9.qa.svg',
    ],
  }

  return imageMap[props.planId || 21]
})

const tooltips = computed(() => {
  const tooltipsMap = {
    21: {
      title: '和油腻说拜拜，拥抱清新与美丽',
      title2: 'Li05 轻断食生活方式干预计划',
      color: '#00AC97',
      backgroundColor: 'linear-gradient(180deg, #DCFFE6 0%, #F4F5F7 100%)',
    },
    22: {
      title: '每天一点点，脂肪Say Byebye!',
      title2: 'Li05 轻脂瘦身管理计划',
      color: '#0989E3',
      backgroundColor: 'linear-gradient(180deg, #DCF0FF 0%, #F4F5F7 100%);',
    },
    23: {
      title: '每天一点点，脂肪Say Byebye!',
      title2: 'Li05 降脂减重管理计划',
      color: '#E7B100',
      backgroundColor: 'linear-gradient(180deg, #FFF9E6 0%, #F4F5F7 100%);',
    },
    24: {
      title: '每天一点点，脂肪Say Byebye!',
      title2: 'Li05 糖脂代谢综合管理计划',
      color: '#5E6AF2',
      backgroundColor: 'linear-gradient(180deg, #EEEDFF 0%, #F4F5F7 100%);',
    },
  }
  return tooltipsMap[props.planId || 21]
})
</script>

<template>
  <div :style="{ background: tooltips.backgroundColor, color: tooltips.color }" style="height: 277px;">
    <div class="pt-[31px] px-[16px] mb-[20px]">
      <div class="text-[14px] font-[600]">
        {{ tooltips.title }}
      </div>
      <div className="text-[20px] font-[900] mt-[5px]">
        {{ tooltips.title2 }}
      </div>
    </div>

    <img v-for="image in planImages" :key="image" :src="`${imagePrefix}/${image}`" className="w-full h-auto"
      mode="widthFix" alt="" srcset="">

    <div className="h-[80px] w-full" />
  </div>
</template>
