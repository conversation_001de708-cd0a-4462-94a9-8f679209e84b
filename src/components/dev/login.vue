<script setup lang="ts">
import { BASE_URL } from '@/utils/mode'
import Taro from '@tarojs/taro'

const props = defineProps<{
  state: string
  doctorId: string
}>()

async function loginMock() {
  const phoneNumber = '15372333383'
  const { code } = await Taro.login()
  const url = encodeURIComponent(`${BASE_URL}/mp-redirect?phone=${phoneNumber}&state=${props.state}&code=${code}&doctorId=${props.doctorId}`)

  Taro.navigateTo({
    url: `/pages/webview/index?url=${url}`,
  })
}
</script>

<template>
  <div class="text-[12px] text-[#4E5969] flex" @click="loginMock">
    模拟登录
  </div>
</template>
