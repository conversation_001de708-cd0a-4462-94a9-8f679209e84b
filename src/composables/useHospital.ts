import { isNanJing } from '@/utils/common'
import { BASE_URL } from '@/utils/mode'
import Taro from '@tarojs/taro'
import { ref } from 'vue'

export function useHospital() {
  const currentHospital = ref<'nanjing' | 'slmc' | null>(null)

  async function getHospital(state: string) {
    try {
      if (state) {
        if (isNanJing(state)) {
          currentHospital.value = 'nanjing'
          Taro.setNavigationBarTitle({
            title: '体重管理专家',
          })
        }
      }
      else {
        const { code } = await Taro.login()
        const { data } = await Taro.request({
          url: `${BASE_URL}/api/open-api/v1/wx/getStateByCode`,
          method: 'GET',
          data: {
            code,
          },
        })

        if (isNanJing(data.results)) {
          currentHospital.value = 'nanjing'
          Taro.setNavigationBarTitle({
            title: '体重管理专家',
          })
        }
      }
    }
    catch (error) {
      console.log(error)
    }
    finally {
      if (!currentHospital.value) {
        currentHospital.value = 'slmc'
        Taro.setNavigationBarTitle({
          title: 'SLMC体重管家',
        })
      }
    }
  }

  return {
    currentHospital,
    getHospital,
  }
}
