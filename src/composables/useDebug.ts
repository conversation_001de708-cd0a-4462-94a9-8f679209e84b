import { getMode } from '@/utils/mode'
import Taro from '@tarojs/taro'
import { ref, watch } from 'vue'

export function useDebug() {
  let touchTimer: NodeJS.Timeout | null = null
  let touchStartTime = 0

  const mode = ref(getMode())

  watch(mode, (newVal) => {
    Taro.setStorageSync('mode', newVal)
  })

  const customUrl = ref(Taro.getStorageSync('custom-url') || '')

  watch(customUrl, (newVal) => {
    Taro.setStorageSync('custom-url', newVal)
  })

  const isDebugPopupShow = ref(false)

  // 添加长按相关的变量

  // 添加触摸开始事件处理函数
  function handleTouchStart() {
    touchStartTime = Date.now()
    touchTimer = setTimeout(() => {
      if (Date.now() - touchStartTime >= 5000) {
        // isDebugPopupShow.value = true
        mode.value = mode.value === 'PROD' ? 'TEST' : 'PROD'
        Taro.showToast({
          title: `已切换到${mode.value === 'PROD' ? '生产环境' : '测试环境'}`,
          icon: 'none',
        })
      }
    }, 5000)
  }

  // 添加触摸结束事件处理函数
  function handleTouchEnd() {
    if (touchTimer) {
      clearTimeout(touchTimer)
      touchTimer = null
    }
  }

  return {
    isDebugPopupShow,
    handleTouchStart,
    handleTouchEnd,
    mode,
    customUrl,
  }
}
