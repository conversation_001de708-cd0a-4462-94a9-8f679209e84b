<script setup lang="ts">
import Taro from '@tarojs/taro'

function handleHuaweiAuth() {
  console.log('华为授权')
  Taro.navigateToMiniProgram({
    appId: 'wxa6c04f899577d944',
    path: 'pages/authLogin/authLogin',
    extraData: {
      lang: 'zh-CN',
      client_id: 'xxxx',
      scope: ['https://www.huawei.com/healthkit/step.read'],
      state: 'xxxx',
    },
  })
}
</script>

<template>
  <button @click="handleHuaweiAuth">
    华为授权
  </button>
</template>
