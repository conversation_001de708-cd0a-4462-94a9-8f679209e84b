<script setup lang="ts">
import { useLoad } from '@tarojs/taro'
import { ref } from 'vue'

const url = ref()

useLoad((options) => {
  url.value = decodeURIComponent(options.url)
})

// function handleMessage(e) {
//   const messageList = e.detail.data

//   console.log('handleMessage', {
//     messageList,
//   })

//   const latestMessage = messageList[messageList.length - 1]

//   if (latestMessage.type.includes('login')) {
//     Taro.setStorageSync(latestMessage.type, latestMessage.data)
//     if (latestMessage.redirectTo) {
//       Taro.setStorageSync('webview-redirect', latestMessage.redirectTo)
//     }
//   }
//   else if (latestMessage.type.includes('logout')) {
//     Taro.removeStorageSync('user:login')
//     Taro.removeStorageSync('manager:login')
//   }
// }
</script>

<template>
  <web-view :src="url" />
</template>
