<script setup lang="ts">
import LIVER_BG from '@/assets/svgs/liver.svg'
import SLMC_LOGO from '@/assets/svgs/slmc.svg'
import BaseLoading from '@/components/base/loading.vue'
import DevLogin from '@/components/dev/login.vue'
import LoginTip from '@/components/login-tip.vue'
import { useDebug } from '@/composables/useDebug'
import { useHospital } from '@/composables/useHospital'
import { parsePlanId } from '@/utils/common'
import { TEMPLATE_ID } from '@/utils/constants'
import { getWeRunData } from '@/utils/getWeRunData'
import { handleError } from '@/utils/handleError'
import { BASE_URL } from '@/utils/mode'
import { parseTaroParams } from '@/utils/parseUrl'
import Taro, { getSetting, getStorageSync, setStorageSync, useDidShow, useRouter } from '@tarojs/taro'
import { ref } from 'vue'

Taro.hideHomeButton()

const { handleTouchStart, handleTouchEnd, mode } = useDebug()
const isDev = process.env.NODE_ENV === 'development'
const taroMode = process.env.TARO_APP_MODE
const state = ref('')
const doctorId = ref('')
const planId = ref('')

const isLogin = ref(true)
let redirectAfterGetPhoneNumber = false
let loginUrl = ''

async function handleRedirect() {
  const { getBaseUrl } = await import('@/utils/mode')
  const BASE_URL = getBaseUrl()

  if (isLogin.value) {
    try {
      if (planId.value) {
        Taro.navigateTo({
          url: `/pages/order/new-pay?planId=${planId.value}&showPay=1`,
        })

        return
      }

      if (state.value) {
        const { code } = await Taro.login()
        const userStore = Taro.getStorageSync('userStore')
        const phone = userStore.phone
        const { data } = await Taro.request({
          url: `${BASE_URL}/api/open-api/v1/wx/phone/callback`,
          data: {
            phone,
            state: state.value,
            code,
          },
        })

        // 如果问卷未完成，则跳转到问卷页面
        const { questionId, questionIsFinished } = data.results
        if (questionIsFinished === 0) {
          const questionUrl = encodeURIComponent(`${BASE_URL}/user/survey/consent?surveyId=${questionId}&type=PRELIMINARY_EVALUATION`)

          Taro.navigateTo({
            url: `/pages/webview/index?url=${questionUrl}`,
          })

          return
        }
      }
    }
    catch (error) {
      console.log(error)
    }

    const redirectUrl = getStorageSync('webview-redirect') || ''

    if (redirectUrl) {
      Taro.removeStorageSync('webview-redirect')

      const url = encodeURIComponent(`${BASE_URL}${redirectUrl}`)

      Taro.navigateTo({
        url: `/pages/webview/index?url=${url}`,
      })

      return
    }

    if (getStorageSync('scope.werun') !== 'true') {
      const res = await getSetting()

      if (res.authSetting['scope.werun']) {
        setStorageSync('scope.werun', 'true')
      }
    }

    let url = ''

    if (getStorageSync('scope.werun') === 'true') {
      try {
        const { step, lastStepUpdateTime, stepTime } = await getWeRunData()

        console.log({
          step,
          lastStepUpdateTime,
          stepTime,
        })

        url = encodeURIComponent(`${BASE_URL}/user/checkin?doctorId=${doctorId.value}&step=${step}&lastStepUpdateTime=${lastStepUpdateTime}&stepTime=${stepTime}`)
      }
      catch {
        url = encodeURIComponent(`${BASE_URL}/user/checkin?doctorId=${doctorId.value}`)
      }
    }
    else {
      url = encodeURIComponent(`${BASE_URL}/user/checkin?doctorId=${doctorId.value}`)
    }

    Taro.navigateTo({
      url: `/pages/webview/index?url=${url}`,
    })
  }
  else {
    const wxSetting = await getSetting({
      withSubscriptions: true,
    })

    if (wxSetting.subscriptionsSetting.mainSwitch === false || Object.prototype.hasOwnProperty.call(wxSetting.subscriptionsSetting?.itemSettings || {}, TEMPLATE_ID)) {
      redirectAfterGetPhoneNumber = true
    }
  }
}

async function loginByPhoneNumber(e: any) {
  try {
    Taro.showLoading({
      title: '登录中...',
      mask: true,
    })
    const { getBaseUrl } = await import('@/utils/mode')
    const BASE_URL = getBaseUrl()

    const { data } = await Taro.request({
      url: `${BASE_URL}/api/open-api/v1/wx/phone/${e.detail.code}`,
      method: 'GET',
    })

    const phoneNumber = data.results.purePhoneNumber

    const { code } = await Taro.login()

    if (planId.value) {
      Taro.navigateTo({
        url: `/pages/order/new-pay?planId=${planId.value}&showPay=1&phone=${phoneNumber}&state=${state.value}&code=${code}&doctorId=${doctorId.value}`,
      })

      return
    }

    const url = encodeURIComponent(`${BASE_URL}/mp-redirect?phone=${phoneNumber}&state=${state.value}&code=${code}&doctorId=${doctorId.value}`)

    loginUrl = url

    if (redirectAfterGetPhoneNumber) {
      Taro.navigateTo({
        url: `/pages/webview/index?url=${url}`,
      })
    }
  }
  catch (error) {
    handleError(error)
  }
  finally {
    Taro.hideLoading()
  }
}

const isPrivacyAgreed = ref(false)

async function handlePrivacy(type: 'user' | 'privacy') {
  const { BASE_URL } = await import('@/utils/mode')
  const url = encodeURIComponent(`${BASE_URL}/agreements/${type}`)

  Taro.navigateTo({
    url: `/pages/webview/index?url=${url}`,
  })
}

async function subscribeMessage() {
  if (redirectAfterGetPhoneNumber) {
    return
  }
  try {
    // @ts-expect-error 类型错误
    await Taro.requestSubscribeMessage({
      tmplIds: [TEMPLATE_ID],
    })
  }
  catch (error) {
    console.log(error)
  }
  finally {
    Taro.navigateTo({
      url: `/pages/webview/index?url=${loginUrl}`,
    })
  }
}

const { getHospital, currentHospital } = useHospital()

useDidShow(async () => {
  isLogin.value = !!(getStorageSync('user:login') === 'login' || getStorageSync('manager:login') === 'login')
  const params = useRouter().params

  const { state: state_, doctorId: doctorId_ } = parseTaroParams(params)
  await getHospital(state_)
  state.value = state_
  doctorId.value = doctorId_
  planId.value = parsePlanId(state_)

  const userLogin = getStorageSync('user:login')
  const managerLogin = getStorageSync('manager:login')

  console.log({
    state: state.value,
    doctorId: doctorId.value,
    planId: planId.value,
    userLogin,
    managerLogin,
  })

  await handleRedirect()
})
</script>

<template>
  <BaseLoading v-if="isLogin" />

  <div v-else class="relative h-screen pb-[100px] pt-[142px] login-bg">
    <img :src="LIVER_BG" alt="" srcset="" class="w-[406px] h-[342px] z-10 absolute top-[153px]">
    <div class="flex flex-col h-full items-center justify-between">
      <div class="flex items-center justify-center flex-col z-[100]">
        <img v-if="currentHospital === 'slmc'" :src="SLMC_LOGO" alt="" srcset="" class="w-[151px] h-[65px]"
          @touchstart="handleTouchStart" @touchend="handleTouchEnd">

        <img v-else-if="currentHospital === 'nanjing'" :src="`${BASE_URL}/images/nj-logo.png`" alt="" srcset=""
          class="w-[173px] h-[114px]">

        <div v-if="currentHospital" class="text-[#00AC97] mt-[8px] text-[24px] font-[500]">
          体重管家
        </div>

        <nut-tag v-if="taroMode === 'TEST'" type="warning" round>
          测试环境
        </nut-tag>

        <LoginTip />
      </div>

      <button
        class="w-[295px] h-[50px] rounded-[100px] text-[15px] text-white leading-[50px] active:bg-[#00AC97] active:opacity-80 after:border-none"
        :class="{
          'bg-[#00AC97]': isPrivacyAgreed,
          'bg-[#00AC97]/80': !isPrivacyAgreed,
        }" :disabled="!isPrivacyAgreed" open-type="getPhoneNumber" @getphonenumber="(e) => loginByPhoneNumber(e)"
        @click="subscribeMessage">
        手机号授权登录 {{ mode === 'TEST' ? '(测试环境)' : '' }}
      </button>
    </div>

    <div class="flex justify-center items-center mt-[10px]">
      <nut-checkbox v-model="isPrivacyAgreed">
        <div class="text-[12px] text-[#4E5969]">
          我已阅读并同意
        </div>
      </nut-checkbox>

      <div class="text-[12px] text-[#4E5969] flex" @click="handlePrivacy('user')">
        《<span class="text-[#00AC97]">用户服务协议</span>》
      </div>

      <div class="text-[12px] text-[#4E5969] flex" @click="handlePrivacy('privacy')">
        《<span class="text-[#00AC97]">隐私政策</span>》
      </div>
    </div>

    <div v-if="isDev" class="text-[12px] text-[#4E5969] flex" @click="subscribeMessage">
      订阅消息
      {{ mode }}
    </div>

    <DevLogin v-if="isDev" :state="state" :doctor-id="doctorId" />

    <!-- <nut-toast v-model:visible="isNutToastVisible" title="登录中..." type="loading" /> -->
  </div>
</template>

<style>
.login-bg {
  height: 100vh;
  background: linear-gradient(180deg, #EBFFFD 0%, #FFFFFF 100%);
}

.nut-checkbox {
  margin-right: 0 !important;
}

.nut-checkbox__label {
  margin-left: 10rpx !important;
}
</style>
