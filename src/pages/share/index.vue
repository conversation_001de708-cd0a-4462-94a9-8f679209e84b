<script setup lang="ts">
import Dots from '@/assets/svgs/blue-dots.svg'
import Arrow from '@/assets/svgs/share-arrow.svg'
import { useLoad, useShareAppMessage } from '@tarojs/taro'
import { ref } from 'vue'

let state = ''

useShareAppMessage(() => {
  return {
    title: '代谢相关脂肪性肝病风险评估',
    path: `/pages/login/index?state=${state}`,
    imageUrl: 'https://m.slmc.top/images/mini-share.png',
  }
})

useLoad((options) => {
  console.log({
    options,
  })

  state = options.state || ''
})

const show = ref(true)
</script>

<template>
  <nut-overlay v-model:visible="show" :close-on-click-overlay="false">
    <img class="h-[88px] w-[60px] absolute right-[70px]" :src="Arrow" alt="" srcset="">

    <div class="flex flex-col mt-[70px] justify-center items-center">
      <div class="flex items-center">
        <div class="text-[18px] text-[#fff] font-500">
          点击右上角
        </div>

        <img :src="Dots" alt="" srcset="" class="w-[34px] h-[16px] ml-[4px]">
      </div>

      <div class="text-[18px] text-[#fff] font-500">
        将问卷邀请分享给客户
      </div>
    </div>
  </nut-overlay>
</template>
