<script setup lang="ts">
import { TEMPLATE_ID } from '@/utils/constants'
import { BASE_URL } from '@/utils/mode'
import Taro from '@tarojs/taro'

async function subscribeMessage() {
  try {
    // @ts-expect-error 类型错误
    const res = await Taro.requestSubscribeMessage({
      tmplIds: [TEMPLATE_ID],
    })

    if (res.errMsg === 'requestSubscribeMessage:ok') {
      Taro.showToast({
        title: '开启通知成功',
        icon: 'success',
      })
    }

    await new Promise(resolve => setTimeout(resolve, 1000))
  }
  catch (error) {
    console.log(error)
  }
  finally {
    Taro.navigateBack()
  }
}
</script>

<template>
  <div class="relative h-screen">
    <img :src="`${BASE_URL}/no-cache/wx-sub.png`" alt="" srcset="" className="background-image">

    <div className="flex justify-center">
      <button
        className="w-[240px] h-[50px] bottom-[50px] absolute rounded-[100px] text-[15px] text-white leading-[50px] bg-[#00AC97] active:opacity-80 after:border-none"
        @click="subscribeMessage">
        开启通知
      </button>
    </div>
  </div>
</template>

<style>
.background-image {
  object-fit: cover;
  object-position: center;
  position: absolute;
  top: 0;
  left: 0;
  width: 375px;
  height: 100%;
}
</style>
