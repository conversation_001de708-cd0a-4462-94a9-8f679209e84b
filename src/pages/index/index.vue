<script setup lang="ts">
import BaseLoading from '@/components/base/loading.vue'
import { decodeMessage } from '@/utils/common'
import { BASE_URL } from '@/utils/mode'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'

async function handleRedirect() {
  await new Promise((resolve) => {
    setTimeout(resolve, 500)
  })

  const userLogin = Taro.getStorageSync('user:login')
  const managerLogin = Taro.getStorageSync('manager:login')

  const redirectUrl = Taro.getStorageSync('webview-redirect') || ''

  Taro.removeStorageSync('webview-redirect')

  const loginData = (userLogin === 'login' || managerLogin === 'login')

  const url = encodeURIComponent(`${BASE_URL}${redirectUrl}`)

  console.log({
    userLogin,
    managerLogin,
    redirectUrl,
    loginData,
    url,
  })

  if (loginData) {
    Taro.redirectTo({
      url: `/pages/webview/index?url=${url}`,
    })
  }
  else {
    Taro.reLaunch({
      url: '/pages/login/index',
    })
  }
}

useDidShow(async () => {
  const params = useRouter().params

  const message = params.message || ''

  if (message) {
    const decodedMessage = decodeMessage(message)

    if (decodedMessage.type.includes('login')) {
      Taro.setStorageSync(decodedMessage.type, decodedMessage.data)
      Taro.setStorageSync('userStore', decodedMessage.userStore)
      Taro.setStorageSync('token', decodedMessage.token)
      if (decodedMessage.redirectTo) {
        Taro.setStorageSync('webview-redirect', decodedMessage.redirectTo)
      }
    }
    else if (decodedMessage.type.includes('logout')) {
      Taro.removeStorageSync('user:login')
      Taro.removeStorageSync('manager:login')
      Taro.removeStorageSync('userStore')
      Taro.removeStorageSync('token')
    }
  }

  await handleRedirect()
})
</script>

<template>
  <div class="flex justify-center items-center">
    <BaseLoading />
  </div>
</template>
