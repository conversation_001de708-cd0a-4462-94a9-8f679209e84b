<script setup lang="ts">
import PlanImages from '@/components/order/plan-images.vue'
import { BASE_URL } from '@/utils/mode'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import { computed, ref } from 'vue'

let reportId = ''

const token = Taro.getStorageSync('token')

const planList = ref()

const planDetail = computed(() => {
  if (!planList.value)
    return null

  const interventionPlan = JSON.parse(planList.value.interventionPlan)

  const recommendId = Array.isArray(interventionPlan.recommendId) ? interventionPlan.recommendId[0] : interventionPlan.recommendId

  return {
    detail: interventionPlan.interventionPlanVOList.find((item: any) => item.id.toString() === recommendId.toString()),
    id: recommendId,
  }
})

const isPay = computed(() => {
  return planList.value?.payStatus === 'SUCCESS'
})

async function getPlanDetail() {
  try {
    const { data } = await Taro.request({
      url: `${BASE_URL}/api/patient-assessment/getAllAssessmentReportsByCustomerId`,
      method: 'GET',
      header: {
        Authorization: token,
      },
    })

    const findPlan = data.results.find((item: any) => item.id.toString() === reportId)

    planList.value = findPlan
  }
  catch (error) {
    console.log(error)
  }
}

useDidShow(() => {
  try {
    const params = useRouter().params

    reportId = params.reportId!

    getPlanDetail()
  }
  catch (error) {
    console.log(error)
  }
})

async function handlePay() {
  Taro.navigateTo({
    url: `/pages/order/pay?reportId=${useRouter().params.reportId}`,
  })
}
</script>

<template>
  <div class="bg-[#F5F5F5] h-screen relative overflow-auto">
    <div v-if="planDetail?.detail">
      <PlanImages :plan-id="planDetail.id" />
    </div>

    <div v-if="planDetail?.detail" className="fixed bottom-0 bg-[#fff] px-[16px] w-full flex dynamic-height">
      <div className="h-[56px] w-full flex justify-between items-center">
        <div className="flex items-center gap-[3px]">
          <span className="text-[12px] text-red-500 font-[600]">
            ¥
          </span>

          <span className="text-[17px] text-red-500 font-[600]">0.</span>

          <span className="text-[13px] text-red-500 font-[600] relative top-[1px]">00</span>

          <div className="text-[#868F9C] text-[13px] line-through relative top-[2px] left-[3px]">
            {{ planDetail.detail.price }}.00
          </div>
        </div>

        <nut-button v-if="!isPay" color="#00AC97" @click="handlePay">
          立即下单
        </nut-button>

        <div v-else class="text-gray-500 text-[12px]">
          订单已完成
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.dynamic-height {
  height: calc(56px + constant(safe-area-inset-bottom)) !important;
  height: calc(56px + env(safe-area-inset-bottom)) !important;
}

.dynamic-mb {
  margin-bottom: calc(56px + constant(safe-area-inset-bottom)) !important;
  margin-bottom: calc(56px + env(safe-area-inset-bottom)) !important;
}

.nut-tabs .nut-tabs__titles {
  background-color: #fff;
  border-radius: 10px 10px 0 0;
  border-bottom: 1px solid #efeeee;
}

.nut-tab-pane {
  border-radius: 0 0 10px 10px;
}
</style>
