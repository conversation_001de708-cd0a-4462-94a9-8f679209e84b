<script setup lang="ts">
import Location from '@/assets/svgs/location.svg'
import ArrowRight from '@/assets/svgs/right-arrow.svg'
import Select from '@/components/base/select.vue'
import PlanDetail from '@/components/order/plan-detail.vue'
import { BASE_URL } from '@/utils/mode'
import http from '@/utils/request'
import Taro, { useRouter } from '@tarojs/taro'
import { computed, onMounted, ref, watch } from 'vue'

definePageConfig({
  navigationBarTitleText: '体重管理计划',
})

// 轻断食 fasting
// 降脂减重 lipid-reduction
// 糖脂代谢 metabolic-improvement

const planId = ref('fasting')
const showPay = ref(false)

planId.value = useRouter().params.planId || 'fasting'

const showToggle = ref(false)
const showFreeItem = ref(true)
const showPlanDetail = ref(true)

const phone = useRouter().params.phone || ''
const state = decodeURIComponent(useRouter().params.state || '') || ''
// 有 code， 代表走预付款流程
const code = useRouter().params.code || ''
const doctorId = useRouter().params.doctorId || ''

if (useRouter().params.showPay) {
  showPay.value = true
}

if (useRouter().params.showToggle) {
  showToggle.value = true
}

const planDetailValue = computed(() => {
  const imagePrefix = `${BASE_URL}/no-cache/new-plan-images`

  const planDetailMap = {
    'fasting': {
      title1: 'SLMC 生活方式',
      title2: '套餐',
      title3: '（28天/84天/长期有效）',
      description: '基于个体相关病史、生活方式风险、医学检查报告等多维度数据，智能分析健康优先级，制定个体化生活方式管理计划，通过调整日常饮食、运动模式，减少体重、体脂，改善代谢健康，降低心血管疾病、2型糖尿病等风险。',
      imageList: [
        `${imagePrefix}/${planId.value}/details/point.png`,
        `${imagePrefix}/${planId.value}/details/recommand-people.png`,
        `${imagePrefix}/${planId.value}/details/detail.png`,
        `${imagePrefix}/${planId.value}/details/unrecommand-people.png`,
      ],
    },
    'lipid-reduction': {
      title1: 'SLMC 降脂减重',
      title2: '套餐',
      title3: '（28天/84天）',
      description: '基于个体年龄、体重、医学检查、生活习惯等多维度数据，智能分析健康优先级，制定精准化饮食方案，结合Li05专利益生菌+亚麻籽膳食纤维益生元使用，直击内脏脂肪代谢，提升免疫，以实现重塑肠道健康、降低内脂、调节血糖血脂的健康减重方法。',
      imageList: [
        `${imagePrefix}/${planId.value}/details/point.png`,
        `${imagePrefix}/${planId.value}/details/recommand-people.png`,
        `${imagePrefix}/${planId.value}/details/advantage.png`,
        `${imagePrefix}/${planId.value}/details/detail.png`,
        `${imagePrefix}/${planId.value}/details/unrecommand-people.png`,
      ],
    },
    'metabolic-improvement': {
      title1: 'SLMC 糖脂代谢',
      title2: '套餐',
      title3: '（28天/84天）',
      description: '基于个体代谢特征、肠道菌群、生活习惯等多维度数据，智能记录诊断信息，制定精准化饮食方案，结合Li05专利益生菌+亚麻籽膳食纤维益生元使用，通过精准评估和动态调整，改善糖脂代谢，实现与药物治疗的高效协同，减轻药物的不良反应。',
      imageList: [
        `${imagePrefix}/${planId.value}/details/point.png`,
        `${imagePrefix}/${planId.value}/details/recommand-people.png`,
        `${imagePrefix}/${planId.value}/details/advantage.png`,
        `${imagePrefix}/${planId.value}/details/detail.png`,
        `${imagePrefix}/${planId.value}/details/unrecommand-people.png`,
      ],
    },
  }
  return planDetailMap[planId.value]
})

const priceDetail = ref<any>()

function getPriceByPeriodAndId(planId: string, period: number, id: string) {
  return priceDetail.value[planId][id].find(item => item.value === period)
}

const formDetail = ref({
  // 计划ID
  planId: '',
  address: null as Record<string, string> | null,
  pickupMethod: '1',
  packageCover: '',
  packagePrice: {
    cover: '',
    period: 28,
    originalPrice: 0,
    externalPrice: 0,
    label: '',
    contents: [],
  },
  // 在线干预
  onlineIntervention: {
    period: 1,
    originalPrice: 199,
    externalPrice: 0,
  },
  period: 28,
  // 益生菌
  probiotics: {
    period: 28,
    originalPrice: 0,
    externalPrice: 0,
  },
  // 益生元
  prebiotics: {
    period: 28,
    originalPrice: 0,
    externalPrice: 0,
  },
  // 营养师
  nutritionistService: {
    period: 28,
    originalPrice: 0,
    externalPrice: 0,
  },
})

function handleToggle() {
  showToggle.value = false
  showFreeItem.value = false
  formDetail.value.onlineIntervention.originalPrice = 0
  showPay.value = true
  showPlanDetail.value = false
}

const pageTextShow = computed(() => {
  return {
    price: formDetail.value.planId === 'fasting' ? '限时特惠' : '内部到手价',
    badge: formDetail.value.planId === 'fasting' ? '限时特惠' : '限时赠送',
  }
})

function handleGetAddress() {
  Taro.chooseAddress({
    success: (res: any) => {
      formDetail.value.address = res
    },
  })
}

const prices = computed(() => {
  const priceString = (formDetail.value.packagePrice.externalPrice + formDetail.value.probiotics.externalPrice + formDetail.value.prebiotics.externalPrice + formDetail.value.nutritionistService.externalPrice + formDetail.value.onlineIntervention.externalPrice).toFixed(2)
  const originalPriceString = (formDetail.value.packagePrice.originalPrice + formDetail.value.probiotics.originalPrice + formDetail.value.prebiotics.originalPrice + formDetail.value.nutritionistService.originalPrice + formDetail.value.onlineIntervention.originalPrice).toFixed(2)
  return {
    priceNumber: Number(priceString),
    originalPriceNumber: Number(originalPriceString),
    priceInteger: priceString.split('.')[0],
    priceDecimal: priceString.split('.')[1],
    originalPriceInteger: originalPriceString.split('.')[0],
    originalPriceDecimal: originalPriceString.split('.')[1],
  }
})

watch([() => formDetail.value.planId, () => formDetail.value.period], ([newPlanId, newProbioticsPeriod]) => {
  if (!priceDetail.value) {
    return
  }

  const probioticsPeriod = newProbioticsPeriod

  const probioticsPrice = getPriceByPeriodAndId(newPlanId, probioticsPeriod, 'probiotics')
  const prebioticsPrice = getPriceByPeriodAndId(newPlanId, probioticsPeriod, 'prebiotics')
  const nutritionistServicePrice = getPriceByPeriodAndId(newPlanId, probioticsPeriod, 'nutritionistService')

  formDetail.value.probiotics.period = newProbioticsPeriod
  formDetail.value.probiotics.externalPrice = probioticsPrice.externalPrice
  formDetail.value.probiotics.originalPrice = probioticsPrice.originalPrice
  formDetail.value.prebiotics.externalPrice = prebioticsPrice.externalPrice
  formDetail.value.prebiotics.originalPrice = prebioticsPrice.originalPrice
  formDetail.value.prebiotics.period = probioticsPeriod
  formDetail.value.nutritionistService.externalPrice = nutritionistServicePrice.externalPrice
  formDetail.value.nutritionistService.originalPrice = nutritionistServicePrice.originalPrice
  formDetail.value.nutritionistService.period = probioticsPeriod
  formDetail.value.packageCover = priceDetail.value[newPlanId].packages.find(item => item.value === newProbioticsPeriod)?.cover
  formDetail.value.packagePrice = priceDetail.value[newPlanId].packages.find(item => item.value === newProbioticsPeriod)
})

const currentPriceDetail = computed(() => {
  if (!priceDetail.value) {
    return null
  }

  const currentProbioticsPeriod = formDetail.value.period
  return {
    packages: priceDetail.value[planId.value].packages,
    probiotics: priceDetail.value[planId.value].probiotics.filter(item => item.value === currentProbioticsPeriod),
    prebiotics: priceDetail.value[planId.value].prebiotics.filter(item => item.value === currentProbioticsPeriod),
    nutritionistService: priceDetail.value[planId.value].nutritionistService.filter(item => item.value === currentProbioticsPeriod),
    onlineIntervention: priceDetail.value[planId.value].onlineIntervention,
    showItems: priceDetail.value[planId.value].showItems,
    detailsImages: priceDetail.value[planId.value].detailsImages,
  }
})

function handleUpdatePrebioticsPrice(value: number) {
  if (value === 0) {
    formDetail.value.prebiotics.externalPrice = 0
    formDetail.value.prebiotics.originalPrice = 0
    return
  }

  const prebioticsPrice = getPriceByPeriodAndId(planId.value, value, 'prebiotics')
  formDetail.value.prebiotics.externalPrice = prebioticsPrice.externalPrice
  formDetail.value.prebiotics.originalPrice = prebioticsPrice.originalPrice
}

function handleUpdateNutritionistServicePrice(value: number) {
  if (value === 0) {
    formDetail.value.nutritionistService.originalPrice = 0
    formDetail.value.nutritionistService.externalPrice = 0
    return
  }

  const nutritionistServicePrice = getPriceByPeriodAndId(planId.value, value, 'nutritionistService')
  formDetail.value.nutritionistService.externalPrice = nutritionistServicePrice.externalPrice
  formDetail.value.nutritionistService.originalPrice = nutritionistServicePrice.originalPrice
}

function handleUpdateProbioticsPrice(value: number) {
  if (value === 0) {
    formDetail.value.probiotics.externalPrice = 0
    formDetail.value.probiotics.originalPrice = 0
    return
  }

  const probioticsPrice = getPriceByPeriodAndId(planId.value, value, 'probiotics')
  formDetail.value.probiotics.externalPrice = probioticsPrice.externalPrice
  formDetail.value.probiotics.originalPrice = probioticsPrice.originalPrice
}

async function handlePay() {
  try {
    if (!formDetail.value.address && formDetail.value.pickupMethod === '1' && planId.value !== 'fasting') {
      Taro.showToast({
        title: '请选择收货地址',
        icon: 'error',
      })

      return false
    }

    const planContent: Record<string, any> = {}

    planContent.period = formDetail.value.period
    planContent.onlineIntervention = formDetail.value.onlineIntervention

    if (planId.value !== 'fasting') {
      if (formDetail.value.probiotics.period) {
        planContent.probiotics = formDetail.value.probiotics
      }
      if (formDetail.value.prebiotics.period) {
        planContent.prebiotics = formDetail.value.prebiotics
      }
      if (formDetail.value.nutritionistService.period) {
        planContent.nutritionistService = formDetail.value.nutritionistService
      }
    }

    let newCode = ''

    if (code) {
      const { code: _newCode } = await Taro.login()
      newCode = _newCode
    }

    const isDev = process.env.NODE_ENV === 'development'
    const { data } = await http.post(
      code ? '/api/pay/open-api/createPlanPayOrder' : '/api/pay/createPlanPayOrder',
      {
        address: JSON.stringify(formDetail.value.address),
        pickupMethod: formDetail.value.pickupMethod,
        planId: planId.value,
        totalAmount: prices.value.priceNumber > 0
          ? isDev ? 0.01 : prices.value.priceNumber
          : 0,
        planContent: JSON.stringify(planContent),
        code: newCode,
        phone,
        state,
      },
      {
        showLoading: true,
        loadingText: '下单中...',
      },
    )

    if (data.state !== 200) {
      throw new Error(data.message)
    }

    const result = data.results

    if (result === true) {
      Taro.showToast({
        title: '下单成功',
        icon: 'success',
      })

      await new Promise(resolve => setTimeout(resolve, 500))

      const url = encodeURIComponent(`${BASE_URL}/user/checkin`)
      Taro.redirectTo({
        url: `/pages/index/index?url=${url}`,
      })

      return
    }

    const paymentResult = await Taro.requestPayment({
      timeStamp: result.timeStamp,
      nonceStr: result.nonceStr,
      package: result.packageValue,
      signType: result.signType,
      paySign: result.paySign,
    })

    if (paymentResult.errMsg === 'requestPayment:ok') {
      Taro.showToast({
        title: '下单成功',
        icon: 'success',
      })

      await new Promise(resolve => setTimeout(resolve, 500))

      let url = ''

      if (code) {
        const { code: newCode } = await Taro.login()
        url = encodeURIComponent(`${BASE_URL}/mp-redirect?phone=${phone}&state=${state}&code=${newCode}&doctorId=${doctorId}`)
        Taro.redirectTo({
          url: `/pages/webview/index?url=${url}`,
        })
      }
      else {
        url = encodeURIComponent(`${BASE_URL}/user/checkin`)
        Taro.redirectTo({
          url: `/pages/index/index?url=${url}`,
        })
      }
    }
    else if (paymentResult.errMsg === 'requestPayment:fail cancel') {
      console.log('用户取消支付')
    }
    else {
      Taro.showToast({
        title: '下单失败',
        icon: 'error',
      })
    }
  }
  catch (error) {
    // Taro.showToast({
    //   title: '下单失败',
    //   icon: 'error',
    // })
    console.log(error)
  }
}

// async function handleNotJoin() {
//   const url = encodeURIComponent(`${BASE_URL}/user/checkin`)
//   Taro.redirectTo({
//     url: `/pages/index/index?url=${url}`,
//   })
// }

function formatPrice(price: number) {
  // 自动补全 2 位小数
  return `¥ ${price.toFixed(2)}`
}

const playedCount = ref<{
  planId: string
  planCount: number
}[]>()

const currentPlayedCount = computed(() => {
  const count = playedCount.value?.find(item => item.planId === planId.value)?.planCount
  const base = 2173
  return (count || 0) + base
})

async function getPlayedCount() {
  try {
    const { data } = await http.get('/api/customerPlan/getPayedPlanCount')
    playedCount.value = data.results
  }
  catch (error) {
    console.error('获取已售数量失败:', error)
  }
}

const isDisablePayButton = computed(() => {
  if (showFreeItem.value)
    return false

  return prices.value.priceNumber === 0
})

onMounted(async () => {
  try {
    handleToggle()
    const { data } = await http.get('/no-cache/plan-detail.json', {
      showLoading: true,
      loadingText: '加载计划详情...',
    })
    priceDetail.value = data
    formDetail.value.planId = planId.value
  }
  catch (error) {
    console.error('获取计划详情失败:', error)
    Taro.showToast({
      title: '加载失败，请重试',
      icon: 'error',
    })
  }

  getPlayedCount()
})
</script>

<template>
  <div v-if="currentPriceDetail">
    <div class="p-[10px] bg-[#F2F4F7] flex h-screen flex-col gap-[10px] overflow-auto">
      <div v-if="showPay" class="rounded-[10px] bg-[#fff] p-[16px]">
        <div class="flex items-center justify-between">
          <div class="text-[#1D2229] text-[16px] font-[600]">
            {{ `${planDetailValue.title1}${planDetailValue.title2}` }}{{ `（${formDetail.period}天）` }}
          </div>

          <!-- <nut-button type="default" size="small" color="#F2F4F7" style="color: #4E5969" @click="handleNotJoin">
            暂不加入
          </nut-button> -->
        </div>

        <div class="mt-[12px] flex items-center justify-between">
          <div class="flex items-center gap-[8px]">
            <img :src="`${BASE_URL}${formDetail.packageCover}`" class="w-[56px] h-[56px] rounded-[10px]" alt=""
              srcset="">

            <div>
              <div class="flex items-center gap-[8px]">
                <div class="text-[#00AC97] flex items-center">
                  <span className="text-[14px] mr-[3px] font-[600]">
                    ¥
                  </span>

                  <span className="text-[20px] font-[600]">{{ prices.priceInteger }}.</span>

                  <span className="text-[14px] font-[600] relative top-[2px]">{{ prices.priceDecimal }}</span>
                </div>

                <div class="text-[#00AC97] bg-[#E4FAF9] text-[12px] px-[8px] py-[2px] rounded-[4px]">
                  {{ pageTextShow.price }}
                </div>
              </div>

              <div class="text-[#868F9C] text-[13px] flex items-center line-through">
                ¥{{ prices.originalPriceInteger }}.{{ prices.originalPriceDecimal }}
              </div>
            </div>
          </div>

          <div class="text-[#4E5969] text-[12px]">
            已售 {{ currentPlayedCount }} 件
          </div>
        </div>
      </div>

      <div v-if="planId !== 'fasting' && showPay"
        className="bg-[#fff] my-[10px] flex justify-between items-center p-[16px] rounded-[10px]"
        @click="handleGetAddress">
        <div className="flex items-start gap-1">
          <img :src="Location" className="w-[16px] h-[16px] flex-shrink-0 relative top-[2px]" alt="" srcset="">

          <div v-if="formDetail.address">
            <div className="font-[600] text-[15px] text-[#1D2229]">
              {{ formDetail.address.provinceName + formDetail.address.cityName + formDetail.address.countyName
                + formDetail.address.detailInfo }}
            </div>

            <div className="text-[#4E5969] text-[14px] mt-[8px]">
              {{ `${formDetail.address.userName} ${formDetail.address.telNumber}` }}
            </div>
          </div>

          <div v-else className="font-[600] text-[15px] text-[#1D2229]">
            请选择地址
          </div>
        </div>

        <img :src="ArrowRight" className="w-[16px] h-[16px] flex-shrink-0" alt="" srcset="">
      </div>

      <div className="bg-white p-[16px]">
        <div class="flex flex-col gap-[16px]">
          <div v-if="currentPriceDetail.showItems.includes('packages') && showPay">
            <div className="text-[15px] mb-[8px] font-[600] text-[#1D2229] title-with-decorator">
              可选套餐
            </div>

            <div className="flex gap-[12px]">
              <Select v-model:value="formDetail.period" class="w-[122px]" :options="currentPriceDetail.packages">
                <template #default="{ option }">
                  <div>
                    <img :src="`${BASE_URL}${option.cover}`"
                      class="w-[116px] ml-[2px] h-[118px] mt-[1px] rounded-t-[10px]" alt="" srcset="">

                    <div class="text-[#1D2229] flex items-center justify-center font-[400] h-[31px] text-[12px]">
                      ¥{{ option.label }}
                    </div>
                  </div>
                </template>
              </Select>
            </div>
          </div>

          <div v-if="showPay">
              <template v-if="formDetail.packagePrice.contents.length > 0">
                <div className="text-[15px] mb-[8px] font-[600] text-[#1D2229] title-with-decorator">
                  套餐内容
                </div>
                <div class="flex flex-col gap-[8px] mb-[8px]">
                  <div v-for="(item, index) in formDetail.packagePrice.contents" :key="index" class="flex flex-col gap-[4px]">
                    <div class="text-[#1D2229] text-[14px] font-[400] title-with-dot">
                      {{ item.title }}
                    </div>
                    <div class="text-[#868F9C] text-[12px] font-[400] pl-[16px]">
                      {{ item.subTitle }}
                    </div>
                  </div>
                </div>
              </template>

            <!-- <div class="flex flex-col gap-[10px]">
              <Select v-if="currentPriceDetail.showItems.includes('onlineIntervention') && showFreeItem"
                v-model:value="formDetail.onlineIntervention.period" :badge="pageTextShow.badge"
                :options="currentPriceDetail.onlineIntervention">
                <template #default="{ option, active }">
                  <div class="flex px-[12px] py-[10px] w-full justify-between">
                    <div class="text-[#1D2229] font-[400]">
                      {{ option.label }}
                    </div>

                    <div class="font-[400] flex-shrink-0" :class="active ? 'text-[#00AC97]' : 'text-[#868F9C]'">
                      {{ formatPrice(option.externalPrice || 0) }}
                    </div>
                  </div>
                </template>
              </Select>

              <Select v-if="currentPriceDetail.showItems.includes('probiotics')"
                v-model:value="formDetail.probiotics.period" cancel-select :options="currentPriceDetail.probiotics"
                @update:value="handleUpdateProbioticsPrice">
                <template #default="{ option, active }">
                  <div class="flex px-[12px] py-[10px] w-full justify-between">
                    <div class="text-[#1D2229] font-[400]">
                      {{ option.label }}
                    </div>

                    <div class="font-[400] flex-shrink-0" :class="active ? 'text-[#00AC97]' : 'text-[#868F9C]'">
                      {{ formatPrice(option.externalPrice || 0) }}
                    </div>
                  </div>
                </template>
              </Select>

              <Select v-if="currentPriceDetail.showItems.includes('prebiotics')"
                v-model:value="formDetail.prebiotics.period" cancel-select :options="currentPriceDetail.prebiotics"
                @update:value="handleUpdatePrebioticsPrice">
                <template #default="{ option, active }">
                  <div class="flex px-[12px] py-[10px] w-full justify-between">
                    <div class="text-[#1D2229] font-[400]">
                      {{ option.label }}
                    </div>

                    <div class="font-[400] flex-shrink-0" :class="active ? 'text-[#00AC97]' : 'text-[#868F9C]'">
                      {{ formatPrice(option.externalPrice || 0) }}
                    </div>
                  </div>
                </template>
              </Select>

              <Select v-if="currentPriceDetail.showItems.includes('nutritionistService')"
                v-model:value="formDetail.nutritionistService.period" cancel-select
                :options="currentPriceDetail.nutritionistService" @update:value="handleUpdateNutritionistServicePrice">
                <template #default="{ option, active }">
                  <div class="flex px-[12px] py-[10px] w-full justify-between">
                    <div class="text-[#1D2229] font-[400]">
                      {{ option.label }}
                    </div>
                    <div class="font-[400] flex-shrink-0" :class="active ? 'text-[#00AC97]' : 'text-[#868F9C]'">
                      {{ formatPrice(option.externalPrice || 0) }}
                    </div>
                  </div>
                </template>
              </Select>
            </div> -->

            <template v-if="currentPriceDetail?.detailsImages?.length > 0">
              <div className="text-[15px] mb-[8px] font-[600] text-[#1D2229] title-with-decorator">
                套餐详情
              </div>

              <img v-for="(item, index) in currentPriceDetail.detailsImages" :key="index" mode="widthFix" :src="`${BASE_URL}${item}`" alt="" srcset="">
            </template>
          </div>

          <PlanDetail v-if="showPlanDetail" :plan-detail="planDetailValue" :plan-id="planId" />

          <div v-if="showPay || showToggle" class="h-[70px]" />
        </div>
      </div>
    </div>

    <div v-if="showPay" className="fixed bottom-0 bg-[#fff] w-full flex p-[16px]">
      <div className="dynamic-height-2 w-full flex justify-between">
        <div>
          <div class="flex items-center gap-[8px]">
            <div class="text-[#00AC97] flex items-center">
              <span className="text-[14px] mr-[3px] font-[600]">
                ¥
              </span>

              <span className="text-[20px] font-[600]">{{ prices.priceInteger }}.</span>

              <span className="text-[14px] font-[600] relative top-[2px]">{{ prices.priceDecimal }}</span>
            </div>

            <div class="text-[#00AC97] bg-[#E4FAF9] text-[12px] px-[8px] py-[2px] rounded-[4px]">
              {{ pageTextShow.price }}
            </div>
          </div>

          <div class="text-[#868F9C] text-[13px] flex items-center line-through">
            ¥{{ prices.originalPriceInteger }}.{{ prices.originalPriceDecimal }}
          </div>
        </div>

        <nut-button :disabled="isDisablePayButton" color="#00AC97" style="height: 50px!important;width: 120px!important"
          @click="handlePay">
          立即支付
        </nut-button>
      </div>
    </div>

    <div v-if="showToggle" className="fixed bottom-0 bg-[#fff] w-full flex p-[16px]">
      <div className="dynamic-height-2 w-full flex justify-between">
        <nut-button block color="#00AC97" style="height: 50px!important;" @click="handleToggle">
          立即加入
        </nut-button>
      </div>
    </div>
  </div>
</template>

<style>
.plan-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.nut-popup {
  background-color: #F2F4F7;
}

.dynamic-height-2 {
  height: calc(48px + constant(safe-area-inset-bottom)) !important;
  height: calc(48px + env(safe-area-inset-bottom)) !important;
}

.title-with-decorator {
  position: relative;
  padding-left: 8px;
}

.title-with-decorator::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 4px;
  height: 13px;
  background-color: #00AC97;
  border-radius: 2px;
}

.title-with-dot {
  position: relative;
  padding-left: 16px;
}

.title-with-dot::before {
  content: '';
  position: absolute;
  left: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 6px;
  height: 6px;
  background-color: #C9CDD4;
  border-radius: 50%;
}
</style>
