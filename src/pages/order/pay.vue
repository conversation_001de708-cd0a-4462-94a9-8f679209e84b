<script setup lang="ts">
import rightArrow from '@/assets/svgs/right-arrow.svg'
import OrderContent from '@/components/order/content.vue'
import OrderTitle from '@/components/order/title.vue'
import { decodeMessage } from '@/utils/common'
import { BASE_URL } from '@/utils/mode'
import Taro, { useDidShow, useRouter } from '@tarojs/taro'
import { computed, ref } from 'vue'

let reportId = ''

function handleAddressPage() {
  const addressWebviewUrl = encodeURIComponent(`${BASE_URL}/user/confirm-order?reportId=${reportId}`)

  Taro.navigateTo({
    url: `/pages/webview/index?url=${addressWebviewUrl}`,
  })
}

const token = Taro.getStorageSync('token')

const address = ref()

const planList = ref()

const planDetail = computed(() => {
  if (!planList.value)
    return null

  const interventionPlan = JSON.parse(planList.value.interventionPlan)

  const recommendId = Array.isArray(interventionPlan.recommendId) ? interventionPlan.recommendId[0] : interventionPlan.recommendId

  return interventionPlan.interventionPlanVOList.find((item: any) => item.id.toString() === recommendId.toString())
})

const isPay = computed(() => {
  return planList.value?.payStatus === 'SUCCESS'
})

async function getPlanDetail() {
  try {
    const { data } = await Taro.request({
      url: `${BASE_URL}/api/patient-assessment/getAllAssessmentReportsByCustomerId`,
      method: 'GET',
      header: {
        Authorization: token,
      },
    })

    const findPlan = data.results.find((item: any) => item.id.toString() === reportId)

    planList.value = findPlan
  }
  catch (error) {
    console.log(error)
  }
}

useDidShow(() => {
  try {
    const params = useRouter().params

    console.log({ params })

    const message = params.message || ''
    reportId = params.reportId!

    getPlanDetail()

    address.value = decodeMessage(message)
  }
  catch (error) {
    console.log(error)
  }
})

const pickupMethod = ref('2')

async function handlePay() {
  try {
    if (!address.value && pickupMethod.value === '1') {
      Taro.showToast({
        title: '请选择收货地址',
        icon: 'error',
      })

      return false
    }

    const { data } = await Taro.request({
      url: `${BASE_URL}/api/pay/create`,
      method: 'POST',
      data: {
        assessmentReportId: reportId,
        addressId: address.value ? address.value.id : '',
        pickupMethod: pickupMethod.value,
        totalAmount: 0.01,
      },
      header: {
        Authorization: token,
      },
    })

    const result = data.results

    await Taro.requestPayment({
      timeStamp: result.timeStamp,
      nonceStr: result.nonceStr,
      package: result.packageValue,
      signType: result.signType,
      paySign: result.paySign,
    })

    console.log(data)

    // if (data.results === 'SUCCESS') {
    //   Taro.showToast({
    //     title: '下单成功',
    //     icon: 'success',
    //   })

    //   await new Promise(resolve => setTimeout(resolve, 500))

    //   const url = encodeURIComponent(`${BASE_URL}/user/checkin`)
    //   Taro.redirectTo({
    //     url: `/pages/index/index?url=${url}`,
    //   })
    // }
    // else {
    //   Taro.showToast({
    //     title: '下单失败',
    //     icon: 'error',
    //   })
    // }
  }
  catch (error) {
    Taro.showToast({
      title: '下单失败',
      icon: 'error',
    })
    console.log(error)
  }
}
</script>

<template>
  <div class="bg-[#F5F5F5] h-screen relative overflow-auto">
    <div v-if="planDetail" class="p-[16px]">
      <nut-tabs v-if="!isPay" v-model="pickupMethod">
        <nut-tab-pane title="现场自取" pane-key="2">
          <div class="text-[#1D2229] text-[14px] font-[600]">
            请联系现场工作人员
          </div>
        </nut-tab-pane>
        <nut-tab-pane title="寄送到家" pane-key="1">
          <div class="bg-[#fff] rounded-[10px]" @click="handleAddressPage">
            <template v-if="!address">
              <div class="flex justify-between items-center">
                <div>
                  <div class="text-[#1D2229] text-[16px] font-[600]">
                    请选择收货地址
                  </div>

                  <div class="text-[#868F9C] text-[12px]">
                    准确填写收件地址，以便准时送达哦~
                  </div>
                </div>

                <img class="w-[16px] h-[16px]" :src="rightArrow" alt="" srcset="">
              </div>
            </template>

            <template v-else>
              <div class="text-[#1D2229] text-[16px] font-[600]">
                {{ `${address.province + address.city + address.district} ${address.detail}` }}
              </div>

              <div class="text-[12px] text-[#4E5969] mt-[10px]">
                {{ `${address.name} ${address.phone}` }}
              </div>
            </template>
          </div>
        </nut-tab-pane>
      </nut-tabs>
      <!-- <div class="p-[16px]">
        <img class="w-full mt-[16px] h-[379px]" :src="`${BASE_URL}/images/order/3.png`" alt="" srcset="">
      </div> -->
      <div className="rounded-[10px] bg-[#fff] mt-[10px] p-[16px] mb-[100px]">
        <div class="text-[18px] font-bold text-center">
          营养套餐
        </div>

        <OrderTitle class="mt-[16px]" :title="`套餐名称：${planDetail.type}`" number="1" />

        <div class="mt-[16px]">
          <OrderContent title="服务内容" :contents="planDetail.serviceContent.split('|')" />
        </div>

        <!-- <div class="mt-[16px]">
          <OrderContent title="适用人群" :contents="planDetail.applicableGroups.split('|')" />
        </div> -->

        <OrderTitle class="mt-[16px]" title="套餐规格" number="2" />

        <div class="flex">
          <div
            class="text-[#4E5969] mt-[8px] flex px-[12px] rounded-[100px] leading-[29px] text-[12px] h-[29px] bg-[#F2F3F5]">
            {{ planDetail.packageSpecificationsRecommendId }}
          </div>
        </div>

        <!-- <OrderTitle class="mt-[16px]" title="产品信息" number="3" />

        <img className="w-full h-auto mt-[16px]" :src="`${BASE_URL}/no-cache/order-detail.png`" mode="widthFix" alt=""
          srcset=""> -->
      </div>
    </div>

    <div v-if="planDetail" className="fixed bottom-0 bg-[#fff] px-[16px] w-full flex dynamic-height">
      <div className="h-[56px] w-full flex justify-between items-center">
        <div className="flex items-center gap-[3px]">
          <span className="text-[12px] text-red-500 font-[600]">
            ¥
          </span>

          <span className="text-[17px] text-red-500 font-[600]">0.</span>

          <span className="text-[13px] text-red-500 font-[600] relative top-[1px]">00</span>

          <div className="text-[#868F9C] text-[13px] line-through relative top-[2px] left-[3px]">
            {{ planDetail.price }}.00
          </div>
        </div>

        <nut-button v-if="!isPay" color="#00AC97" @click="handlePay">
          立即下单
        </nut-button>

        <div v-else class="text-gray-500 text-[12px]">
          订单已完成
        </div>
      </div>
    </div>
  </div>
</template>

<style>
.dynamic-height {
  height: calc(56px + constant(safe-area-inset-bottom)) !important;
  height: calc(56px + env(safe-area-inset-bottom)) !important;
}

.dynamic-mb {
  margin-bottom: calc(56px + constant(safe-area-inset-bottom)) !important;
  margin-bottom: calc(56px + env(safe-area-inset-bottom)) !important;
}

.nut-tabs .nut-tabs__titles {
  background-color: #fff;
  border-radius: 10px 10px 0 0;
  border-bottom: 1px solid #efeeee;
}
</style>
