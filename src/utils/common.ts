import { base64Decode } from './base64'

export function decodeMessage(message: string) {
  return JSON.parse(decodeURIComponent(base64Decode(message)))
}

export function isNanJing(state: string) {
  try {
    const decoded = JSON.parse(base64Decode(state))
    console.log(decoded)
    return decoded.inviteId === '5200'
  }
  catch {
    return false
  }
}

export function parsePlanId(state: string) {
  try {
    const decoded = JSON.parse(base64Decode(state))
    return decoded.planId
  }
  catch {
    return null
  }
}
