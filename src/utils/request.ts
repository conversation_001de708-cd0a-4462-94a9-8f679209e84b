import Taro from '@tarojs/taro'
import { BASE_URL, getBaseUrl } from './mode'

// 请求配置接口
interface RequestConfig {
  url: string
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH'
  data?: any
  header?: Record<string, string>
  timeout?: number
  showLoading?: boolean
  loadingText?: string
}

// 响应接口
interface Response<T = any> {
  data: T
  statusCode: number
  header: any
  cookies?: string[]
}

// 错误类型
interface RequestError {
  message: string
  statusCode?: number
  data?: any
}

class HttpRequest {
  private baseURL: string = ''
  private defaultTimeout: number = 10000
  private defaultLoadingText: string = '加载中...'

  constructor(config?: { baseURL?: string, timeout?: number }) {
    // 使用 mode.ts 中的 getBaseUrl() 方法设置默认 baseURL
    this.baseURL = config?.baseURL || getBaseUrl()
    if (config?.timeout) {
      this.defaultTimeout = config.timeout
    }
  }

  /**
   * 获取 token
   */
  private getToken(): string {
    try {
      return Taro.getStorageSync('token') || ''
    }
    catch (error) {
      console.error('获取 token 失败:', error)
      return ''
    }
  }

  /**
   * 构建请求头
   */
  private buildHeaders(customHeaders?: Record<string, string>): Record<string, string> {
    const token = this.getToken()
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...customHeaders,
    }

    if (token) {
      headers.Authorization = token
    }

    return headers
  }

  /**
   * 处理响应错误
   */
  private handleResponseError(response: any): RequestError | null {
    const { statusCode, data } = response

    console.log({
      statusCode,
    })

    // HTTP 状态码错误处理
    if (statusCode >= 400) {
      let message = '请求失败'

      switch (statusCode) {
        case 400:
          message = '请求参数错误'
          break
        case 401:
          message = '未授权，请重新登录'
          // 清除 token 并跳转到登录页
          this.handleUnauthorized()
          break
        case 403:
          message = '拒绝访问'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        case 502:
          message = '网关错误'
          break
        case 503:
          message = '服务不可用'
          break
        default:
          message = `请求失败 (${statusCode})`
      }

      return {
        message,
        statusCode,
        data,
      }
    }

    // 业务状态码错误处理
    if (data && data.state !== undefined && data.state !== 200) {
      return {
        message: data.message || '业务处理失败',
        statusCode,
        data,
      }
    }

    // 响应成功，返回 null
    return null
  }

  /**
   * 处理未授权情况
   */
  private handleUnauthorized(): void {
    try {
      // 清除 token
      Taro.removeStorageSync('token')

      // 显示提示
      Taro.showToast({
        title: '登录已过期，请重新登录',
        icon: 'none',
        duration: 2000,
      })

      const logoutUrl = encodeURIComponent(`${BASE_URL}/logout`)
      // 延迟跳转到登录页
      setTimeout(() => {
        Taro.redirectTo({
          url: `/pages/webview/index?url=${logoutUrl}`,
        })
      }, 2000)
    }
    catch (error) {
      console.error('处理未授权错误失败:', error)
    }
  }

  /**
   * 显示加载提示
   */
  private showLoading(text?: string): void {
    Taro.showLoading({
      title: text || this.defaultLoadingText,
      mask: true,
    })
  }

  /**
   * 隐藏加载提示
   */
  private hideLoading(): void {
    Taro.hideLoading()
  }

  /**
   * 核心请求方法
   */
  private async request<T = any>(config: RequestConfig): Promise<Response<T>> {
    const {
      url,
      method = 'GET',
      data,
      header,
      timeout = this.defaultTimeout,
      showLoading = false,
      loadingText,
    } = config

    // 显示加载提示
    if (showLoading) {
      this.showLoading(loadingText)
    }

    try {
      const fullUrl = url.startsWith('http') ? url : `${this.baseURL}${url}`
      const headers = this.buildHeaders(header)

      const response = await Taro.request({
        url: fullUrl,
        method,
        data,
        header: headers,
        timeout,
      })

      // 隐藏加载提示
      if (showLoading) {
        this.hideLoading()
      }

      // 检查响应状态
      const error = this.handleResponseError(response)
      if (error) {
        throw new Error(error.message)
      }

      return response
    }
    catch (error: any) {
      // 隐藏加载提示
      if (showLoading) {
        this.hideLoading()
      }

      // 网络错误处理
      if (error.errMsg) {
        let message = '网络请求失败'

        if (error.errMsg.includes('timeout')) {
          message = '请求超时，请检查网络连接'
        }
        else if (error.errMsg.includes('fail')) {
          message = '网络连接失败，请检查网络设置'
        }

        throw new Error(message)
      }

      // 其他错误直接抛出
      throw error
    }
  }

  /**
   * GET 请求
   */
  async get<T = any>(url: string, config?: Omit<RequestConfig, 'url' | 'method'>): Promise<Response<T>> {
    return this.request<T>({ url, method: 'GET', ...config })
  }

  /**
   * POST 请求
   */
  async post<T = any>(url: string, data?: any, config?: Omit<RequestConfig, 'url' | 'method' | 'data'>): Promise<Response<T>> {
    return this.request<T>({ url, method: 'POST', data, ...config })
  }

  /**
   * PUT 请求
   */
  async put<T = any>(url: string, data?: any, config?: Omit<RequestConfig, 'url' | 'method' | 'data'>): Promise<Response<T>> {
    return this.request<T>({ url, method: 'PUT', data, ...config })
  }

  /**
   * DELETE 请求
   */
  async delete<T = any>(url: string, config?: Omit<RequestConfig, 'url' | 'method'>): Promise<Response<T>> {
    return this.request<T>({ url, method: 'DELETE', ...config })
  }

  /**
   * PATCH 请求
   */
  async patch<T = any>(url: string, data?: any, config?: Omit<RequestConfig, 'url' | 'method' | 'data'>): Promise<Response<T>> {
    return this.request<T>({ url, method: 'PATCH', data, ...config })
  }
}

// 创建默认实例
const http = new HttpRequest()

// 导出实例和类
export default http
export { HttpRequest }
export type { RequestConfig, RequestError, Response }

/*
使用示例：

// 1. 基本使用
import http from '@/utils/request'

// GET 请求
const response = await http.get('/api/users')
console.log(response.data)

// POST 请求
const result = await http.post('/api/users', { name: '张三', age: 25 })
console.log(result.data)

// 2. 带配置的请求
const response = await http.get('/api/data', {
  showLoading: true,
  loadingText: '加载数据中...',
  timeout: 15000,
  header: {
    'Custom-Header': 'value'
  }
})

// 3. 错误处理
try {
  const response = await http.post('/api/login', { username: 'user', password: 'pass' })
  console.log('登录成功:', response.data)
} catch (error) {
  console.error('登录失败:', error.message)
}

// 4. 创建自定义实例
import { HttpRequest } from '@/utils/request'

const customHttp = new HttpRequest({
  baseURL: 'https://api.example.com',
  timeout: 20000
})

// 5. 在组件中使用
// 在 Vue 组件中
const fetchData = async () => {
  try {
    const response = await http.get('/api/plan-detail.json', {
      showLoading: true,
      loadingText: '获取计划详情...'
    })
    priceDetail.value = response.data
  } catch (error) {
    Taro.showToast({
      title: error.message,
      icon: 'error'
    })
  }
}
*/
