export function parseTaroParams(params: Partial<Record<string, string>>) {
  try {
    const { q } = params

    let state = ''
    let doctorId = ''
    let planId = ''

    if (q) {
      const url = new URL(decodeURIComponent(q))
      const params2 = new URLSearchParams(url.search)
      state = params2.get('state') || ''
      doctorId = params2.get('doctorId') || ''
      planId = params2.get('planId') || ''
    }
    else {
      state = params.state || ''
      doctorId = params.doctorId || ''
      planId = params.planId || ''
    }

    return {
      state,
      doctorId,
      planId,
    }
  }
  catch (error) {
    console.error(error)
    return {
      state: params.state || '',
      doctorId: params.doctorId || '',
      planId: params.planId || '',
    }
  }
}
