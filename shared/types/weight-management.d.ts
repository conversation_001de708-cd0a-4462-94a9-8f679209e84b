declare type DietaryPlanType = '高蛋白' | '低碳水化合物膳食' | '低GI' | '低脂' | 'DASH' | '地中海' | '江南'

declare type SportsAbilityLevel = '初级' | '中级' | '高级'

declare interface DecodedResults {
    role?: string
    inviteId?: string
    noId?: string
    noPay?: string
    planId?: string
}

declare interface PhysicalCondition {
    waistCircumference: number
    bodyFatRate: number
    visceralFatArea: number
}

declare interface ParsedResult {
    name: string
    age: number
    alcohol: string
    alcohol_freq: string
    alcohol_amt: string
    bmi: number
    fat_rate: number
    bowel_sounds: string
    conditions: string[]
    cigarettes: string
    constipation: string
    diarrhea: string
    diet_behaviors: string[]
    family_diseases: string[]
    flatulence: string
    gender: string
    height: number
    intestine: string[]
    late_nights: string
    medications: string[]
    pain: string
    physical_activity: string
    sitting_time: string
    sleep_time: string
    smoking: string
    sports: string[]
    waist: number
    water: string
    weight: number
    workout_duration: string
    workout_freq: string
}

declare interface DietRecommendationResponse {
    userId: string
    dietRecommendation: {
        recommendedPattern: {
            name: string
            reason: string
        }
        notRecommendedPatterns: Array<{
            name: string
            reason: string
        }>
    }
    nutritionGuide: {
        calorie: number
        unit: string
        nutrients: Array<{
            name: string
            value: number
            unit: string
        }>
    }
    sampleMealPlan: {
        breakfast: {
            name: string
            calories: number
            description: string
        }
        lunch: {
            name: string
            calories: number
            description: string
        }
        dinner: {
            name: string
            calories: number
            description: string
        }
        snack: {
            name: string
            calories: number
            description: string
        }
    }
    additionalNotes: string[]
}

declare interface SportsAbilityRatingResponse {
    iz_deleted: number
    createTime: string
    updateTime: string
    id: number
    questionId: number
    customerId: string
    projectId: number
    questionTitle: string
    questionResult: string
    sportsAbilityRating: string
}

declare interface DietPlan {
    kcal: number
    dietMode: string
    dietDetail: string
}

declare interface HealthProgramIndex {
    weightManagementQuestionnaire: string
    dietPlan: DietPlan
    sportRating: string
    physicalCondition: PhysicalCondition
    pes?: string
    evaluation_suggestions?: string
    qwenReportEnd?: number
    readStatus?: number
}

declare interface BaseResponse<T> {
    state: number
    results: T
}
