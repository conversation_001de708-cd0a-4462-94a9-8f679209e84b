interface FoodItem {
    uuid: string
    name: string
    weight: number
    calories: number
    foodType: string
    fat: number
    rawMaterial?: string[]
    carbohydrates: number
    protein: number
    dietaryFiber: number
    mealPicture: string
    source: 'photo' | 'recommend' | 'lib'
}

interface CheckinFoodRoot {
    id: number | null
    kind: DietType
    mealContent: CheckinFood
    checkInDate: string
    status: 0 | 1
    calorie: number
}

interface CheckinFood {
    kind: 'recommend' | 'picture' | null
    pictures: string[]
    items: FoodItem[]
    mealEvaluationList: string[]
}
