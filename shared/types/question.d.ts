interface QuestionMeta {
    id: number
    coverPicture: string
    title: string
    content: string
    shareNumber: number
    writeNumber: number
    dealNumber: number
    relatedProjects: string
    questionResultId: number
    status: 'DRAFT' | 'PUBLISHED'
}

interface QuestionList {
    content: string
    createTime: string
    customerId: string
    id: number
    projectId: number
    questionId: number
    questionResultId: string
    questionTitle: string
    title: string
    reportInterpretationId: number
    questionCoverPicture: string
    coverPicture: string
}

interface QuestionResult {
    createTime: string
    customerId: string
    id: number
    projectId: number
    projectName: string
    questionCoverPicture: string
    questionId: number
    questionResult: string
    questionTitle: string
    reportInterpretationId: number
}

type QuestionType = 'PRELIMINARY_EVALUATION' | 'SECONDARY_EVALUATION'
