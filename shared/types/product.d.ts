interface Product {
    projectProductId: number
    number: number
    price: string
    projectProductName: string
    describeContext: string
    pictureId: string
}

interface NutritionProgram {
    nutritionProgramId: number
    customerId: string
    questionId: number
    projectName: string
    createTime: number
    projectId: number
    productContent: Product[]
    price: number
    accountManagerId: string
    status: string
}
