interface CustomerIndex {
    waterIndex: string | null
    aerobicTimeIndex: string | null
    footIndex: string | null
    sportKcalIndex: string | null
    weightIndex: string | null
    foodKcalIndex: number | null
    stepCalorieEnabled: boolean | null
}

type DietType = 'breakfast' | 'lunch' | 'dinner' | 'snack'

type ExerciseType = 'aerobic' | 'anaerobic'

type CheckInType =
    | 'diet'
    | 'exercise'
    | 'weight'
    | 'water'
    | 'nutrition'
    | 'medicine'
    | 'meal'

interface CheckInCard {
    id: number
    cardTitle: string
    status: 0 | 1
    path: string
    icon: string
    cardName: string
    value: number | string
    sort: number
}

interface CheckInCardData {
    key: string
    component: Component
    value: number | string[]
    showText?: boolean
    leftText: (v: number) => string
    rightText: string
    color: string
    onClick: () => void
}

interface ExerciseData {
    steps: number
    checkInDate: string
}

// 食物详情接口
interface FoodDetail {
    食物名: string
    原料名: string | { [key: string]: string } // 可以是字符串或对象
    碳水化合物含量: string
    脂肪含量: string
    膳食纤维含量: string
    蛋白质含量: string
    熟重?: string
    每百克卡路里?: string
    realCalorie?: string
}

// 食谱组接口
interface RecipeGroup {
    recipe_details: string
    recipe_details_value: FoodDetail[]
    type: DietType
}

// 主接口
interface DietPlan {
    breakfast_values: RecipeGroup[]
    lunch_values: RecipeGroup[]
    dinner_values: RecipeGroup[]
    snack_values: RecipeGroup[]
}

interface RecommendMeal {
    breakfast: RecipeGroup[]
    lunch: RecipeGroup[]
    dinner: RecipeGroup[]
    snack: RecipeGroup[]
}

// interface CheckinFoodRoot {
//     id: number | null
//     kind: DietType
//     mealContent: CheckinFood
//     checkInDate: string
//     status: 0 | 1
//     calorie: number
// }

// interface CheckinFood {
//     kind: 'recommend' | 'picture' | null
//     items: kind extends 'picture'
//         ? CheckinFoodItem[]
//         : kind extends 'recommend'
//             ? RecipeGroup
//             : never
// }

interface CheckinFoodItem {
    uuid: string
    mealPicture: string
    foodName: string
    weight: number
    unit: string
    caloriePer100g: string
    realCalorie: string
}

interface CheckInCustomerData {
    data: {
        checkInCount: number
        days: CheckinDataDays[]
        start: string
    }
    starCount: number
}

interface CheckinDataDays {
    checkInCount: number
    day: string
    nutrition: number
    sport: number
    waterIntake: number
    weight: number
    mealCount: string[]
    waistCircumference: number
    sportCalorie: number
}

interface NutritionCheckinData {
    id: number | null
    checkInDate: string
    probioticsPictures: NutritionPicture[]
    prebioticsPictures: NutritionPicture[]
}

interface NutritionPicture {
    id: string | null
    pictureUrl: string
    createdAt: string
}

interface Sport {
    sportType: string
    subList: SubSport[]
    count: number | undefined
}

interface SubSport {
    name: string
    burnCalories: number
    id: number
    note: string
    met: string
    strengthGrade: string
    type: string
}

interface CheckInToolsItem {
    key: string
    title: string
    value: number | string | string[]
    unit: string
    icon: string
    path: string
    decimalPlaces?: number
    showQuick?: boolean
    lastUpdateTime?: string
    customSubTitle?: string
    id?: number
    sort?: number
    status?: 0 | 1
    customCard?: boolean
}

interface PlanData {
    id: number
    dietaryPatternsName: string
    planName: string
    targetWeight: string
    totalDay: number
    weightWhenSendPlan: string
    createTime: string
    initialWeight: number
    registrationTime: string
}

interface NewCheckinData {
    date: string
    details: NewCheckinDataDetail[]
}

interface NewCheckinDataDetail {
    kind: string
    result: number
    updateTime: string
}

interface CheckInCustomerMeal {
    kindList: DietType[] | null
    updateTime: string | null
}

interface DiaryContent {
    content: string
    timestamp?: string
}

interface DiaryDetail {
    iz_deleted: number
    createTime: string
    updateTime: string
    id: number
    customerId: string
    weight: number
    recordTime: string
    content: string
    pictures: string
}
