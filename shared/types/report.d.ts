interface ReportInterpretation {
    accountManagerId: string
    content: string
    createTime: string
    customerId: string
    customerName: string
    interpretationTime: string
    questionCoverPicture: string
    pdfUrl: string
    projectId: number
    projectName: string
    questionId: number
    questionResultId: number
    questionTitle: string
    reportInterpretationId: number
    status: string
}

interface EvaluateResult {
    aiRecommendations: string
    aiReview: string
    bmi: string
    riskLevelOfFattyLiver: string
}

interface EvaluateQuestion {
    preliminaryEvaluationQuestionId: number
    preliminaryEvaluationQuestionResultId: number
    secondaryEvaluationQuestionId: number
    secondaryEvaluationQuestionResultId: number
}
