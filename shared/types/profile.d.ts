interface ProfileForm {
    height: number
    weight: number
    historyOfIllness: string[]
    drugHistory: string[]
    allergyHistory: string[]
    familyHistory: string[]
    label: number[]
}

type Duties = 'doctor' | 'nurse' | 'user-manager' | (string & {})

interface HostProfileForm {
    headImage: string
    nickName: string
    name: string
    phone?: string
    idCard: string
    duties: Duties | undefined
    teamId: number | null
    gender?: string
    age?: number
    operationType?: string
}

interface User {
    id: number
    customerId: string
    name: string
    nickname: string
    headImgUrl: string
    idCard: string
    phone: string
    registerTime: string
    archiveContent?: string
}

interface UserResponse {
    name: string
    phone: string
    role: 'user' | 'manager'
    idCard: string
    duties: Duties
    status: 'init' | 'login'
    questionIsFinished: 0 | 1
    questionId: number
    token: string
    wxOAuth2UserInfo: any
    teamId: number | null
    sex?: string
    age?: number
    state?: string
    operationType?: string
}
