{"name": "nuxt-app", "type": "module", "version": "1.0.3", "private": true, "packageManager": "pnpm@10.13.1", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "typecheck": "vue-tsc --noEmit", "check": "pnpm lint && pnpm typecheck", "postinstall": "nuxt prepare", "start": "node .output/server/index.mjs", "lint": "eslint .", "release": "bumpp"}, "dependencies": {"driver.js": "^1.3.6", "slimeform": "^0.10.0", "vue-virtual-scroller": "2.0.0-beta.8"}, "devDependencies": {"@iconify-json/radix-icons": "^1.2.2", "@nuxt/eslint": "^1.7.1", "@pinia/nuxt": "^0.11.2", "@sanomics/eslint-config": "^1.2.0", "@sanomics/survey": "^0.1.6", "@types/lodash-es": "^4.17.12", "@types/postcss-pxtorem": "^6.1.0", "@unocss/nuxt": "^66.3.3", "@unocss/reset": "^66.3.3", "@unocss/transformer-directives": "^66.3.3", "@vant/area-data": "^2.0.0", "@vant/nuxt": "^1.0.7", "@vueuse/components": "^13.6.0", "@vueuse/core": "^13.6.0", "@vueuse/motion": "^3.0.3", "@vueuse/nuxt": "^13.6.0", "@vueuse/router": "^13.6.0", "amfe-flexible": "^2.2.1", "autoprefixer": "^10.4.21", "chart.js": "^4.5.0", "compressorjs": "^1.2.1", "dayjs-nuxt": "^2.1.11", "destr": "^2.0.5", "eslint": "^9.32.0", "fast-glob": "^3.3.3", "idcard": "^4.2.0", "lodash-es": "^4.17.21", "nuxt": "^4.0.2", "nuxt-module-cli-shortcuts": "^0.0.4", "openai": "^5.11.0", "pinia": "^3.0.3", "pinia-plugin-persistedstate": "^4.4.1", "postcss-pxtorem": "^6.1.0", "sass": "^1.89.2", "spark-md5": "^3.0.2", "survey-core": "1.12.6", "swiper": "^11.2.10", "taze": "^19.1.0", "ufo": "^1.6.1", "unocss": "^66.3.3", "unocss-preset-scrollbar-hide": "^1.0.1", "unplugin-info": "^1.2.4", "unplugin-turbo-console": "^2.2.0", "uuid": "^11.1.0", "vant": "^4.9.21", "vconsole": "^3.15.1", "vue-countup-v3": "^1.4.2", "vue-draggable-plus": "^0.6.0", "vue-pdf-embed": "^2.1.3", "vue-tsc": "^3.0.4", "vue-wrap-balancer": "^1.2.1", "weixin-js-sdk": "^1.6.5"}}