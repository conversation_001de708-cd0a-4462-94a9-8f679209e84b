{"name": "slmc-mini", "version": "1.0.0", "private": true, "description": "健管家小程序", "templateInfo": {"name": "vue3-NutUI", "typescript": true, "css": "Sass", "framework": "Vue3"}, "author": "", "license": "MIT", "scripts": {"dev": "npm run build:weapp-test -- --watch", "dev:prod": "npm run build:weapp-prod -- --watch", "build:weapp-prod": "taro build --type weapp --mode production", "build:weapp-test": "taro build --type weapp --mode development", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:quickapp": "taro build --type quickapp", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "postinstall": "weapp-tw patch"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "dependencies": {"@babel/runtime": "^7.7.7", "@nutui/icons-vue-taro": "^0.0.9", "@nutui/nutui-taro": "^4.2.8", "@tarojs/components": "4.0.5", "@tarojs/helper": "4.0.5", "@tarojs/plugin-framework-vue3": "4.0.5", "@tarojs/plugin-html": "4.0.5", "@tarojs/plugin-platform-alipay": "4.0.5", "@tarojs/plugin-platform-h5": "4.0.5", "@tarojs/plugin-platform-jd": "4.0.5", "@tarojs/plugin-platform-qq": "4.0.5", "@tarojs/plugin-platform-swan": "4.0.5", "@tarojs/plugin-platform-tt": "4.0.5", "@tarojs/plugin-platform-weapp": "4.0.5", "@tarojs/runtime": "4.0.5", "@tarojs/shared": "4.0.5", "@tarojs/taro": "4.0.5", "vue": "^3.3.0"}, "devDependencies": {"@antfu/eslint-config": "^3.8.0", "@babel/core": "^7.8.0", "@babel/plugin-proposal-class-properties": "^7.18.6", "@nutui/auto-import-resolver": "^1.0.0", "@tarojs/cli": "4.0.5", "@tarojs/taro-loader": "4.0.5", "@tarojs/webpack5-runner": "4.0.5", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "@vue/babel-plugin-jsx": "^1.0.6", "@vue/compiler-sfc": "^3.0.0", "autoprefixer": "^10.4.20", "babel-preset-taro": "4.0.5", "css-loader": "3.4.2", "eslint": "^9.14.0", "postcss": "^8.4.47", "postcss-rem-to-responsive-pixel": "^6.0.2", "style-loader": "1.3.0", "stylelint": "^14.4.0", "tailwind-scrollbar-hide": "^1.1.7", "tailwindcss": "^3.4.13", "terser-webpack-plugin": "^5.3.11", "tsconfig-paths-webpack-plugin": "^4.0.1", "typescript": "^5.1.0", "unplugin-vue-components": "^0.26.0", "vue-loader": "^17.0.0", "weapp-tailwindcss": "^3.5.2", "webpack": "5.78.0"}}