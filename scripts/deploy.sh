#!/bin/bash

REMOTE_IMAGE_NAME=${DOCKER_REGISTRY}/repo_public/${IMAGE_NAME}

docker build \
  --build-arg NUXT_PUBLIC_APP_ID=$NUXT_PUBLIC_APP_ID \
  --build-arg NUXT_PUBLIC_TAG=$NUXT_PUBLIC_TAG \
  -t ${IMAGE_NAME}:${TAG} .
docker tag ${IMAGE_NAME}:${TAG} ${REMOTE_IMAGE_NAME}:${TAG}
docker login --username=1111和涛 -p l670819. ${DOCKER_REGISTRY}
docker push ${REMOTE_IMAGE_NAME}:${TAG}
echo "docker image pushed: ${REMOTE_IMAGE_NAME}:${TAG}"

if [ -n "$K8S_DEPLOY_URL" ]; then
  echo "deploy to k8s"
  curl \
    --insecure \
    --location \
    --request PATCH "$K8S_DEPLOY_URL" \
    --header "Authorization: Bearer $K8S_TOKEN" \
    --header "Content-Type: application/json-patch+json" \
    --data-raw "[
        {
            \"op\": \"replace\",
            \"path\": \"/spec/template/spec/containers/0/image\",
            \"value\": \"registry.cn-hangzhou.aliyuncs.com/repo_public/${IMAGE_NAME}:${TAG}\"
        }
    ]"
fi
